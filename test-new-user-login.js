// Test script to verify the newly created user can log in immediately
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testNewUserLogin() {
  console.log('🧪 Testing login for newly created user...');
  
  const testCredentials = {
    email: '<EMAIL>',
    password: 'newpassword123'
  };
  
  try {
    // Attempt to sign in with the newly created user
    console.log(`🔐 Attempting to sign in as: ${testCredentials.email}`);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testCredentials.email,
      password: testCredentials.password
    });
    
    if (error) {
      console.error('❌ Login failed:', error.message);
      return false;
    }
    
    if (data.user) {
      console.log('✅ Login successful!');
      console.log('📋 User details:');
      console.log(`   - ID: ${data.user.id}`);
      console.log(`   - Email: ${data.user.email}`);
      console.log(`   - Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
      console.log(`   - Created: ${data.user.created_at}`);
      console.log(`   - Metadata:`, data.user.user_metadata);
      
      // Sign out after successful test
      await supabase.auth.signOut();
      console.log('🚪 Signed out successfully');
      
      return true;
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during login test:', error);
    return false;
  }
}

// Run the test
testNewUserLogin()
  .then(success => {
    if (success) {
      console.log('\n🎉 AUTHENTICATION FIX VERIFIED!');
      console.log('✅ Admin-created accounts can now log in immediately without email confirmation');
    } else {
      console.log('\n❌ Authentication test failed');
      console.log('🔧 Further investigation needed');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test script error:', error);
    process.exit(1);
  });
