# YalaOffice .gitignore
# Developer: Alindevx00x (https://github.com/alindevx00x)

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
dist-ssr/
.vite/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Logs
logs/
*.log

# IDE and Editor files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cache directories
.cache/
.temp/
.npm/
.eslintcache
.stylelintcache

# Coverage and testing
coverage/
.nyc_output/
.pytest_cache/

# TypeScript
*.tsbuildinfo

# Supabase
.supabase/

# Deployment
.vercel/
.netlify/

# OS files
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
desktop.ini
$RECYCLE.BIN/

# Sensitive files
*.key
*.pem
secrets/

# Keep important files
!README.md
!package.json
!database-schema-final.sql
!docs/
!scripts/
