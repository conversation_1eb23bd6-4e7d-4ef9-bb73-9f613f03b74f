# YalaOffice Production Environment Configuration
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice

# =============================================
# SUPABASE CONFIGURATION
# =============================================
# Get these values from your Supabase project dashboard
# https://app.supabase.com/project/YOUR_PROJECT/settings/api

VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# =============================================
# APPLICATION CONFIGURATION
# =============================================

VITE_APP_NAME=YalaOffice
VITE_APP_VERSION=2.0.0
VITE_APP_ENV=production

# Your domain name (without https://)
VITE_APP_DOMAIN=yalaoffice.com

# Application title and description
VITE_APP_TITLE=YalaOffice - Smart Supply Management
VITE_APP_DESCRIPTION=Professional supply management system for Moroccan businesses

# =============================================
# API CONFIGURATION
# =============================================

# Supabase REST API URL (usually same as SUPABASE_URL + /rest/v1)
VITE_API_URL=https://your-project-ref.supabase.co/rest/v1

# API timeout in milliseconds
VITE_API_TIMEOUT=30000

# =============================================
# SECURITY CONFIGURATION
# =============================================

# Enable/disable debug mode (set to false in production)
VITE_DEBUG_MODE=false

# Session timeout in minutes
VITE_SESSION_TIMEOUT=480

# Enable/disable analytics
VITE_ANALYTICS_ENABLED=true

# =============================================
# FEATURE FLAGS
# =============================================

# Enable/disable specific features
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_EXPORT=true

# =============================================
# LOCALIZATION
# =============================================

# Default language and locale
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_LOCALE=en-US
VITE_DEFAULT_TIMEZONE=Africa/Casablanca

# Currency settings
VITE_DEFAULT_CURRENCY=MAD
VITE_CURRENCY_SYMBOL=Dh

# =============================================
# PERFORMANCE CONFIGURATION
# =============================================

# Pagination settings
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# File upload limits (in MB)
VITE_MAX_FILE_SIZE=10
VITE_MAX_IMAGE_SIZE=5

# Cache settings
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# =============================================
# MONITORING & LOGGING
# =============================================

# Enable/disable error reporting
VITE_ERROR_REPORTING=true

# Log level (error, warn, info, debug)
VITE_LOG_LEVEL=error

# =============================================
# THIRD-PARTY INTEGRATIONS
# =============================================

# Google Maps API (if needed)
# VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Email service configuration (if using external service)
# VITE_EMAIL_SERVICE_URL=https://your-email-service.com/api

# =============================================
# DEVELOPMENT OVERRIDES
# =============================================
# These are only used in development mode

# VITE_DEV_SERVER_PORT=5173
# VITE_DEV_SERVER_HOST=localhost

# =============================================
# INSTRUCTIONS
# =============================================
#
# 1. Copy this file to .env in your project root
# 2. Replace all placeholder values with your actual configuration
# 3. Never commit the .env file to version control
# 4. Keep your Supabase keys secure and never share them
#
# Required values to change:
# - VITE_SUPABASE_URL
# - VITE_SUPABASE_ANON_KEY
# - VITE_APP_DOMAIN
# - VITE_API_URL
#
# Optional values to customize:
# - All other settings based on your needs
#
# For Supabase setup:
# 1. Go to https://supabase.com
# 2. Create a new project
# 3. Go to Settings > API
# 4. Copy the URL and anon key
# 5. Update the values above
#
# For domain setup:
# 1. Point your domain to your Linode server IP
# 2. Update VITE_APP_DOMAIN with your actual domain
# 3. Setup SSL certificate with Let's Encrypt
#
# =============================================
