#!/bin/bash

# YalaOffice Vercel Deployment Diagnostic Script
# This script helps diagnose deployment issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${BLUE}"
echo "=================================================="
echo "  YalaOffice Vercel Deployment Diagnostics"
echo "=================================================="
echo -e "${NC}"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI is not installed!"
    print_info "Install it with: npm install -g vercel"
    exit 1
fi

print_status "Vercel CLI found"

# Check if user is logged in
if ! vercel whoami &> /dev/null; then
    print_error "You need to login to Vercel first"
    print_info "Run: vercel login"
    exit 1
fi

VERCEL_USER=$(vercel whoami)
print_status "Logged in as: $VERCEL_USER"

echo ""
print_info "Checking project configuration..."

# Check if vercel.json exists
if [ -f "vercel.json" ]; then
    print_status "vercel.json found"
    print_info "Vercel configuration:"
    cat vercel.json | jq '.' 2>/dev/null || cat vercel.json
else
    print_warning "vercel.json not found"
fi

echo ""
print_info "Checking environment variables..."

# List environment variables
print_info "Current environment variables in Vercel:"
vercel env ls

echo ""
print_info "Checking for required environment variables..."

# Check critical variables
REQUIRED_VARS=("VITE_SUPABASE_URL" "VITE_SUPABASE_ANON_KEY")

for var in "${REQUIRED_VARS[@]}"; do
    if vercel env ls | grep -q "$var"; then
        print_status "$var is set"
    else
        print_error "$var is missing!"
    fi
done

echo ""
print_info "Checking local build..."

# Test local build
if npm run build; then
    print_status "Local build successful"
    
    # Check if dist directory exists
    if [ -d "dist" ]; then
        print_status "dist directory created"
        
        # Check if index.html exists
        if [ -f "dist/index.html" ]; then
            print_status "index.html found in dist"
        else
            print_error "index.html not found in dist"
        fi
    else
        print_error "dist directory not created"
    fi
else
    print_error "Local build failed!"
    print_info "Fix local build issues before deploying"
fi

echo ""
print_info "Checking deployment status..."

# Get latest deployment info
DEPLOYMENT_INFO=$(vercel ls --scope="$VERCEL_USER" 2>/dev/null | head -n 5)
print_info "Recent deployments:"
echo "$DEPLOYMENT_INFO"

echo ""
print_info "Diagnostic complete!"

echo ""
print_warning "If you're still experiencing issues:"
echo "1. Ensure all required environment variables are set (run setup-vercel-env.sh)"
echo "2. Check that your local build works: npm run build && npm run preview"
echo "3. Redeploy with: vercel --prod"
echo "4. Check deployment logs in Vercel dashboard"
echo "5. Test the reset password URL: https://yalaoffice.com/reset-password"

echo ""
print_info "Common solutions:"
echo "• Missing environment variables → Run setup-vercel-env.sh"
echo "• Build failures → Check package.json and dependencies"
echo "• Routing issues → Verify vercel.json configuration"
echo "• 404 errors → Usually caused by missing environment variables"
