# Vercel Deployment Fix for Password Reset

## Issue
The password reset link `https://yalaoffice.com/reset-password` returns a 404 error because Vercel doesn't know how to handle client-side routing for React applications.

## Solution
I've created a `vercel.json` configuration file that will fix this issue.

## Files Created/Modified

### 1. `vercel.json` (NEW FILE)
This file configures Vercel to properly handle client-side routing:

```json
{
  "version": 2,
  "name": "yalaoffice",
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    },
    {
      "src": "/health",
      "dest": "/health"
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

## Deployment Steps

1. **Commit the new `vercel.json` file:**
   ```bash
   git add vercel.json
   git commit -m "Add Vercel configuration for client-side routing"
   ```

2. **Push to your repository:**
   ```bash
   git push origin main
   ```

3. **Redeploy on Vercel:**
   - Go to your Vercel dashboard
   - Find your YalaOffice project
   - Click "Redeploy" or push the changes to trigger automatic deployment

## What This Fixes

- ✅ `/reset-password` route will now work correctly
- ✅ All client-side routes will be handled by React Router
- ✅ Direct URL access to any route will work
- ✅ Browser refresh on any page will work
- ✅ Password reset emails will redirect correctly

## Verification

After deployment, test:

1. **Password Reset Flow:**
   - Go to https://yalaoffice.com
   - Click "Forgot Password"
   - Enter your email
   - Check email and click the reset link
   - Should now redirect to https://yalaoffice.com/reset-password correctly

2. **Direct URL Access:**
   - Try accessing https://yalaoffice.com/reset-password directly
   - Should load the reset password page instead of 404

## Current Supabase Configuration ✅

Your Supabase auth configuration is already correct:
- Site URL: `https://yalaoffice.com`
- Allowed URLs: `https://yalaoffice.com/**`
- Recovery email template: Properly configured

## Additional Security Headers

The `vercel.json` also includes security headers:
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

## Troubleshooting

If the issue persists after deployment:

1. **Clear browser cache** and try again
2. **Check Vercel deployment logs** for any build errors
3. **Verify the `vercel.json` file** was deployed correctly
4. **Test with incognito/private browsing** to avoid cache issues

## Alternative Solution (if needed)

If Vercel still has issues, you can also add this to your `vite.config.ts`:

```typescript
export default defineConfig({
  // ... existing config
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
  preview: {
    port: 3000,
    strictPort: true,
  },
  server: {
    port: 3001,
    strictPort: true,
    host: true,
    origin: "http://0.0.0.0:3001",
  },
});
```

But the `vercel.json` solution should be sufficient.
