// Test script to verify the authentication fix for admin-created users
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SERVICE_ROLE_KEY || !ANON_KEY) {
  console.error('Missing required environment variables');
  process.exit(1);
}

// Create admin client with service role key
const adminClient = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
    flowType: 'pkce'
  }
});

// Create regular client for testing login
const regularClient = createClient(SUPABASE_URL, ANON_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
    flowType: 'pkce'
  }
});

async function testAuthFix() {
  try {
    console.log('🔄 Testing authentication fix for admin-created users...');
    
    const testEmail = '<EMAIL>';
    const testPassword = 'TestAuth2024!';
    
    // Step 1: Create user in Supabase Auth using admin client
    console.log('📝 Step 1: Creating user in Supabase Auth...');
    const { data: authData, error: authError } = await adminClient.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true, // Bypass email confirmation
      user_metadata: {
        full_name: 'Test Auth User',
        user_type: 'client',
        admin_created: true
      }
    });

    if (authError) {
      console.error('❌ Auth user creation failed:', authError);
      return;
    }

    console.log('✅ Auth user created successfully:', authData.user.id);

    // Step 2: Create user in custom users table
    console.log('📝 Step 2: Creating user in custom users table...');
    const { data: userData, error: userError } = await adminClient
      .from('users')
      .insert({
        id: authData.user.id,
        email: testEmail,
        full_name: 'Test Auth User',
        user_type: 'client',
        city: 'Tetouan',
        is_active: true
      })
      .select()
      .single();

    if (userError) {
      console.error('❌ Custom user creation failed:', userError);
      // Clean up auth user
      await adminClient.auth.admin.deleteUser(authData.user.id);
      return;
    }

    console.log('✅ Custom user created successfully:', userData.id);

    // Step 3: Test login with regular client
    console.log('📝 Step 3: Testing login with regular client...');
    const { data: loginData, error: loginError } = await regularClient.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });

    if (loginError) {
      console.error('❌ Login failed:', loginError);
      console.log('🔍 This indicates the authentication fix is not working properly');
    } else {
      console.log('✅ Login successful!');
      console.log('🎉 Authentication fix is working correctly');
      console.log('User ID:', loginData.user.id);
      console.log('Email confirmed:', loginData.user.email_confirmed_at);
    }

    // Step 4: Clean up test user
    console.log('📝 Step 4: Cleaning up test user...');
    await adminClient.from('users').delete().eq('id', authData.user.id);
    await adminClient.auth.admin.deleteUser(authData.user.id);
    console.log('✅ Test user cleaned up');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testAuthFix();
