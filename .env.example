# YalaOffice Environment Variables Template
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local to version control

# =============================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================
VITE_SUPABASE_URL=your_supabase_project_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# =============================================
# APPLICATION CONFIGURATION
# =============================================
VITE_APP_NAME=YalaOffice
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="Smart Supply Management System for Moroccan Businesses"

# =============================================
# API CONFIGURATION
# =============================================
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# =============================================
# DEVELOPMENT SETTINGS
# =============================================
VITE_DEBUG=false
VITE_DEVTOOLS=true
VITE_MOCK_API=false

# =============================================
# SECURITY CONFIGURATION
# =============================================
# Session timeout in seconds (default: 24 hours)
VITE_SESSION_TIMEOUT=86400

# Maximum login attempts before lockout
VITE_MAX_LOGIN_ATTEMPTS=5

# Account lockout duration in minutes
VITE_LOCKOUT_DURATION=15

# =============================================
# FEATURE FLAGS
# =============================================
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_REPORTS=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_MOBILE_APP=true

# =============================================
# LOCALIZATION
# =============================================
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_CURRENCY=MAD
VITE_DEFAULT_TIMEZONE=Africa/Casablanca

# =============================================
# MONITORING & LOGGING
# =============================================
VITE_MONITORING_ENABLED=false
VITE_LOG_LEVEL=info

# =============================================
# PRODUCTION ONLY (Server-side)
# =============================================
# These variables are only used in production server environment
# Do not include VITE_ prefix for server-only variables

# Database Configuration
DATABASE_URL=************************************/yalaoffice
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
EMAIL_FROM=YalaOffice <<EMAIL>>

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
UPLOAD_PATH=/var/uploads/yalaoffice

# AWS Configuration (for production)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=eu-west-1
AWS_S3_BUCKET=yalaoffice-production

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=yalaoffice-backups
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# Monitoring Configuration
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true
XSS_PROTECTION=true
FRAME_OPTIONS=SAMEORIGIN

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=https://yalaoffice.com,https://www.yalaoffice.com
CORS_CREDENTIALS=true
