// <PERSON><PERSON><PERSON> to set a password for the testadmin user
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SERVICE_ROLE_KEY) {
  console.error('Missing required environment variables');
  process.exit(1);
}

// Create admin client with service role key
const adminClient = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
    flowType: 'pkce'
  }
});

async function setAdminPassword() {
  try {
    console.log('🔄 Creating new testadmin in auth.users...');

    const email = '<EMAIL>';
    const newPassword = 'TestAdmin2024!';

    // Create the user in auth.users table (let Supabase generate new ID)
    const { data: newAuthUser, error: createError } = await adminClient.auth.admin.createUser({
      email: email,
      password: newPassword,
      email_confirm: true, // Skip email confirmation
      user_metadata: {
        full_name: 'Test Admin',
        user_type: 'admin',
        admin_created: true
      }
    });

    if (createError) {
      console.error('❌ Error creating auth user:', createError);
      return;
    }

    console.log('✅ Admin user created successfully in auth.users');
    console.log('🆔 New User ID:', newAuthUser.user.id);
    console.log('📧 Email:', email);
    console.log('🔑 Password:', newPassword);

    // Update the custom users table with the new ID
    const oldUserId = '155af44f-9e1b-4116-b993-11c682989d6f';
    const { error: updateError } = await adminClient
      .from('users')
      .update({ id: newAuthUser.user.id })
      .eq('id', oldUserId);

    if (updateError) {
      console.error('❌ Error updating custom users table:', updateError);
    } else {
      console.log('✅ Custom users table updated with new ID');
    }

  } catch (error) {
    console.error('❌ Failed with error:', error);
  }
}

// Run the script
setAdminPassword();
