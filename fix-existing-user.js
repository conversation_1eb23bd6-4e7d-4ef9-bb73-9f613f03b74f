// Script to fix the existing testfixuser by creating them in auth.users
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SERVICE_ROLE_KEY) {
  console.error('Missing required environment variables');
  process.exit(1);
}

// Create admin client with service role key
const adminClient = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
    flowType: 'pkce'
  }
});

async function fixExistingUser() {
  try {
    console.log('🔄 Fixing existing testfixuser...');
    
    const userId = 'bc97400e-b92c-44fa-87f1-3857e43d6bef';
    const email = '<EMAIL>';
    const password = 'newpassword123';
    
    // Check if user exists in auth.users
    console.log('📝 Checking if user exists in auth.users...');
    const { data: existingAuthUser, error: getUserError } = await adminClient.auth.admin.getUserById(userId);
    
    if (getUserError && getUserError.message.includes('User not found')) {
      console.log('❌ User not found in auth.users, creating...');
      
      // Create the user in auth.users table
      const { data: newAuthUser, error: createError } = await adminClient.auth.admin.createUser({
        id: userId,
        email: email,
        password: password,
        email_confirm: true, // Skip email confirmation
        user_metadata: {
          full_name: 'Test Fix User',
          user_type: 'client',
          city: 'Tetouan',
          admin_created: true
        }
      });

      if (createError) {
        console.error('❌ Error creating auth user:', createError);
        return;
      }

      console.log('✅ Auth user created successfully:', newAuthUser.user.id);
    } else if (getUserError) {
      console.error('❌ Error checking auth user:', getUserError);
      return;
    } else {
      console.log('✅ User already exists in auth.users');
    }

    console.log('🎉 User should now be able to log in!');

  } catch (error) {
    console.error('❌ Fix failed with error:', error);
  }
}

// Run the fix
fixExistingUser();
