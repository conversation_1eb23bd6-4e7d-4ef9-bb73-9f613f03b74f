-- =============================================
-- YALAOFFICE PRODUCTION DATABASE SCHEMA
-- =============================================
-- Version: 2.0.0
-- Date: 2025-01-23
-- Developer: Alindevx00x (https://github.com/alindevx00x)
-- Description: Complete production-ready database schema for YalaOffice Supply Management System
-- Compatibility: PostgreSQL 14+ (Supabase)
-- Features: Multi-branch, Role-based access, Inventory management, Order processing,
--          Customer management, Analytics, Integrations, Audit trails, Security
--
-- Copyright (c) 2025 YalaOffice - Developed by Alindevx00x
-- GitHub: https://github.com/alindevx00x
--
-- This schema provides enterprise-grade database architecture for Moroccan businesses
-- with comprehensive security, performance optimization, and scalability features.

-- =============================================
-- EXTENSIONS AND PREREQUISITES
-- =============================================

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Set timezone for consistent timestamps
SET timezone = 'Africa/Casablanca';

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    year_suffix TEXT;
    sequence_num INTEGER;
    order_num TEXT;
BEGIN
    year_suffix := TO_CHAR(NOW(), 'YY');
    
    -- Get next sequence number for this year
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 4) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM orders
    WHERE order_number LIKE 'YO' || year_suffix || '%';
    
    order_num := 'YO' || year_suffix || LPAD(sequence_num::TEXT, 6, '0');
    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- CORE SYSTEM TABLES
-- =============================================

-- Users table with comprehensive role-based access control
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    user_type TEXT NOT NULL CHECK (user_type IN ('admin', 'manager', 'client', 'reseller', 'delivery_person')),
    phone TEXT,
    city TEXT,
    is_active BOOLEAN DEFAULT true,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'suspended')),
    
    -- Company information (for business users)
    is_company BOOLEAN DEFAULT false,
    company_name TEXT,
    ice_number TEXT, -- Morocco ICE number
    company_address TEXT,
    company_phone TEXT,
    company_city TEXT,
    company_email TEXT,
    tax_id TEXT,
    legal_form TEXT, -- SARL, SA, etc.
    
    -- Profile and preferences
    profile_picture_url TEXT,
    last_login TIMESTAMPTZ,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User profiles for extended user information
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    avatar_url TEXT,
    bio TEXT,
    job_title TEXT,
    language TEXT DEFAULT 'en',
    currency TEXT DEFAULT 'MAD',
    timezone TEXT DEFAULT 'Africa/Casablanca',
    notifications JSONB DEFAULT '{"email": true, "sms": true, "push": true, "orderUpdates": true, "promotions": true, "stockAlerts": true}',
    order_defaults JSONB DEFAULT '{"deliveryPreference": "standard"}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Branches/Locations for multi-branch support
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    address JSONB NOT NULL, -- {street, city, state, postal_code, country}
    contact JSONB NOT NULL, -- {phone, email, manager}
    coordinates JSONB, -- {latitude, longitude}
    is_active BOOLEAN DEFAULT true,
    is_main_branch BOOLEAN DEFAULT false,
    operating_hours JSONB, -- {monday: {open, close, is_closed}, ...}
    services TEXT[],
    capacity JSONB, -- {storage, staff}
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Product categories with hierarchical structure
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    level INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Products with comprehensive inventory tracking
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    sku TEXT UNIQUE NOT NULL,
    category_id UUID REFERENCES categories(id),
    brand TEXT,
    
    -- Pricing
    price DECIMAL(12,2) NOT NULL,
    reseller_price DECIMAL(12,2),
    cost_price DECIMAL(12,2), -- For profit calculations
    
    -- Media
    featured_image TEXT,
    thumbnail_images TEXT[],
    
    -- Reviews and ratings
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    
    -- Inventory
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reserved_stock INTEGER DEFAULT 0,
    
    -- Physical properties
    weight DECIMAL(8,3), -- in kg
    dimensions JSONB, -- {length, width, height} in cm
    
    -- Metadata
    tags TEXT[],
    is_active BOOLEAN DEFAULT true,
    is_new BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    
    -- SEO
    meta_title TEXT,
    meta_description TEXT,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- =============================================
-- ORDER MANAGEMENT SYSTEM
-- =============================================

-- Orders with comprehensive tracking
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number TEXT UNIQUE NOT NULL DEFAULT generate_order_number(),
    customer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    branch_id UUID REFERENCES branches(id),
    
    -- Status tracking
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped', 'delivered', 'cancelled', 'returned')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled')),
    payment_method TEXT CHECK (payment_method IN ('cash', 'bank_transfer', 'check', 'credit')),
    
    -- Delivery assignment (fixed data type consistency)
    assigned_delivery_person UUID REFERENCES users(id),
    assigned_delivery_person_id UUID REFERENCES users(id), -- Compatibility field
    delivery_person_name TEXT,
    delivery_assigned_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    delivery_status TEXT DEFAULT 'not_assigned' CHECK (delivery_status IN ('not_assigned', 'assigned', 'picked', 'out_for_delivery', 'delivered', 'failed', 'returned')),
    delivery_notes TEXT,
    
    -- Financial details
    subtotal DECIMAL(12,2) NOT NULL,
    delivery_fee DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total DECIMAL(12,2) NOT NULL,
    
    -- Addresses
    delivery_address JSONB,
    billing_address JSONB,
    
    -- Additional information
    promo_code TEXT,
    notes TEXT,
    estimated_delivery TIMESTAMPTZ,
    tracking_number TEXT,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Order items
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Order tracking/status history
CREATE TABLE order_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    location TEXT,
    notes TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES users(id)
);

-- =============================================
-- CUSTOMER MANAGEMENT
-- =============================================

-- Customer profiles (extends users table)
CREATE TABLE customer_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    discount_rate DECIMAL(5,2) DEFAULT 0, -- For resellers
    credit_limit DECIMAL(12,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0,
    last_order_date TIMESTAMPTZ,
    loyalty_points INTEGER DEFAULT 0,
    preferred_payment_method TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Customer wishlists
CREATE TABLE wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(customer_id, product_id)
);

-- Product reviews
CREATE TABLE product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    title TEXT,
    comment TEXT,
    status TEXT DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_reply TEXT,
    admin_reply_by UUID REFERENCES users(id),
    admin_reply_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INVENTORY MANAGEMENT
-- =============================================

-- Stock movements for audit trail
CREATE TABLE stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id),
    branch_id UUID REFERENCES branches(id),
    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
    quantity INTEGER NOT NULL,
    reference_type TEXT, -- 'order', 'purchase', 'adjustment', 'transfer'
    reference_id UUID,
    reason TEXT,
    cost_per_unit DECIMAL(12,2),
    total_cost DECIMAL(12,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Branch inventory
CREATE TABLE branch_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id UUID NOT NULL REFERENCES branches(id),
    product_id UUID NOT NULL REFERENCES products(id),
    stock INTEGER DEFAULT 0,
    reserved_stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    last_restocked TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(branch_id, product_id)
);

-- =============================================
-- PROMOTIONS AND PRICING
-- =============================================

-- Promo codes
CREATE TABLE promo_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(12,2),
    max_discount_amount DECIMAL(12,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    valid_from TIMESTAMPTZ,
    valid_until TIMESTAMPTZ,
    applicable_to JSONB, -- {user_types: [], categories: [], products: []}
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- =============================================
-- SYSTEM CONFIGURATION
-- =============================================

-- Company settings
CREATE TABLE company_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    email TEXT,
    tax_id TEXT,
    ice_number TEXT,
    logo_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- System configurations
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(category, key)
);

-- =============================================
-- NOTIFICATIONS AND COMMUNICATIONS
-- =============================================

-- System notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    is_read BOOLEAN DEFAULT false,
    action_url TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    read_at TIMESTAMPTZ
);

-- Email queue for reliable email delivery
CREATE TABLE email_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    to_email TEXT NOT NULL,
    from_email TEXT NOT NULL,
    from_name TEXT,
    subject TEXT NOT NULL,
    html_content TEXT,
    text_content TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
    smtp_config JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    sent_at TIMESTAMPTZ,
    attempts INTEGER DEFAULT 0,
    last_error TEXT,
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10)
);

-- =============================================
-- AUDIT AND SECURITY
-- =============================================

-- Comprehensive audit logs
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    entity_type TEXT NOT NULL,
    entity_id UUID,
    action TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    severity TEXT DEFAULT 'info' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Security events tracking
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL,
    user_id UUID REFERENCES users(id),
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ANALYTICS AND REPORTING
-- =============================================

-- Analytics events for tracking user behavior
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL,
    user_id UUID REFERENCES users(id),
    session_id TEXT,
    properties JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Customer behavior tracking
CREATE TABLE customer_behavior (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    product_id UUID REFERENCES products(id),
    category_id UUID REFERENCES categories(id),
    properties JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- PERFORMANCE INDEXES
-- =============================================

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_city ON users(city);
CREATE INDEX idx_users_created_at ON users(created_at);

-- User profiles indexes
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);

-- Products table indexes
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_products_is_featured ON products(is_featured);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_stock ON products(stock);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_title_search ON products USING gin(to_tsvector('english', title));

-- Orders table indexes
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_delivery_status ON orders(delivery_status);
CREATE INDEX idx_orders_assigned_delivery_person ON orders(assigned_delivery_person);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Order items indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Categories indexes
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_is_active ON categories(is_active);
CREATE INDEX idx_categories_level ON categories(level);
CREATE INDEX idx_categories_slug ON categories(slug);

-- Stock movements indexes
CREATE INDEX idx_stock_movements_product_id ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_branch_id ON stock_movements(branch_id);
CREATE INDEX idx_stock_movements_created_at ON stock_movements(created_at);
CREATE INDEX idx_stock_movements_reference ON stock_movements(reference_type, reference_id);

-- Branch inventory indexes
CREATE INDEX idx_branch_inventory_branch_product ON branch_inventory(branch_id, product_id);
CREATE INDEX idx_branch_inventory_product_id ON branch_inventory(product_id);

-- Reviews indexes
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_customer_id ON product_reviews(customer_id);
CREATE INDEX idx_product_reviews_status ON product_reviews(status);
CREATE INDEX idx_product_reviews_rating ON product_reviews(rating);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Email queue indexes
CREATE INDEX idx_email_queue_status ON email_queue(status);
CREATE INDEX idx_email_queue_created_at ON email_queue(created_at);
CREATE INDEX idx_email_queue_priority ON email_queue(priority);

-- Audit logs indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_entity_type ON audit_logs(entity_type);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_severity ON audit_logs(severity);

-- Security events indexes
CREATE INDEX idx_security_events_user_id ON security_events(user_id);
CREATE INDEX idx_security_events_event_type ON security_events(event_type);
CREATE INDEX idx_security_events_severity ON security_events(severity);
CREATE INDEX idx_security_events_created_at ON security_events(created_at);

-- Analytics indexes
CREATE INDEX idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_timestamp ON analytics_events(timestamp);

-- Customer behavior indexes
CREATE INDEX idx_customer_behavior_customer_id ON customer_behavior(customer_id);
CREATE INDEX idx_customer_behavior_event_type ON customer_behavior(event_type);
CREATE INDEX idx_customer_behavior_timestamp ON customer_behavior(timestamp);

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_profiles_updated_at BEFORE UPDATE ON customer_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_reviews_updated_at BEFORE UPDATE ON product_reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_promo_codes_updated_at BEFORE UPDATE ON promo_codes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_company_settings_updated_at BEFORE UPDATE ON company_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Delivery person synchronization trigger
CREATE OR REPLACE FUNCTION sync_delivery_person_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Keep both delivery person fields synchronized
    IF NEW.assigned_delivery_person IS NOT NULL THEN
        NEW.assigned_delivery_person_id := NEW.assigned_delivery_person;
        
        -- Update delivery person name
        SELECT full_name INTO NEW.delivery_person_name
        FROM users
        WHERE id = NEW.assigned_delivery_person;
        
        -- Set assignment timestamp if not already set
        IF OLD.assigned_delivery_person IS NULL THEN
            NEW.delivery_assigned_at := NOW();
        END IF;
    END IF;
    
    IF NEW.assigned_delivery_person_id IS NOT NULL AND NEW.assigned_delivery_person IS NULL THEN
        NEW.assigned_delivery_person := NEW.assigned_delivery_person_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER sync_delivery_person_fields_trigger
    BEFORE INSERT OR UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION sync_delivery_person_fields();

-- Stock movement trigger
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
BEGIN
    -- Update main product stock
    UPDATE products
    SET stock = stock + NEW.quantity,
        updated_at = NOW()
    WHERE id = NEW.product_id;

    -- Update branch inventory if branch_id is provided
    IF NEW.branch_id IS NOT NULL THEN
        INSERT INTO branch_inventory (branch_id, product_id, stock, updated_at)
        VALUES (NEW.branch_id, NEW.product_id, NEW.quantity, NOW())
        ON CONFLICT (branch_id, product_id)
        DO UPDATE SET
            stock = branch_inventory.stock + NEW.quantity,
            updated_at = NOW();
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_stock
    AFTER INSERT ON stock_movements
    FOR EACH ROW
    EXECUTE FUNCTION update_product_stock();

-- Order total calculation trigger
CREATE OR REPLACE FUNCTION calculate_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalculate order totals when items change
    UPDATE orders
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0)
        FROM order_items
        WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
    ),
    total = subtotal + delivery_fee - discount_amount + tax_amount,
    updated_at = NOW()
    WHERE id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_calculate_order_totals
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION calculate_order_totals();

-- Customer profile update trigger
CREATE OR REPLACE FUNCTION update_customer_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update customer profile statistics when orders change
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE customer_profiles
        SET total_orders = (
            SELECT COUNT(*)
            FROM orders
            WHERE customer_id = NEW.customer_id
            AND status NOT IN ('cancelled', 'returned')
        ),
        total_spent = (
            SELECT COALESCE(SUM(total), 0)
            FROM orders
            WHERE customer_id = NEW.customer_id
            AND status = 'delivered'
        ),
        last_order_date = (
            SELECT MAX(created_at)
            FROM orders
            WHERE customer_id = NEW.customer_id
        ),
        updated_at = NOW()
        WHERE user_id = NEW.customer_id;
        
        -- Create customer profile if it doesn't exist
        INSERT INTO customer_profiles (user_id, total_orders, total_spent, last_order_date)
        SELECT NEW.customer_id, 1, NEW.total, NEW.created_at
        WHERE NOT EXISTS (
            SELECT 1 FROM customer_profiles WHERE user_id = NEW.customer_id
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_customer_stats
    AFTER INSERT OR UPDATE OR DELETE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_customer_stats();

-- Product rating update trigger
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    -- Update product rating and review count
    UPDATE products
    SET rating = (
        SELECT COALESCE(AVG(rating), 0)
        FROM product_reviews
        WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        AND status = 'approved'
    ),
    review_count = (
        SELECT COUNT(*)
        FROM product_reviews
        WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        AND status = 'approved'
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_rating
    AFTER INSERT OR UPDATE OR DELETE ON product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_product_rating();

-- =============================================
-- SECURITY FUNCTIONS
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT user_type
        FROM users
        WHERE id = auth.uid()
        AND is_active = true
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(get_user_role() = 'admin', false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager or admin
CREATE OR REPLACE FUNCTION is_manager_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(get_user_role() IN ('admin', 'manager'), false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can manage clients
CREATE OR REPLACE FUNCTION can_manage_clients()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(get_user_role() IN ('admin', 'manager'), false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- BUSINESS LOGIC FUNCTIONS
-- =============================================

-- Function to get orders for user with proper permissions
CREATE OR REPLACE FUNCTION get_orders_for_user(user_id_param UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    customer_id UUID,
    customer_name TEXT,
    total_amount DECIMAL,
    status TEXT,
    delivery_status TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Use current user if no user_id provided
    IF user_id_param IS NULL THEN
        user_id_param := auth.uid();
    END IF;

    -- Return orders based on user role
    RETURN QUERY
    SELECT
        o.id,
        CAST(o.order_number AS TEXT),
        o.customer_id,
        CAST(u.full_name AS TEXT) as customer_name,
        o.total,
        CAST(o.status AS TEXT),
        CAST(o.delivery_status AS TEXT),
        o.created_at,
        o.updated_at
    FROM orders o
    LEFT JOIN users u ON o.customer_id = u.id
    WHERE
        -- Admin/Manager can see all orders
        (get_user_role() IN ('admin', 'manager')) OR
        -- Customers can see their own orders
        (o.customer_id = user_id_param) OR
        -- Delivery persons can see assigned orders
        (get_user_role() = 'delivery_person' AND o.assigned_delivery_person = user_id_param)
    ORDER BY o.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Parameterless version for frontend compatibility
CREATE OR REPLACE FUNCTION get_orders_for_user()
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    customer_id UUID,
    customer_name TEXT,
    total_amount DECIMAL,
    status TEXT,
    delivery_status TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM get_orders_for_user(auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_statistics(target_user_id UUID DEFAULT NULL)
RETURNS JSONB AS $$
DECLARE
    user_stats JSONB;
    current_user_role TEXT;
BEGIN
    current_user_role := get_user_role();

    -- Use current user if no target specified
    IF target_user_id IS NULL THEN
        target_user_id := auth.uid();
    END IF;

    -- Check permissions
    IF NOT (current_user_role IN ('admin', 'manager') OR target_user_id = auth.uid()) THEN
        RETURN '{"error": "Access denied"}'::JSONB;
    END IF;

    SELECT jsonb_build_object(
        'total_orders', COALESCE(cp.total_orders, 0),
        'total_spent', COALESCE(cp.total_spent, 0),
        'loyalty_points', COALESCE(cp.loyalty_points, 0),
        'last_order_date', cp.last_order_date,
        'wishlist_count', (
            SELECT COUNT(*) FROM wishlists WHERE customer_id = target_user_id
        ),
        'review_count', (
            SELECT COUNT(*) FROM product_reviews WHERE customer_id = target_user_id
        )
    ) INTO user_stats
    FROM customer_profiles cp
    WHERE cp.user_id = target_user_id;

    RETURN COALESCE(user_stats, '{}'::JSONB);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate order total
CREATE OR REPLACE FUNCTION calculate_order_total(order_id UUID)
RETURNS DECIMAL(12,2) AS $$
DECLARE
    subtotal DECIMAL(12,2);
    delivery_fee DECIMAL(12,2);
    discount DECIMAL(12,2);
    tax DECIMAL(12,2);
    total DECIMAL(12,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO subtotal
    FROM order_items WHERE order_id = calculate_order_total.order_id;

    -- Get delivery fee, discount, and tax from order
    SELECT o.delivery_fee, o.discount_amount, o.tax_amount
    INTO delivery_fee, discount, tax
    FROM orders o WHERE id = calculate_order_total.order_id;

    total := subtotal + COALESCE(delivery_fee, 0) - COALESCE(discount, 0) + COALESCE(tax, 0);

    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Function to apply promo code
CREATE OR REPLACE FUNCTION apply_promo_code(code_text TEXT, order_total DECIMAL)
RETURNS JSONB AS $$
DECLARE
    promo promo_codes%ROWTYPE;
    discount_amount DECIMAL(12,2);
    result JSONB;
BEGIN
    -- Get promo code
    SELECT * INTO promo
    FROM promo_codes
    WHERE code = code_text
    AND is_active = true
    AND (valid_from IS NULL OR valid_from <= NOW())
    AND (valid_until IS NULL OR valid_until >= NOW())
    AND (usage_limit IS NULL OR used_count < usage_limit);

    IF NOT FOUND THEN
        RETURN '{"valid": false, "error": "Invalid or expired promo code"}'::JSONB;
    END IF;

    -- Check minimum order amount
    IF promo.min_order_amount IS NOT NULL AND order_total < promo.min_order_amount THEN
        RETURN jsonb_build_object(
            'valid', false,
            'error', 'Minimum order amount of ' || promo.min_order_amount || ' required'
        );
    END IF;

    -- Calculate discount
    IF promo.discount_type = 'percentage' THEN
        discount_amount := order_total * (promo.discount_value / 100);
    ELSE
        discount_amount := promo.discount_value;
    END IF;

    -- Apply maximum discount limit
    IF promo.max_discount_amount IS NOT NULL AND discount_amount > promo.max_discount_amount THEN
        discount_amount := promo.max_discount_amount;
    END IF;

    RETURN jsonb_build_object(
        'valid', true,
        'discount_amount', discount_amount,
        'promo_id', promo.id,
        'promo_name', promo.name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE promo_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_behavior ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS users_select_policy ON users;
DROP POLICY IF EXISTS users_insert_policy ON users;
DROP POLICY IF EXISTS users_update_policy ON users;
DROP POLICY IF EXISTS users_delete_policy ON users;

DROP POLICY IF EXISTS user_profiles_select_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_insert_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_update_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_delete_policy ON user_profiles;

DROP POLICY IF EXISTS orders_select_policy ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;
DROP POLICY IF EXISTS orders_delete_policy ON orders;

-- Users table policies
CREATE POLICY users_select_policy ON users
    FOR SELECT USING (
        auth.uid() = id OR
        is_manager_or_admin()
    );

CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (
        is_admin()
    );

CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (
        auth.uid() = id OR
        is_admin()
    )
    WITH CHECK (
        auth.uid() = id OR
        is_admin()
    );

CREATE POLICY users_delete_policy ON users
    FOR DELETE USING (
        is_admin()
    );

-- User profiles table policies
CREATE POLICY user_profiles_select_policy ON user_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY user_profiles_insert_policy ON user_profiles
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY user_profiles_update_policy ON user_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    )
    WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY user_profiles_delete_policy ON user_profiles
    FOR DELETE USING (
        is_admin()
    );

-- Orders table policies
CREATE POLICY orders_select_policy ON orders
    FOR SELECT USING (
        -- Customers can see their own orders
        auth.uid() = customer_id OR
        -- Users can see orders they created
        auth.uid() = created_by OR
        -- Admins and managers can see all orders
        is_manager_or_admin() OR
        -- Delivery persons can see their assigned orders
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        )
    );

CREATE POLICY orders_insert_policy ON orders
    FOR INSERT WITH CHECK (
        -- Must be authenticated
        auth.uid() IS NOT NULL AND
        -- Customer must be valid (either self or managed by admin/manager)
        (
            customer_id = auth.uid() OR
            is_manager_or_admin()
        )
    );

CREATE POLICY orders_update_policy ON orders
    FOR UPDATE USING (
        -- Admins and managers can update all orders
        is_manager_or_admin() OR
        -- Delivery persons can update status of assigned orders
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        ) OR
        -- Customers can update their own orders (limited fields)
        (
            auth.uid() = customer_id AND
            get_user_role() IN ('client', 'reseller')
        )
    )
    WITH CHECK (
        -- Same conditions for WITH CHECK
        is_manager_or_admin() OR
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        ) OR
        (
            auth.uid() = customer_id AND
            get_user_role() IN ('client', 'reseller')
        )
    );

CREATE POLICY orders_delete_policy ON orders
    FOR DELETE USING (
        is_admin()
    );

-- Order items policies
CREATE POLICY order_items_select_policy ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_id
            AND (
                auth.uid() = o.customer_id OR
                auth.uid() = o.created_by OR
                is_manager_or_admin() OR
                (get_user_role() = 'delivery_person' AND auth.uid() = o.assigned_delivery_person)
            )
        )
    );

CREATE POLICY order_items_insert_policy ON order_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_id
            AND (
                auth.uid() = o.customer_id OR
                is_manager_or_admin()
            )
        )
    );

CREATE POLICY order_items_update_policy ON order_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_id
            AND (
                is_manager_or_admin() OR
                (auth.uid() = o.customer_id AND o.status IN ('pending', 'confirmed'))
            )
        )
    );

CREATE POLICY order_items_delete_policy ON order_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_id
            AND (
                is_manager_or_admin() OR
                (auth.uid() = o.customer_id AND o.status IN ('pending', 'confirmed'))
            )
        )
    );

-- Products table policies (public read, admin/manager write)
CREATE POLICY products_select_policy ON products
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

CREATE POLICY products_insert_policy ON products
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY products_update_policy ON products
    FOR UPDATE USING (
        is_manager_or_admin()
    )
    WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY products_delete_policy ON products
    FOR DELETE USING (
        is_admin()
    );

-- Customer profiles policies
CREATE POLICY customer_profiles_select_policy ON customer_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        can_manage_clients()
    );

CREATE POLICY customer_profiles_insert_policy ON customer_profiles
    FOR INSERT WITH CHECK (
        can_manage_clients()
    );

CREATE POLICY customer_profiles_update_policy ON customer_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        can_manage_clients()
    )
    WITH CHECK (
        auth.uid() = user_id OR
        can_manage_clients()
    );

CREATE POLICY customer_profiles_delete_policy ON customer_profiles
    FOR DELETE USING (
        is_admin()
    );

-- Wishlists policies
CREATE POLICY wishlists_select_policy ON wishlists
    FOR SELECT USING (
        auth.uid() = customer_id OR
        is_manager_or_admin()
    );

CREATE POLICY wishlists_insert_policy ON wishlists
    FOR INSERT WITH CHECK (
        auth.uid() = customer_id
    );

CREATE POLICY wishlists_delete_policy ON wishlists
    FOR DELETE USING (
        auth.uid() = customer_id OR
        is_admin()
    );

-- Product reviews policies
CREATE POLICY product_reviews_select_policy ON product_reviews
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND
        (
            status = 'approved' OR
            auth.uid() = customer_id OR
            is_manager_or_admin()
        )
    );

CREATE POLICY product_reviews_insert_policy ON product_reviews
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        auth.uid() = customer_id
    );

CREATE POLICY product_reviews_update_policy ON product_reviews
    FOR UPDATE USING (
        auth.uid() = customer_id OR
        is_manager_or_admin()
    )
    WITH CHECK (
        auth.uid() = customer_id OR
        is_manager_or_admin()
    );

CREATE POLICY product_reviews_delete_policy ON product_reviews
    FOR DELETE USING (
        auth.uid() = customer_id OR
        is_admin()
    );

-- Promo codes policies
CREATE POLICY promo_codes_select_policy ON promo_codes
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND
        (
            is_active = true OR
            is_manager_or_admin()
        )
    );

CREATE POLICY promo_codes_insert_policy ON promo_codes
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY promo_codes_update_policy ON promo_codes
    FOR UPDATE USING (
        is_manager_or_admin()
    )
    WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY promo_codes_delete_policy ON promo_codes
    FOR DELETE USING (
        is_admin()
    );

-- Notifications policies
CREATE POLICY notifications_select_policy ON notifications
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY notifications_insert_policy ON notifications
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY notifications_update_policy ON notifications
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY notifications_delete_policy ON notifications
    FOR DELETE USING (
        auth.uid() = user_id OR
        is_admin()
    );

-- Audit logs policies (admin only)
CREATE POLICY audit_logs_select_policy ON audit_logs
    FOR SELECT USING (
        is_admin()
    );

CREATE POLICY audit_logs_insert_policy ON audit_logs
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Security events policies (admin only)
CREATE POLICY security_events_select_policy ON security_events
    FOR SELECT USING (
        is_admin()
    );

CREATE POLICY security_events_insert_policy ON security_events
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Email queue policies (admin only)
CREATE POLICY email_queue_select_policy ON email_queue
    FOR SELECT USING (
        is_manager_or_admin()
    );

CREATE POLICY email_queue_insert_policy ON email_queue
    FOR INSERT WITH CHECK (
        true -- System can insert emails
    );

-- Stock movements policies
CREATE POLICY stock_movements_select_policy ON stock_movements
    FOR SELECT USING (
        is_manager_or_admin()
    );

CREATE POLICY stock_movements_insert_policy ON stock_movements
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

-- Branch inventory policies
CREATE POLICY branch_inventory_select_policy ON branch_inventory
    FOR SELECT USING (
        is_manager_or_admin()
    );

CREATE POLICY branch_inventory_insert_policy ON branch_inventory
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY branch_inventory_update_policy ON branch_inventory
    FOR UPDATE USING (
        is_manager_or_admin()
    );

-- Password reset tokens policies
CREATE POLICY password_reset_tokens_select_policy ON password_reset_tokens
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_admin()
    );

CREATE POLICY password_reset_tokens_insert_policy ON password_reset_tokens
    FOR INSERT WITH CHECK (
        true -- System can create tokens
    );

CREATE POLICY password_reset_tokens_update_policy ON password_reset_tokens
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_admin()
    );

-- Analytics events policies
CREATE POLICY analytics_events_select_policy ON analytics_events
    FOR SELECT USING (
        is_manager_or_admin()
    );

CREATE POLICY analytics_events_insert_policy ON analytics_events
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Customer behavior policies
CREATE POLICY customer_behavior_select_policy ON customer_behavior
    FOR SELECT USING (
        auth.uid() = customer_id OR
        is_manager_or_admin()
    );

CREATE POLICY customer_behavior_insert_policy ON customer_behavior
    FOR INSERT WITH CHECK (
        auth.uid() = customer_id OR
        is_manager_or_admin()
    );

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_manager_or_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION can_manage_clients() TO authenticated;
GRANT EXECUTE ON FUNCTION get_orders_for_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_orders_for_user() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_statistics(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_order_total(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION apply_promo_code(TEXT, DECIMAL) TO authenticated;

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- Product inventory view with branch details
CREATE OR REPLACE VIEW product_inventory_view AS
SELECT
    p.id,
    p.title,
    p.sku,
    p.price,
    p.reseller_price,
    c.name as category_name,
    p.stock as total_stock,
    p.min_stock,
    COALESCE(bi.stock, 0) as branch_stock,
    b.name as branch_name,
    b.code as branch_code,
    CASE
        WHEN COALESCE(bi.stock, p.stock) <= p.min_stock THEN 'low'
        WHEN COALESCE(bi.stock, p.stock) = 0 THEN 'out'
        ELSE 'normal'
    END as stock_status,
    p.is_active,
    p.created_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN branch_inventory bi ON p.id = bi.product_id
LEFT JOIN branches b ON bi.branch_id = b.id
WHERE p.is_active = true;

-- Order summary view
CREATE OR REPLACE VIEW order_summary_view AS
SELECT
    o.id,
    o.order_number,
    o.status,
    o.payment_status,
    o.delivery_status,
    o.total,
    o.created_at,
    o.updated_at,
    u.full_name as customer_name,
    u.email as customer_email,
    u.user_type as customer_type,
    b.name as branch_name,
    dp.full_name as delivery_person_name,
    COUNT(oi.id) as item_count,
    SUM(oi.quantity) as total_quantity
FROM orders o
LEFT JOIN users u ON o.customer_id = u.id
LEFT JOIN branches b ON o.branch_id = b.id
LEFT JOIN users dp ON o.assigned_delivery_person = dp.id
LEFT JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id, u.full_name, u.email, u.user_type, b.name, dp.full_name;

-- Customer analytics view
CREATE OR REPLACE VIEW customer_analytics_view AS
SELECT
    u.id,
    u.full_name,
    u.email,
    u.user_type,
    u.city,
    u.is_company,
    u.company_name,
    cp.total_orders,
    cp.total_spent,
    cp.last_order_date,
    cp.loyalty_points,
    cp.status,
    CASE
        WHEN cp.total_spent >= 50000 THEN 'VIP'
        WHEN cp.total_spent >= 20000 THEN 'Gold'
        WHEN cp.total_spent >= 5000 THEN 'Silver'
        ELSE 'Bronze'
    END as customer_tier,
    (
        SELECT COUNT(*)
        FROM wishlists w
        WHERE w.customer_id = u.id
    ) as wishlist_count,
    (
        SELECT COUNT(*)
        FROM product_reviews pr
        WHERE pr.customer_id = u.id
        AND pr.status = 'approved'
    ) as review_count,
    u.created_at as registration_date
FROM users u
LEFT JOIN customer_profiles cp ON u.id = cp.user_id
WHERE u.user_type IN ('client', 'reseller')
AND u.is_active = true;

-- Product performance view
CREATE OR REPLACE VIEW product_performance_view AS
SELECT
    p.id,
    p.title,
    p.sku,
    p.price,
    p.stock,
    p.rating,
    p.review_count,
    c.name as category_name,
    (
        SELECT COUNT(*)
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE oi.product_id = p.id
        AND o.status = 'delivered'
        AND o.created_at >= NOW() - INTERVAL '30 days'
    ) as sales_last_30_days,
    (
        SELECT COALESCE(SUM(oi.quantity), 0)
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE oi.product_id = p.id
        AND o.status = 'delivered'
    ) as total_sold,
    (
        SELECT COALESCE(SUM(oi.total_price), 0)
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE oi.product_id = p.id
        AND o.status = 'delivered'
    ) as total_revenue,
    (
        SELECT COUNT(*)
        FROM wishlists w
        WHERE w.product_id = p.id
    ) as wishlist_count,
    p.created_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.is_active = true;

-- Sales analytics view
CREATE OR REPLACE VIEW sales_analytics_view AS
SELECT
    DATE_TRUNC('day', o.created_at) as date,
    COUNT(*) as order_count,
    SUM(o.total) as total_revenue,
    AVG(o.total) as avg_order_value,
    COUNT(DISTINCT o.customer_id) as unique_customers,
    SUM(oi.quantity) as total_items_sold
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
WHERE o.status NOT IN ('cancelled', 'returned')
GROUP BY DATE_TRUNC('day', o.created_at)
ORDER BY date DESC;

-- Delivery performance view
CREATE OR REPLACE VIEW delivery_performance_view AS
SELECT
    dp.id as delivery_person_id,
    dp.full_name as delivery_person_name,
    COUNT(*) as total_deliveries,
    COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as completed_deliveries,
    COUNT(CASE WHEN o.delivery_status = 'failed' THEN 1 END) as failed_deliveries,
    AVG(
        CASE
            WHEN o.delivered_at IS NOT NULL AND o.delivery_assigned_at IS NOT NULL
            THEN EXTRACT(EPOCH FROM (o.delivered_at - o.delivery_assigned_at)) / 3600
        END
    ) as avg_delivery_time_hours,
    MAX(o.delivered_at) as last_delivery_date
FROM users dp
LEFT JOIN orders o ON dp.id = o.assigned_delivery_person
WHERE dp.user_type = 'delivery_person'
AND dp.is_active = true
GROUP BY dp.id, dp.full_name;

-- =============================================
-- INITIAL SEED DATA
-- =============================================

-- Insert default company settings
INSERT INTO company_settings (id, name, address, phone, email, tax_id, ice_number, logo_url) VALUES
(uuid_generate_v4(), 'YalaOffice', 'Avenue Mohammed V, Casablanca, Morocco', '+*********** 456', '<EMAIL>', 'TAX123456', 'ICE001234567890123', '/assets/logo.png')
ON CONFLICT DO NOTHING;

-- Insert system configurations
INSERT INTO system_configs (category, key, value, description, is_public) VALUES
('general', 'site_name', '"YalaOffice"', 'Site name', true),
('general', 'site_description', '"Smart Supply Management System for Moroccan Businesses"', 'Site description', true),
('general', 'developer', '"Alindevx00x"', 'System developer', true),
('general', 'developer_github', '"https://github.com/alindevx00x"', 'Developer GitHub profile', true),
('general', 'version', '"2.0.0"', 'System version', true),
('general', 'default_currency', '"MAD"', 'Default currency', true),
('general', 'default_language', '"en"', 'Default language', true),
('general', 'timezone', '"Africa/Casablanca"', 'Default timezone', true),
('orders', 'auto_confirm', 'false', 'Auto-confirm orders', false),
('orders', 'default_delivery_fee', '50', 'Default delivery fee in MAD', false),
('orders', 'free_delivery_threshold', '500', 'Free delivery threshold in MAD', true),
('inventory', 'low_stock_threshold', '10', 'Low stock alert threshold', false),
('inventory', 'auto_reorder', 'false', 'Auto-reorder when stock is low', false),
('notifications', 'email_enabled', 'true', 'Enable email notifications', false),
('notifications', 'sms_enabled', 'false', 'Enable SMS notifications', false),
('security', 'session_timeout', '86400', 'Session timeout in seconds', false),
('security', 'max_login_attempts', '5', 'Maximum login attempts', false),
('security', 'lockout_duration', '900', 'Account lockout duration in seconds', false)
ON CONFLICT (category, key) DO NOTHING;

-- Insert main branch
INSERT INTO branches (id, name, code, address, contact, is_active, is_main_branch, created_at) VALUES
(uuid_generate_v4(), 'Main Branch - Casablanca', 'MAIN-CASA',
'{"street": "Avenue Mohammed V", "city": "Casablanca", "state": "Grand Casablanca", "postal_code": "20000", "country": "Morocco"}',
'{"phone": "+*********** 456", "email": "<EMAIL>", "manager": "Store Manager"}',
true, true, NOW())
ON CONFLICT (code) DO NOTHING;

-- Insert additional branches for Morocco
INSERT INTO branches (id, name, code, address, contact, is_active, is_main_branch, created_at) VALUES
(uuid_generate_v4(), 'Branch Rabat', 'BRANCH-RABAT',
'{"street": "Avenue Allal Ben Abdellah", "city": "Rabat", "state": "Rabat-Salé-Kénitra", "postal_code": "10000", "country": "Morocco"}',
'{"phone": "+212 537 123 456", "email": "<EMAIL>", "manager": "Rabat Manager"}',
true, false, NOW()),
(uuid_generate_v4(), 'Branch Marrakech', 'BRANCH-MARRAKECH',
'{"street": "Avenue Mohammed VI", "city": "Marrakech", "state": "Marrakech-Safi", "postal_code": "40000", "country": "Morocco"}',
'{"phone": "+212 524 123 456", "email": "<EMAIL>", "manager": "Marrakech Manager"}',
true, false, NOW()),
(uuid_generate_v4(), 'Branch Fès', 'BRANCH-FES',
'{"street": "Avenue Hassan II", "city": "Fès", "state": "Fès-Meknès", "postal_code": "30000", "country": "Morocco"}',
'{"phone": "+212 535 123 456", "email": "<EMAIL>", "manager": "Fès Manager"}',
true, false, NOW())
ON CONFLICT (code) DO NOTHING;

-- Insert root categories
INSERT INTO categories (id, name, slug, description, parent_id, level, sort_order, is_active) VALUES
(uuid_generate_v4(), 'Office Supplies', 'office-supplies', 'Essential office supplies and stationery', NULL, 0, 1, true),
(uuid_generate_v4(), 'Technology', 'technology', 'Computers, electronics and tech accessories', NULL, 0, 2, true),
(uuid_generate_v4(), 'Furniture', 'furniture', 'Office furniture and equipment', NULL, 0, 3, true),
(uuid_generate_v4(), 'Cleaning Supplies', 'cleaning-supplies', 'Cleaning and maintenance supplies', NULL, 0, 4, true),
(uuid_generate_v4(), 'Safety Equipment', 'safety-equipment', 'Safety and security equipment', NULL, 0, 5, true)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample promo codes
INSERT INTO promo_codes (id, code, name, description, discount_type, discount_value, min_order_amount, is_active, valid_from, valid_until, created_by) VALUES
(uuid_generate_v4(), 'WELCOME10', 'Welcome Discount', '10% discount for new customers', 'percentage', 10, 100, true, NOW(), NOW() + INTERVAL '1 year', NULL),
(uuid_generate_v4(), 'BULK50', 'Bulk Order Discount', '50 MAD off for orders over 1000 MAD', 'fixed', 50, 1000, true, NOW(), NOW() + INTERVAL '6 months', NULL),
(uuid_generate_v4(), 'FREESHIP', 'Free Shipping', 'Free delivery for orders over 500 MAD', 'fixed', 50, 500, true, NOW(), NOW() + INTERVAL '1 year', NULL)
ON CONFLICT (code) DO NOTHING;

-- =============================================
-- FINAL SETUP AND VERIFICATION
-- =============================================

-- Create function to verify schema integrity
CREATE OR REPLACE FUNCTION verify_schema_integrity()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT
) AS $$
BEGIN
    -- Check if all required tables exist
    RETURN QUERY
    SELECT
        'Tables Check' as check_name,
        CASE
            WHEN COUNT(*) >= 25 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        COUNT(*)::TEXT || ' tables found' as details
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE';

    -- Check if all required functions exist
    RETURN QUERY
    SELECT
        'Functions Check' as check_name,
        CASE
            WHEN COUNT(*) >= 10 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        COUNT(*)::TEXT || ' functions found' as details
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_type = 'FUNCTION';

    -- Check if RLS is enabled on sensitive tables
    RETURN QUERY
    SELECT
        'RLS Check' as check_name,
        CASE
            WHEN COUNT(*) >= 15 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        COUNT(*)::TEXT || ' tables with RLS enabled' as details
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public'
    AND c.relkind = 'r'
    AND c.relrowsecurity = true;

    -- Check if indexes are created
    RETURN QUERY
    SELECT
        'Indexes Check' as check_name,
        CASE
            WHEN COUNT(*) >= 30 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        COUNT(*)::TEXT || ' indexes found' as details
    FROM pg_indexes
    WHERE schemaname = 'public';

    -- Check if triggers are created
    RETURN QUERY
    SELECT
        'Triggers Check' as check_name,
        CASE
            WHEN COUNT(*) >= 10 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        COUNT(*)::TEXT || ' triggers found' as details
    FROM information_schema.triggers
    WHERE trigger_schema = 'public';
END;
$$ LANGUAGE plpgsql;

-- Add table comments for documentation
COMMENT ON TABLE users IS 'Core user accounts with comprehensive role-based access control';
COMMENT ON TABLE user_profiles IS 'Extended user profile information and preferences';
COMMENT ON TABLE products IS 'Product catalog with comprehensive inventory tracking';
COMMENT ON TABLE orders IS 'Customer orders with complete status and delivery tracking';
COMMENT ON TABLE order_items IS 'Individual items within orders';
COMMENT ON TABLE branches IS 'Physical locations/branches for multi-location support';
COMMENT ON TABLE categories IS 'Hierarchical product categorization system';
COMMENT ON TABLE customer_profiles IS 'Extended customer information and statistics';
COMMENT ON TABLE stock_movements IS 'Complete audit trail for all inventory changes';
COMMENT ON TABLE audit_logs IS 'System audit trail for compliance and security';
COMMENT ON TABLE security_events IS 'Security events and incident tracking';
COMMENT ON TABLE notifications IS 'System notifications for users';
COMMENT ON TABLE email_queue IS 'Email delivery queue with retry mechanism';
COMMENT ON TABLE promo_codes IS 'Promotional codes and discount management';
COMMENT ON TABLE wishlists IS 'Customer product wishlists';
COMMENT ON TABLE product_reviews IS 'Product reviews and ratings system';
COMMENT ON TABLE analytics_events IS 'User behavior and analytics tracking';
COMMENT ON TABLE customer_behavior IS 'Customer behavior analysis data';

-- Success message and verification
SELECT 'YalaOffice Production Database Schema v2.0.0 created successfully!' as status;
SELECT 'Developed by: Alindevx00x (https://github.com/alindevx00x)' as developer_info;
SELECT 'Running schema integrity verification...' as info;
SELECT * FROM verify_schema_integrity();

-- Final statistics
SELECT
    'SCHEMA STATISTICS' as info,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE') as total_tables,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public' AND routine_type = 'FUNCTION') as total_functions,
    (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public') as total_indexes,
    (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema = 'public') as total_triggers,
    (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public') as total_policies;
