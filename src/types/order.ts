
export interface OrderCustomer {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  city?: string;
  userType: string;
  isCompany?: boolean;
  companyName?: string | null;
  companyAddress?: string | null;
  companyPhone?: string | null;
  companyCity?: string | null;
  companyEmail?: string | null;
  iceNumber?: string | null;
  taxId?: string | null;
  legalForm?: string | null;
}

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customer?: OrderCustomer; // Complete customer information for invoices
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  status: OrderStatus;
  paymentMethod: string;
  paymentStatus: PaymentStatus;
  deliveryAddress: string;
  selectedBranch: string;
  promoCode?: string;
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
  notes?: string;
}

export interface OrderItem {
  id: number;
  title: string;
  category: string;
  price: number;
  quantity: number;
  image: string;
}

export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

export interface OrderSummary {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
}
