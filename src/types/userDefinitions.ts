declare namespace UserTypes {
  export type UserType = 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
  export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';
}

export type UserType = UserTypes.UserType;
export type UserStatus = UserTypes.UserStatus;

export interface User {
  id: string;
  email: string;
  fullName: string;
  userType: UserType;
  phone?: string;
  city?: string;
  isActive: boolean;
  status?: UserStatus;
  createdAt: string;
  updatedAt?: string;
  lastLogin?: string;
  isCompany?: boolean;
  companyName?: string;
  companyAddress?: string;
  companyCity?: string;
  companyPhone?: string;
  companyEmail?: string;
  iceNumber?: string;
  taxId?: string;
  legalForm?: string;
  createdBy?: string;
  permissions?: string[];
  avatar?: string;
  department?: string;
  branch?: string;
}

export interface UserProfile {
  userId: string;
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      orderUpdates: boolean;
      promotions: boolean;
      stockAlerts: boolean;
    };
    orderDefaults: {
      deliveryPreference: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id: string;
  userId: string;
  type: 'shipping' | 'billing' | 'both';
  label: string;
  fullName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  userId: string;
  type: 'bank_transfer' | 'check' | 'cash_on_delivery';
  label: string;
  isDefault: boolean;
  bankName?: string;
  accountNumber?: string;
  checkDetails?: string;
  createdAt: string;
  updatedAt: string;
}
