import { useState, useEffect, useCallback } from 'react';
import { deliveryService, DeliveryOrder, DeliveryStats } from '../services/deliveryService';

export const useDeliveryOrders = (deliveryPersonId: string) => {
  const [orders, setOrders] = useState<DeliveryOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadOrders = useCallback(async () => {
    try {
      console.log('🔄 useDeliveryOrders: Starting to load orders for delivery person:', deliveryPersonId);
      setLoading(true);
      setError(null);

      if (!deliveryPersonId) {
        console.warn('⚠️  useDeliveryOrders: No delivery person ID provided');
        setOrders([]);
        return;
      }

      const data = await deliveryService.getAssignedOrders(deliveryPersonId);
      console.log('✅ useDeliveryOrders: Successfully loaded orders:', data.length);
      console.log('📋 useDeliveryOrders: Order details:', data);
      setOrders(data);
    } catch (err) {
      const errorMessage = 'Failed to load assigned orders';
      setError(errorMessage);
      console.error('❌ useDeliveryOrders: Error loading delivery orders:', err);
      console.error('❌ useDeliveryOrders: Error details:', {
        deliveryPersonId,
        error: err,
        message: err instanceof Error ? err.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  }, [deliveryPersonId]);

  useEffect(() => {
    if (deliveryPersonId) {
      console.log('🚀 useDeliveryOrders: Initializing for delivery person:', deliveryPersonId);
      loadOrders();

      // Subscribe to real-time updates
      console.log('📡 useDeliveryOrders: Setting up real-time subscription');
      const subscription = deliveryService.subscribeToOrderUpdates(
        deliveryPersonId,
        (payload) => {
          console.log('📨 useDeliveryOrders: Real-time order update received:', payload);
          // Reload orders when there's an update
          loadOrders();
        }
      );

      return () => {
        console.log('🔌 useDeliveryOrders: Cleaning up subscription');
        subscription.unsubscribe();
      };
    } else {
      console.warn('⚠️  useDeliveryOrders: No delivery person ID provided in useEffect');
    }
  }, [deliveryPersonId, loadOrders]);

  const updateOrderStatus = useCallback(async (orderId: string, newStatus: string) => {
    try {
      setError(null);
      const success = await deliveryService.updateOrderStatus(orderId, newStatus, deliveryPersonId);
      
      if (success) {
        // Optimistically update the local state
        setOrders(prev => prev.map(order => 
          order.id === orderId ? { ...order, status: newStatus } : order
        ));
        
        // Reload to get fresh data
        await loadOrders();
        return true;
      } else {
        setError('Failed to update order status');
        return false;
      }
    } catch (err) {
      setError('Failed to update order status');
      console.error('Error updating order status:', err);
      return false;
    }
  }, [deliveryPersonId, loadOrders]);

  return {
    orders,
    loading,
    error,
    refetch: loadOrders,
    updateOrderStatus
  };
};

export const useDeliveryHistory = (deliveryPersonId: string) => {
  const [history, setHistory] = useState<DeliveryOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadHistory = useCallback(async () => {
    try {
      console.log('🔄 useDeliveryHistory: Loading history for delivery person:', deliveryPersonId);
      setLoading(true);
      setError(null);

      if (!deliveryPersonId) {
        console.warn('⚠️  useDeliveryHistory: No delivery person ID provided');
        setHistory([]);
        return;
      }

      const data = await deliveryService.getDeliveryHistory(deliveryPersonId);
      console.log('✅ useDeliveryHistory: Successfully loaded history:', data.length);
      setHistory(data);
    } catch (err) {
      const errorMessage = 'Failed to load delivery history';
      setError(errorMessage);
      console.error('❌ useDeliveryHistory: Error loading delivery history:', err);
    } finally {
      setLoading(false);
    }
  }, [deliveryPersonId]);

  useEffect(() => {
    if (deliveryPersonId) {
      loadHistory();
    }
  }, [deliveryPersonId, loadHistory]);

  return {
    history,
    loading,
    error,
    refetch: loadHistory
  };
};

export const useDeliveryStats = (deliveryPersonId: string) => {
  const [stats, setStats] = useState<DeliveryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStats = useCallback(async () => {
    try {
      console.log('🔄 useDeliveryStats: Loading stats for delivery person:', deliveryPersonId);
      setLoading(true);
      setError(null);

      if (!deliveryPersonId) {
        console.warn('⚠️  useDeliveryStats: No delivery person ID provided');
        setStats(null);
        return;
      }

      const data = await deliveryService.getDeliveryStats(deliveryPersonId);
      console.log('✅ useDeliveryStats: Successfully loaded stats:', data);
      setStats(data);
    } catch (err) {
      const errorMessage = 'Failed to load delivery statistics';
      setError(errorMessage);
      console.error('❌ useDeliveryStats: Error loading delivery stats:', err);
    } finally {
      setLoading(false);
    }
  }, [deliveryPersonId]);

  useEffect(() => {
    if (deliveryPersonId) {
      loadStats();

      // Refresh stats every 5 minutes
      const interval = setInterval(loadStats, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [deliveryPersonId, loadStats]);

  return {
    stats,
    loading,
    error,
    refetch: loadStats
  };
};

// Utility functions for delivery operations
export const useDeliveryActions = () => {
  const makePhoneCall = useCallback((phoneNumber: string) => {
    deliveryService.makePhoneCall(phoneNumber);
  }, []);

  const navigateToAddress = useCallback((address: string) => {
    deliveryService.navigateToAddress(address);
  }, []);

  return {
    makePhoneCall,
    navigateToAddress
  };
};
