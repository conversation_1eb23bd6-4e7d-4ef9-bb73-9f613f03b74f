/**
 * Optimized Data Hooks for YalaOffice
 * Implements caching, pagination, and performance optimizations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { optimizedDataService } from '../services/optimizedDataService';
import { realTimeService } from '../services/realTimeService';
import { Product, Order, UserRow, CustomerProfileRow } from '../types';

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UseOptimizedDataResult<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  refetch: () => Promise<void>;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}

/**
 * Generic hook for paginated data with caching
 */
function useOptimizedPaginatedData<T>(
  fetchFunction: (page: number, limit: number) => Promise<PaginatedResponse<T>>,
  dependencies: any[] = [],
  initialPage: number = 1,
  initialLimit: number = 20
): UseOptimizedDataResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [pagination, setPagination] = useState({
    page: initialPage,
    limit: initialLimit,
    total: 0,
    totalPages: 0
  });

  // Use ref to prevent unnecessary re-renders
  const fetchFunctionRef = useRef(fetchFunction);
  fetchFunctionRef.current = fetchFunction;

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await fetchFunctionRef.current(page, limit);
      setData(result.data);
      setPagination(result.pagination);
    } catch (err) {
      setError(err as Error);
      console.error('Optimized data fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [page, limit, ...dependencies]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    pagination,
    refetch,
    setPage,
    setLimit
  };
}

/**
 * Optimized hook for products with caching and pagination
 */
export const useOptimizedProducts = (
  categoryId?: string,
  initialPage: number = 1,
  initialLimit: number = 20
) => {
  const result = useOptimizedPaginatedData(
    (page, limit) => optimizedDataService.getProducts(page, limit, categoryId),
    [categoryId],
    initialPage,
    initialLimit
  );

  // Set up real-time subscription for cache invalidation
  useEffect(() => {
    const unsubscribe = realTimeService.subscribe('product-updated', () => {
      optimizedDataService.invalidateCache('PRODUCTS');
      result.refetch();
    });

    return unsubscribe;
  }, [result.refetch]);

  return result;
};

/**
 * Optimized hook for orders with caching and pagination
 */
export const useOptimizedOrders = (
  status?: string,
  initialPage: number = 1,
  initialLimit: number = 20
) => {
  const result = useOptimizedPaginatedData(
    (page, limit) => optimizedDataService.getOrders(page, limit, status),
    [status],
    initialPage,
    initialLimit
  );

  // Set up real-time subscription for cache invalidation
  useEffect(() => {
    const unsubscribeCreated = realTimeService.subscribe('order-created', () => {
      optimizedDataService.invalidateCache('ORDERS');
      result.refetch();
    });

    const unsubscribeUpdated = realTimeService.subscribe('order-updated', () => {
      optimizedDataService.invalidateCache('ORDERS');
      result.refetch();
    });

    return () => {
      unsubscribeCreated();
      unsubscribeUpdated();
    };
  }, [result.refetch]);

  return result;
};

/**
 * Optimized hook for customers with caching and pagination
 */
export const useOptimizedCustomers = (
  initialPage: number = 1,
  initialLimit: number = 20
) => {
  const result = useOptimizedPaginatedData(
    (page, limit) => optimizedDataService.getCustomers(page, limit),
    [],
    initialPage,
    initialLimit
  );

  // Set up real-time subscription for cache invalidation
  useEffect(() => {
    const unsubscribe = realTimeService.subscribe('user-updated', () => {
      optimizedDataService.invalidateCache('CUSTOMERS');
      result.refetch();
    });

    return unsubscribe;
  }, [result.refetch]);

  return result;
};

/**
 * Optimized hook for dashboard stats with caching
 */
export const useOptimizedDashboardStats = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await optimizedDataService.getDashboardStats();
      setStats(data);
    } catch (err) {
      setError(err as Error);
      console.error('Dashboard stats fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Set up real-time subscription for cache invalidation
  useEffect(() => {
    const unsubscribeOrder = realTimeService.subscribe('order-created', () => {
      optimizedDataService.invalidateCache('DASHBOARD_STATS');
      fetchStats();
    });

    const unsubscribeProduct = realTimeService.subscribe('product-updated', () => {
      optimizedDataService.invalidateCache('DASHBOARD_STATS');
      fetchStats();
    });

    return () => {
      unsubscribeOrder();
      unsubscribeProduct();
    };
  }, [fetchStats]);

  return { stats, loading, error, refetch: fetchStats };
};

/**
 * Hook for single resource with caching
 */
export const useOptimizedResource = <T>(
  fetchFunction: () => Promise<T | null>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      setError(err as Error);
      console.error('Resource fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
};

/**
 * Hook for cache statistics monitoring
 */
export const useCacheStats = () => {
  const [stats, setStats] = useState(optimizedDataService.getCacheStats());

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(optimizedDataService.getCacheStats());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return stats;
};

export default {
  useOptimizedProducts,
  useOptimizedOrders,
  useOptimizedCustomers,
  useOptimizedDashboardStats,
  useOptimizedResource,
  useCacheStats
};
