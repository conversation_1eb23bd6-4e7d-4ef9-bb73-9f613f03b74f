/**
 * React hook for profile picture management
 * Provides real-time profile picture updates and easy management functions
 */

import { useState, useEffect, useCallback } from 'react';
import { profilePictureService, ProfilePicture } from '../services/profilePictureService';

export interface UseProfilePictureReturn {
  avatarUrl: string | null;
  displayAvatar: string;
  isLoading: boolean;
  error: string | null;
  updateProfilePicture: (avatarUrl: string) => Promise<boolean>;
  uploadProfilePicture: (file: File) => Promise<boolean>;
  refreshProfilePicture: () => Promise<void>;
}

/**
 * Hook for managing a specific user's profile picture
 */
export const useProfilePicture = (userId: string, fullName: string): UseProfilePictureReturn => {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [displayAvatar, setDisplayAvatar] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial profile picture
  const loadProfilePicture = useCallback(async () => {
    if (!userId || !fullName) return;

    try {
      setIsLoading(true);
      setError(null);

      const profilePicture = await profilePictureService.getProfilePicture(userId);
      const displayUrl = await profilePictureService.getDisplayAvatar(userId, fullName);

      setAvatarUrl(profilePicture?.avatarUrl || null);
      setDisplayAvatar(displayUrl);
    } catch (err) {
      console.error('Error loading profile picture:', err);
      setError('Failed to load profile picture');
      
      // Set fallback avatar
      const fallbackUrl = profilePictureService.generateFallbackAvatar(fullName);
      setDisplayAvatar(fallbackUrl);
    } finally {
      setIsLoading(false);
    }
  }, [userId, fullName]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!userId) return;

    // Load initial data
    loadProfilePicture();

    // Subscribe to updates
    const unsubscribe = profilePictureService.subscribe((profilePicture: ProfilePicture) => {
      if (profilePicture.userId === userId) {
        setAvatarUrl(profilePicture.avatarUrl);
        
        // Update display avatar
        if (profilePicture.avatarUrl) {
          setDisplayAvatar(profilePicture.avatarUrl);
        } else {
          const fallbackUrl = profilePictureService.generateFallbackAvatar(fullName);
          setDisplayAvatar(fallbackUrl);
        }
      }
    });

    return unsubscribe;
  }, [userId, fullName, loadProfilePicture]);

  // Update profile picture
  const updateProfilePicture = useCallback(async (newAvatarUrl: string): Promise<boolean> => {
    try {
      setError(null);
      const success = await profilePictureService.updateProfilePicture(userId, newAvatarUrl);
      
      if (success) {
        setAvatarUrl(newAvatarUrl);
        setDisplayAvatar(newAvatarUrl);
      } else {
        setError('Failed to update profile picture');
      }
      
      return success;
    } catch (err) {
      console.error('Error updating profile picture:', err);
      setError('Failed to update profile picture');
      return false;
    }
  }, [userId]);

  // Upload profile picture file
  const uploadProfilePicture = useCallback(async (file: File): Promise<boolean> => {
    try {
      setError(null);
      setIsLoading(true);

      const uploadedUrl = await profilePictureService.uploadProfilePicture(userId, file);
      
      if (uploadedUrl) {
        const success = await profilePictureService.updateProfilePicture(userId, uploadedUrl);
        
        if (success) {
          setAvatarUrl(uploadedUrl);
          setDisplayAvatar(uploadedUrl);
          return true;
        } else {
          setError('Failed to save profile picture');
          return false;
        }
      } else {
        setError('Failed to upload profile picture');
        return false;
      }
    } catch (err) {
      console.error('Error uploading profile picture:', err);
      setError('Failed to upload profile picture');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Refresh profile picture
  const refreshProfilePicture = useCallback(async () => {
    await loadProfilePicture();
  }, [loadProfilePicture]);

  return {
    avatarUrl,
    displayAvatar,
    isLoading,
    error,
    updateProfilePicture,
    uploadProfilePicture,
    refreshProfilePicture
  };
};

/**
 * Hook for managing multiple users' profile pictures (for admin interfaces)
 */
export const useMultipleProfilePictures = (userIds: string[]) => {
  const [profilePictures, setProfilePictures] = useState<Map<string, string>>(new Map());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMultipleProfilePictures = async () => {
      if (userIds.length === 0) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const pictureMap = new Map<string, string>();

        // Load all profile pictures
        await Promise.all(
          userIds.map(async (userId) => {
            try {
              const profilePicture = await profilePictureService.getProfilePicture(userId);
              if (profilePicture?.avatarUrl) {
                pictureMap.set(userId, profilePicture.avatarUrl);
              }
            } catch (error) {
              console.error(`Error loading profile picture for user ${userId}:`, error);
            }
          })
        );

        setProfilePictures(pictureMap);
      } catch (error) {
        console.error('Error loading multiple profile pictures:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMultipleProfilePictures();

    // Subscribe to updates
    const unsubscribe = profilePictureService.subscribe((profilePicture: ProfilePicture) => {
      if (userIds.includes(profilePicture.userId)) {
        setProfilePictures(prev => {
          const newMap = new Map(prev);
          if (profilePicture.avatarUrl) {
            newMap.set(profilePicture.userId, profilePicture.avatarUrl);
          } else {
            newMap.delete(profilePicture.userId);
          }
          return newMap;
        });
      }
    });

    return unsubscribe;
  }, [userIds]);

  const getProfilePicture = useCallback((userId: string, fullName: string): string => {
    const avatarUrl = profilePictures.get(userId);
    if (avatarUrl) {
      return avatarUrl;
    }
    return profilePictureService.generateFallbackAvatar(fullName);
  }, [profilePictures]);

  return {
    profilePictures,
    isLoading,
    getProfilePicture
  };
};

export default useProfilePicture;
