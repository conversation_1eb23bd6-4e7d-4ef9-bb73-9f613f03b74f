/**
 * Session Cleanup Utilities for YalaOffice
 * Handles complete session cleanup on logout to prevent cached content issues
 */

/**
 * List of all localStorage keys used throughout the application
 */
const LOCAL_STORAGE_KEYS = [
  // Dashboard active tabs
  'client_active_tab',
  'reseller_active_tab',
  'delivery_active_tab',
  'admin_active_tab',
  'manager_active_tab',
  
  // Dashboard current pages
  'admin_current_page',
  'manager_current_page',
  
  // Theme settings
  'yala-theme',
  
  // Cart service keys
  'yala_cart',
  'yala_saved_carts',
  
  // User preferences
  'user_preferences',
  'dashboard_preferences',
  
  // Search and filter states
  'product_search_filters',
  'order_search_filters',
  'user_search_filters',
  
  // View modes and settings
  'product_view_mode',
  'order_view_mode',
  
  // Notification settings
  'notification_preferences',
  
  // Advanced search states
  'advanced_search_state',
  
  // Comparison data
  'product_comparison',
  
  // Wishlist data (if stored locally)
  'wishlist_cache',
  
  // Recent searches
  'recent_searches',
  
  // Form data cache
  'form_cache',
  
  // Performance monitoring
  'performance_data',
  
  // Any other YalaOffice specific keys
  'yala_',  // This will be used as a prefix match
];

/**
 * List of all sessionStorage keys used throughout the application
 */
const SESSION_STORAGE_KEYS = [
  // Temporary form data
  'temp_form_data',
  'registration_data',
  
  // Navigation state
  'navigation_state',
  'previous_route',
  
  // Modal states
  'modal_state',
  
  // Temporary user data
  'temp_user_data',
  
  // Session-specific preferences
  'session_preferences',
  
  // Any other session-specific data
  'session_',  // This will be used as a prefix match
];

/**
 * Clear all localStorage items related to YalaOffice
 */
export const clearLocalStorage = (): void => {
  try {
    console.log('🧹 Clearing localStorage...');
    
    // Get all localStorage keys
    const allKeys = Object.keys(localStorage);
    
    // Clear specific known keys
    LOCAL_STORAGE_KEYS.forEach(key => {
      if (key.endsWith('_')) {
        // Handle prefix matches
        const prefix = key;
        allKeys.forEach(storageKey => {
          if (storageKey.startsWith(prefix)) {
            localStorage.removeItem(storageKey);
            console.log(`🗑️  Removed localStorage key: ${storageKey}`);
          }
        });
      } else {
        // Handle exact matches
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`🗑️  Removed localStorage key: ${key}`);
        }
      }
    });
    
    // Also clear any keys that start with 'yala' (case insensitive)
    allKeys.forEach(key => {
      if (key.toLowerCase().includes('yala')) {
        localStorage.removeItem(key);
        console.log(`🗑️  Removed YalaOffice localStorage key: ${key}`);
      }
    });
    
    console.log('✅ localStorage cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing localStorage:', error);
  }
};

/**
 * Clear all sessionStorage items related to YalaOffice
 */
export const clearSessionStorage = (): void => {
  try {
    console.log('🧹 Clearing sessionStorage...');
    
    // Get all sessionStorage keys
    const allKeys = Object.keys(sessionStorage);
    
    // Clear specific known keys
    SESSION_STORAGE_KEYS.forEach(key => {
      if (key.endsWith('_')) {
        // Handle prefix matches
        const prefix = key;
        allKeys.forEach(storageKey => {
          if (storageKey.startsWith(prefix)) {
            sessionStorage.removeItem(storageKey);
            console.log(`🗑️  Removed sessionStorage key: ${storageKey}`);
          }
        });
      } else {
        // Handle exact matches
        if (sessionStorage.getItem(key)) {
          sessionStorage.removeItem(key);
          console.log(`🗑️  Removed sessionStorage key: ${key}`);
        }
      }
    });
    
    // Also clear any keys that start with 'yala' (case insensitive)
    allKeys.forEach(key => {
      if (key.toLowerCase().includes('yala')) {
        sessionStorage.removeItem(key);
        console.log(`🗑️  Removed YalaOffice sessionStorage key: ${key}`);
      }
    });
    
    console.log('✅ sessionStorage cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing sessionStorage:', error);
  }
};

/**
 * Reset URL to base path without any parameters
 */
export const resetURL = (): void => {
  try {
    console.log('🔄 Resetting URL to base path...');
    
    // Get the base URL without parameters
    const baseUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname.split('/')[0] || '/'}`;
    
    // Reset to base URL
    window.history.replaceState({}, document.title, '/');
    
    console.log('✅ URL reset to base path');
  } catch (error) {
    console.error('❌ Error resetting URL:', error);
  }
};

/**
 * Clear browser cache (if possible)
 */
export const clearBrowserCache = (): void => {
  try {
    console.log('🧹 Attempting to clear browser cache...');
    
    // Clear any cached data if available
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          if (cacheName.includes('yala') || cacheName.includes('workbox')) {
            caches.delete(cacheName);
            console.log(`🗑️  Cleared cache: ${cacheName}`);
          }
        });
      });
    }
    
    console.log('✅ Browser cache clearing attempted');
  } catch (error) {
    console.error('❌ Error clearing browser cache:', error);
  }
};

/**
 * Complete session cleanup - clears all cached data and resets state
 */
export const performCompleteSessionCleanup = (): void => {
  console.log('🚀 Starting complete session cleanup...');
  
  try {
    // Clear all storage
    clearLocalStorage();
    clearSessionStorage();
    
    // Reset URL
    resetURL();
    
    // Clear browser cache
    clearBrowserCache();
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
    
    console.log('✅ Complete session cleanup finished successfully');
  } catch (error) {
    console.error('❌ Error during complete session cleanup:', error);
  }
};

/**
 * Utility to check if cleanup is needed (for debugging)
 */
export const checkStorageState = (): void => {
  console.log('📊 Current storage state:');
  console.log('localStorage keys:', Object.keys(localStorage));
  console.log('sessionStorage keys:', Object.keys(sessionStorage));
  console.log('Current URL:', window.location.href);
};
