import { formatCurrency } from './currency';
import { User } from '../types/userDefinitions';

interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  products?: {
    title: string;
    sku?: string;
  };
}

interface OrderData {
  id: string;
  order_number: string;
  customer_id: string;
  status: string;
  payment_status?: string;
  payment_method?: string;
  subtotal: number;
  delivery_fee?: number;
  discount_amount?: number;
  tax_amount?: number;
  total: number;
  created_at: string;
  delivery_address?: any;
  billing_address?: any;
  notes?: string;
  users?: {
    full_name: string;
    email: string;
    phone?: string;
  };
  order_items?: OrderItem[];
}

/**
 * Generate and download a PDF invoice for an order
 * This is a simplified implementation - in production, you'd use a proper PDF library like jsPDF or PDFKit
 */
export const generateOrderPDF = async (order: OrderData, user?: User): Promise<void> => {
  try {
    // Create HTML content for the invoice
    const htmlContent = await generateInvoiceHTML(order, user);
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('Unable to open print window. Please check your popup blocker.');
    }

    // Write the HTML content to the new window
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load, then trigger print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        // Close the window after printing (optional)
        setTimeout(() => {
          printWindow.close();
        }, 1000);
      }, 500);
    };

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

/**
 * Get payment method details for PDF generation
 */
const getPaymentMethodDetails = async (paymentMethod: string, orderTotal: number): Promise<string> => {
  try {
    // Try to get payment methods from localStorage first (faster)
    const savedPaymentMethods = localStorage.getItem('yala_payment_methods');
    let paymentMethods = null;

    if (savedPaymentMethods) {
      paymentMethods = JSON.parse(savedPaymentMethods);
    } else {
      // Fallback to importing liveDataService
      try {
        const { liveDataService } = await import('../services/liveDataService');
        paymentMethods = await liveDataService.getPaymentMethods();
      } catch (error) {
        console.error('Error loading payment methods for PDF:', error);
      }
    }

    if (!paymentMethods) {
      return `<p><strong>Payment Method:</strong> ${paymentMethod}</p>`;
    }

    switch (paymentMethod) {
      case 'bank_transfer':
        if (paymentMethods.bankTransfer?.enabled) {
          return `
            <div class="payment-details">
              <h4>Bank Transfer Details</h4>
              <p><strong>Bank:</strong> ${paymentMethods.bankTransfer.bankName || 'Banque Populaire'}</p>
              <p><strong>Account:</strong> ${paymentMethods.bankTransfer.accountNumber || '**********'}</p>
              <p><strong>IBAN:</strong> ${paymentMethods.bankTransfer.iban || 'MA64 0123 4567 8901 2345 6789 01'}</p>
              <p><strong>SWIFT:</strong> ${paymentMethods.bankTransfer.swiftCode || 'BMCEMAMC'}</p>
              <p><strong>Account Holder:</strong> ${paymentMethods.bankTransfer.accountHolder || 'YalaOffice SARL'}</p>
            </div>
          `;
        }
        break;
      case 'check':
        if (paymentMethods.check?.enabled) {
          return `
            <div class="payment-details">
              <h4>Check Payment Details</h4>
              <p><strong>Payable to:</strong> ${paymentMethods.check.payableTo || 'YalaOffice SARL'}</p>
              <p><strong>Mail to:</strong> ${paymentMethods.check.mailingAddress || 'Casablanca, Morocco'}</p>
              <p><strong>Processing:</strong> ${paymentMethods.check.processingTime || '3-5 business days'}</p>
            </div>
          `;
        }
        break;
      case 'cash':
        return `
          <div class="payment-details">
            <h4>Cash on Delivery (COD)</h4>
            <p><strong>Payment Method:</strong> Cash on Delivery</p>
            <p><strong>Instructions:</strong> Please have the exact amount ready upon delivery.</p>
            <p><strong>Note:</strong> Payment is due upon receipt of goods.</p>
            <p><strong>Total Amount Due:</strong> ${orderTotal?.toFixed(2) || '0.00'} Dh</p>
          </div>
        `;
      default:
        return `<p><strong>Payment Method:</strong> ${paymentMethod}</p>`;
    }

    return `<p><strong>Payment Method:</strong> ${paymentMethod}</p>`;
  } catch (error) {
    console.error('Error getting payment method details:', error);
    return `<p><strong>Payment Method:</strong> ${paymentMethod}</p>`;
  }
};

/**
 * Generate HTML content for the invoice
 */
const generateInvoiceHTML = async (order: OrderData, user?: User): Promise<string> => {
  const formatAddress = (address: any): string => {
    if (!address) return 'Not provided';
    if (typeof address === 'string') return address;
    if (typeof address === 'object') {
      return address.address || address.street || 'Address not formatted';
    }
    return 'Not provided';
  };

  const currentDate = new Date().toLocaleDateString('fr-MA');
  const orderDate = new Date(order.created_at).toLocaleDateString('fr-MA');

  // Get payment method details
  const paymentMethodDetails = await getPaymentMethodDetails(order.payment_method || 'cash', order.total);

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice - Order #${order.order_number}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
        }
        
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          background: white;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 3px solid #0d9488;
        }
        
        .company-info {
          flex: 1;
        }
        
        .company-name {
          font-size: 28px;
          font-weight: bold;
          color: #0d9488;
          margin-bottom: 5px;
        }
        
        .company-tagline {
          color: #f59e0b;
          font-size: 14px;
          font-style: italic;
        }
        
        .invoice-info {
          text-align: right;
        }
        
        .invoice-title {
          font-size: 24px;
          font-weight: bold;
          color: #374151;
          margin-bottom: 10px;
        }
        
        .invoice-details {
          font-size: 14px;
          color: #6b7280;
        }
        
        .section {
          margin-bottom: 25px;
        }
        
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #374151;
          margin-bottom: 10px;
          padding-bottom: 5px;
          border-bottom: 1px solid #e5e7eb;
        }
        
        .customer-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }
        
        .info-block h4 {
          font-size: 14px;
          font-weight: bold;
          color: #374151;
          margin-bottom: 8px;
        }
        
        .info-block p {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        
        .items-table th {
          background-color: #f9fafb;
          color: #374151;
          font-weight: bold;
          padding: 12px;
          text-align: left;
          border-bottom: 2px solid #e5e7eb;
          font-size: 14px;
        }
        
        .items-table td {
          padding: 12px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 14px;
        }
        
        .items-table tr:nth-child(even) {
          background-color: #f9fafb;
        }
        
        .text-right {
          text-align: right;
        }
        
        .text-center {
          text-align: center;
        }
        
        .summary-table {
          width: 300px;
          margin-left: auto;
          margin-top: 20px;
        }
        
        .summary-table td {
          padding: 8px 12px;
          font-size: 14px;
        }
        
        .summary-table .total-row {
          font-weight: bold;
          font-size: 16px;
          border-top: 2px solid #0d9488;
          background-color: #f0fdfa;
        }
        
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
          text-transform: uppercase;
        }
        
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-confirmed { background-color: #dbeafe; color: #1e40af; }
        .status-processing { background-color: #fed7aa; color: #ea580c; }
        .status-shipped { background-color: #e9d5ff; color: #7c3aed; }
        .status-delivered { background-color: #d1fae5; color: #065f46; }
        .status-cancelled { background-color: #fee2e2; color: #dc2626; }
        
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          color: #6b7280;
          font-size: 12px;
        }
        
        .notes {
          background-color: #f9fafb;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #0d9488;
        }

        .payment-details {
          background-color: #f0f9ff;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #0d9488;
          margin-top: 10px;
        }

        .payment-details h4 {
          color: #0d9488;
          margin-bottom: 10px;
          font-size: 16px;
        }
        
        @media print {
          body { margin: 0; }
          .invoice-container { box-shadow: none; }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header -->
        <div class="header">
          <div class="company-info">
            <div class="company-name">YalaOffice</div>
            <div class="company-tagline">Inventory Management System</div>
          </div>
          <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-details">
              <p><strong>Order #:</strong> ${order.order_number}</p>
              <p><strong>Date:</strong> ${currentDate}</p>
              <p><strong>Order Date:</strong> ${orderDate}</p>
            </div>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="section">
          <div class="section-title">Customer Information</div>
          <div class="customer-info">
            <div class="info-block">
              <h4>Bill To:</h4>
              <p><strong>${user?.fullName || order.users?.full_name || 'Unknown Customer'}</strong></p>
              <p>${user?.email || order.users?.email || 'No email provided'}</p>
              ${user?.isCompany && user?.companyName ? `<p><strong>Company:</strong> ${user.companyName}</p>` : ''}
              ${user?.companyAddress ? `<p><strong>Address:</strong> ${user.companyAddress}</p>` : ''}
              ${user?.city || user?.companyCity ? `<p><strong>City:</strong> ${user.city || user.companyCity}</p>` : ''}
              <p><strong>Phone:</strong> ${user?.phone || user?.companyPhone || order.users?.phone || 'No phone provided'}</p>
              ${user?.taxId ? `<p><strong>Tax ID:</strong> ${user.taxId}</p>` : ''}
              ${user?.iceNumber ? `<p><strong>ICE Number:</strong> ${user.iceNumber}</p>` : ''}
            </div>
            <div class="info-block">
              <h4>Order Status:</h4>
              <p><span class="status-badge status-${order.status}">${order.status}</span></p>
              <p><strong>Payment:</strong> ${order.payment_status || 'Pending'}</p>
              <p><strong>Method:</strong> ${order.payment_method || 'Not specified'}</p>
            </div>
          </div>
        </div>

        ${order.delivery_address ? `
        <!-- Delivery Information -->
        <div class="section">
          <div class="section-title">Delivery Information</div>
          <div class="info-block">
            <p>${formatAddress(order.delivery_address)}</p>
          </div>
        </div>
        ` : ''}

        <!-- Order Items -->
        <div class="section">
          <div class="section-title">Order Items</div>
          <table class="items-table">
            <thead>
              <tr>
                <th>Product</th>
                <th>SKU</th>
                <th class="text-center">Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Total</th>
              </tr>
            </thead>
            <tbody>
              ${order.order_items?.map(item => `
                <tr>
                  <td>${item.products?.title || 'Unknown Product'}</td>
                  <td>${item.products?.sku || 'N/A'}</td>
                  <td class="text-center">${item.quantity}</td>
                  <td class="text-right">${formatCurrency(item.unit_price)}</td>
                  <td class="text-right">${formatCurrency(item.total_price)}</td>
                </tr>
              `).join('') || '<tr><td colspan="5" class="text-center">No items found</td></tr>'}
            </tbody>
          </table>

          <!-- Order Summary -->
          <table class="summary-table">
            <tr>
              <td>Subtotal:</td>
              <td class="text-right">${formatCurrency(order.subtotal)}</td>
            </tr>
            ${order.delivery_fee && order.delivery_fee > 0 ? `
            <tr>
              <td>Delivery Fee:</td>
              <td class="text-right">${formatCurrency(order.delivery_fee)}</td>
            </tr>
            ` : ''}
            ${order.discount_amount && order.discount_amount > 0 ? `
            <tr>
              <td>Discount:</td>
              <td class="text-right" style="color: #059669;">-${formatCurrency(order.discount_amount)}</td>
            </tr>
            ` : ''}
            ${order.tax_amount && order.tax_amount > 0 ? `
            <tr>
              <td>Tax:</td>
              <td class="text-right">${formatCurrency(order.tax_amount)}</td>
            </tr>
            ` : ''}
            <tr class="total-row">
              <td><strong>Total:</strong></td>
              <td class="text-right"><strong>${formatCurrency(order.total)}</strong></td>
            </tr>
          </table>
        </div>

        <!-- Payment Information -->
        <div class="section">
          <div class="section-title">Payment Information</div>
          ${paymentMethodDetails}
        </div>

        ${order.notes ? `
        <!-- Notes -->
        <div class="section">
          <div class="section-title">Notes</div>
          <div class="notes">
            <p>${order.notes}</p>
          </div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
          <p>Thank you for your business!</p>
          <p>This invoice was generated on ${currentDate}</p>
          <p>YalaOffice - Inventory Management System</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Alternative method using jsPDF (if you want to install the library)
 * npm install jspdf html2canvas
 */
export const generateOrderPDFWithLibrary = async (order: OrderData, user?: User): Promise<void> => {
  // This would require installing jsPDF
  // import jsPDF from 'jspdf';
  // import html2canvas from 'html2canvas';

  console.log('PDF library method not implemented. Using print method instead.');
  return generateOrderPDF(order, user);
};

export default {
  generateOrderPDF,
  generateOrderPDFWithLibrary
};
