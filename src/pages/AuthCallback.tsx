/**
 * Authentication Callback Page
 * Handles email confirmation and other auth callbacks from Supabase
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing your request...');
  const [userType, setUserType] = useState<string>('client');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('🔐 Auth callback page loaded');
        console.log('URL params:', Object.fromEntries(searchParams.entries()));

        // Handle different types of auth callbacks
        const type = searchParams.get('type');
        const token = searchParams.get('token');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Check for errors first
        if (error) {
          console.error('❌ Auth callback error:', error, errorDescription);
          setStatus('error');
          setMessage(errorDescription || 'Authentication failed. Please try again.');
          return;
        }

        // Handle email confirmation
        if (type === 'signup' || token) {
          console.log('📧 Handling email confirmation callback');
          setMessage('Confirming your email address...');

          // Exchange the token for a session
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
          
          if (sessionError) {
            console.error('❌ Session error:', sessionError);
            setStatus('error');
            setMessage('Failed to confirm email. Please try clicking the confirmation link again.');
            return;
          }

          if (!sessionData.session?.user) {
            console.error('❌ No session found after callback');
            setStatus('error');
            setMessage('Email confirmation failed. Please try registering again.');
            return;
          }

          const user = sessionData.session.user;
          console.log('✅ User session confirmed:', user.id);

          // Wait for database trigger to complete
          setMessage('Creating your account...');
          await new Promise(resolve => setTimeout(resolve, 3000));

          // Check if user record exists in database
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

          if (profileError || !userProfile) {
            console.log('⚠️ User profile not found, creating manually...');
            
            // Create user profile manually
            const userMetadata = user.user_metadata || {};
            const newUserType = userMetadata.user_type || 'client';
            
            const { error: insertError } = await supabase
              .from('users')
              .insert({
                id: user.id,
                email: user.email,
                full_name: userMetadata.full_name || user.email,
                user_type: newUserType,
                phone: userMetadata.phone,
                city: userMetadata.city || 'Tetouan',
                is_active: true
              });

            if (insertError) {
              console.error('❌ Failed to create user profile:', insertError);
              setStatus('error');
              setMessage('Failed to complete registration. Please contact support.');
              return;
            }

            // Create customer profile for client/reseller users
            if (newUserType === 'client' || newUserType === 'reseller') {
              const { error: customerError } = await supabase
                .from('customer_profiles')
                .insert({
                  user_id: user.id,
                  discount_rate: newUserType === 'reseller' ? 5.0 : 0.0,
                  credit_limit: 0,
                  status: 'active'
                });

              if (customerError) {
                console.warn('⚠️ Failed to create customer profile:', customerError);
                // Don't fail the whole process for this
              }
            }

            console.log('✅ User profile created manually');
            setUserType(newUserType);
          } else {
            console.log('✅ User profile found in database');
            setUserType(userProfile.user_type);
          }

          // Fetch the complete user profile
          const { data: completeProfile, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

          if (fetchError || !completeProfile) {
            console.error('❌ Failed to fetch complete profile:', fetchError);
            setStatus('error');
            setMessage('Registration completed but failed to load profile. Please try logging in.');
            return;
          }

          // Create user object for auth context
          const userData = {
            id: completeProfile.id,
            email: completeProfile.email,
            fullName: completeProfile.full_name,
            userType: completeProfile.user_type,
            isActive: completeProfile.is_active,
            createdAt: completeProfile.created_at,
            updatedAt: completeProfile.updated_at,
            phone: completeProfile.phone || '',
            city: completeProfile.city || 'Tetouan',
            companyName: completeProfile.company_name,
            companyAddress: completeProfile.company_address,
            permissions: [] // Will be set by AuthContext
          };

          // Log the user in
          login(userData);

          setStatus('success');
          setMessage('Email confirmed successfully! Redirecting to your dashboard...');

          // Redirect to appropriate dashboard after 2 seconds
          setTimeout(() => {
            navigate('/', { replace: true });
          }, 2000);

        } else {
          // Handle other callback types or unknown callbacks
          console.log('🔄 Unknown callback type, redirecting to home');
          navigate('/', { replace: true });
        }

      } catch (error: any) {
        console.error('❌ Auth callback error:', error);
        setStatus('error');
        setMessage(error.message || 'An unexpected error occurred. Please try again.');
      }
    };

    handleAuthCallback();
  }, [searchParams, navigate, login]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {status === 'loading' && (
          <>
            <div className="mx-auto w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Loader2 className="h-8 w-8 text-teal-600 animate-spin" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Processing...</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Success!</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="text-sm text-gray-500">
              Account Type: <span className="font-medium capitalize">{userType}</span>
            </div>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error</h2>
            <p className="text-gray-600 mb-6">{message}</p>
            <div className="space-y-3">
              <button
                onClick={() => navigate('/', { replace: true })}
                className="w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-semibold"
              >
                Go to Login
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full text-teal-600 hover:text-teal-700 font-medium"
              >
                Try Again
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
