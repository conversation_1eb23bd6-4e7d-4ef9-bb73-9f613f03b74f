import React, { useState, useEffect } from 'react';
import { promoCodeService } from '../services/promoCodeService';
import { PromoCode, PromoCodesListResponse, PromoCodeStats } from '../types/promoCodes';

const PromoCodeDebugTest = () => {
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [stats, setStats] = useState<PromoCodeStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>({});

  const testGetPromoCodes = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🧪 Testing promoCodeService.getPromoCodes()...');
      
      const response: PromoCodesListResponse = await promoCodeService.getPromoCodes(
        {},
        { field: 'created_at', direction: 'desc' },
        1,
        20
      );
      
      console.log('✅ getPromoCodes response:', response);
      setPromoCodes(response.data);
      setDebugInfo(prev => ({ ...prev, getPromoCodes: response }));
    } catch (err) {
      console.error('❌ Error testing getPromoCodes:', err);
      setError(`getPromoCodes error: ${err instanceof Error ? err.message : String(err)}`);
      setDebugInfo(prev => ({ ...prev, getPromoCodesError: err }));
    } finally {
      setLoading(false);
    }
  };

  const testGetStats = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🧪 Testing promoCodeService.getPromoCodeStats()...');
      
      const statsResponse = await promoCodeService.getPromoCodeStats();
      
      console.log('✅ getPromoCodeStats response:', statsResponse);
      setStats(statsResponse);
      setDebugInfo(prev => ({ ...prev, getStats: statsResponse }));
    } catch (err) {
      console.error('❌ Error testing getPromoCodeStats:', err);
      setError(`getPromoCodeStats error: ${err instanceof Error ? err.message : String(err)}`);
      setDebugInfo(prev => ({ ...prev, getStatsError: err }));
    } finally {
      setLoading(false);
    }
  };

  const testGetActivePromoCodes = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🧪 Testing promoCodeService.getActivePromoCodes()...');
      
      const activePromoCodes = await promoCodeService.getActivePromoCodes();
      
      console.log('✅ getActivePromoCodes response:', activePromoCodes);
      setDebugInfo(prev => ({ ...prev, getActivePromoCodes: activePromoCodes }));
    } catch (err) {
      console.error('❌ Error testing getActivePromoCodes:', err);
      setError(`getActivePromoCodes error: ${err instanceof Error ? err.message : String(err)}`);
      setDebugInfo(prev => ({ ...prev, getActivePromoCodesError: err }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Auto-run tests on component mount
    testGetPromoCodes();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">🧪 Promo Code Service Debug Test</h1>
          <p className="text-gray-600 mb-6">
            This page tests the promo code service methods to debug the "No promo codes found" issue.
          </p>

          <div className="flex space-x-4 mb-6">
            <button
              onClick={testGetPromoCodes}
              disabled={loading}
              className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50"
            >
              Test getPromoCodes()
            </button>
            <button
              onClick={testGetStats}
              disabled={loading}
              className="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50"
            >
              Test getPromoCodeStats()
            </button>
            <button
              onClick={testGetActivePromoCodes}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              Test getActivePromoCodes()
            </button>
          </div>

          {loading && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Testing...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h3 className="text-red-800 font-medium">Error:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Promo Codes Results */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Promo Codes ({promoCodes.length})</h2>
            {promoCodes.length > 0 ? (
              <div className="space-y-3">
                {promoCodes.slice(0, 5).map((promo) => (
                  <div key={promo.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">{promo.code}</h3>
                        <p className="text-sm text-gray-600">{promo.name}</p>
                        <p className="text-xs text-gray-500">
                          {promo.discount_type === 'percentage' ? `${promo.discount_value}%` : `${promo.discount_value} Dh`}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        promo.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {promo.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
                {promoCodes.length > 5 && (
                  <p className="text-sm text-gray-500 text-center">... and {promoCodes.length - 5} more</p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No promo codes loaded</p>
            )}
          </div>

          {/* Stats Results */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Statistics</h2>
            {stats ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Codes:</span>
                  <span className="font-medium">{stats.total_codes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Codes:</span>
                  <span className="font-medium">{stats.active_codes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expired Codes:</span>
                  <span className="font-medium">{stats.expired_codes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Featured Codes:</span>
                  <span className="font-medium">{stats.featured_codes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Usage:</span>
                  <span className="font-medium">{stats.total_usage}</span>
                </div>
                {stats.most_used_code && (
                  <div className="pt-2 border-t border-gray-200">
                    <p className="text-sm text-gray-600">Most Used:</p>
                    <p className="font-medium">{stats.most_used_code.code} ({stats.most_used_code.usage_count} uses)</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No statistics loaded</p>
            )}
          </div>
        </div>

        {/* Debug Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Debug Information</h2>
          <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto max-h-96">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default PromoCodeDebugTest;
