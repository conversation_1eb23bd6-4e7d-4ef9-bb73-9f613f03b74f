/**
 * Password Reset Test Page
 * A simple test page to debug password reset functionality
 */

import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Building2, Mail, CheckCircle, AlertCircle } from 'lucide-react';

const PasswordResetTest: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);
    setError(null);

    try {
      console.log('🧪 Testing password reset for:', email);

      // Get the correct redirect URL
      const redirectUrl = 'https://yalaoffice.com/reset-password';
      
      console.log('Using redirect URL:', redirectUrl);

      const { data, error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });

      if (resetError) {
        console.error('❌ Password reset error:', resetError);
        setError(`Error: ${resetError.message}`);
      } else {
        console.log('✅ Password reset request sent:', data);
        setResult('Password reset email sent successfully! Check your inbox.');
      }
    } catch (err: any) {
      console.error('❌ Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 text-center">
          <div className="flex items-center justify-center space-x-3 mb-2">
            <div className="bg-gradient-to-r from-teal-600 to-amber-500 p-2 rounded-xl">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-teal-600 to-amber-500 bg-clip-text text-transparent">
              YalaOffice
            </h1>
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Password Reset Test</h2>
          <p className="text-sm text-gray-600 mt-1">Test the password reset functionality</p>
        </div>

        {/* Form */}
        <form onSubmit={handleTestReset} className="p-6">
          {/* Success Message */}
          {result && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg flex items-start space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-green-600">{result}</p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Email Input */}
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                placeholder="Enter your email address"
                required
              />
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading || !email.trim()}
            className="w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Sending Test Email...' : 'Test Password Reset'}
          </button>

          {/* Debug Info */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Debug Information:</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>Current URL:</strong> {window.location.href}</p>
              <p><strong>Redirect URL:</strong> https://yalaoffice.com/reset-password</p>
              <p><strong>Environment:</strong> {import.meta.env.MODE}</p>
            </div>
          </div>

          {/* Back to Home */}
          <div className="text-center mt-4">
            <a
              href="/"
              className="text-teal-600 hover:text-teal-700 font-medium text-sm"
            >
              Back to Homepage
            </a>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordResetTest;
