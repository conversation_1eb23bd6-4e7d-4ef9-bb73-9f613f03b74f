import { supabase } from '../integrations/supabase/client';

export interface DeliveryStatusChange {
  id: string;
  order_id: string;
  old_status: string;
  new_status: string;
  changed_by: string;
  changed_by_name: string;
  changed_by_role: string;
  change_reason?: string;
  changed_at: string;
  ip_address?: string;
  user_agent?: string;
}

export interface DeliveryStatusHistory {
  old_status: string;
  new_status: string;
  changed_at: string;
  changed_by: string;
  changed_by_name?: string;
  change_reason?: string;
}

class DeliveryStatusService {
  /**
   * Update delivery status with comprehensive audit trail
   */
  async updateDeliveryStatus(
    orderId: string,
    newStatus: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    try {
      console.log('🔄 Updating delivery status:', { orderId, newStatus, userId, reason });

      // First, check if delivery status columns exist
      const hasDeliveryColumns = await this.checkDeliveryColumns();

      if (!hasDeliveryColumns) {
        console.error('❌ Delivery status columns not found in orders table');
        throw new Error('Delivery status columns not found. Please run the database schema update first.');
      }

      // Get current order status
      const { data: currentOrder, error: fetchError } = await supabase
        .from('orders')
        .select('delivery_status, delivery_status_history')
        .eq('id', orderId)
        .single();

      if (fetchError) {
        console.error('❌ Error fetching current order:', fetchError);

        // If column doesn't exist, provide helpful error
        if (fetchError.message.includes('column') && fetchError.message.includes('does not exist')) {
          throw new Error('Delivery status columns not found. Please run the enhanced delivery status schema script first.');
        }

        throw fetchError;
      }

      const oldStatus = currentOrder.delivery_status || 'not_assigned';

      // Don't update if status is the same
      if (oldStatus === newStatus) {
        console.log('ℹ️  Status unchanged, skipping update');
        return;
      }

      // Get user information for audit trail
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('full_name, user_type')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('❌ Error fetching user data:', userError);
        throw userError;
      }

      // Update the order with new delivery status
      const { error: updateError } = await supabase
        .from('orders')
        .update({
          delivery_status: newStatus,
          delivery_status_updated_by: userId,
          delivery_status_updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (updateError) {
        console.error('❌ Error updating order delivery status:', updateError);
        throw updateError;
      }

      // The trigger will automatically handle the audit trail, but we can also
      // manually insert into the detailed log for additional information
      const { error: logError } = await supabase
        .from('delivery_status_changes')
        .insert({
          order_id: orderId,
          old_status: oldStatus,
          new_status: newStatus,
          changed_by: userId,
          changed_by_name: userData.full_name,
          changed_by_role: userData.user_type,
          change_reason: reason,
          changed_at: new Date().toISOString()
        });

      if (logError) {
        console.error('❌ Error logging delivery status change:', logError);
        // Don't throw here as the main update succeeded
      }

      console.log('✅ Delivery status updated successfully:', {
        orderId,
        oldStatus,
        newStatus,
        changedBy: userData.full_name
      });

    } catch (error) {
      console.error('❌ Error in updateDeliveryStatus:', error);
      throw error;
    }
  }

  /**
   * Check if delivery status columns exist in orders table
   */
  private async checkDeliveryColumns(): Promise<boolean> {
    try {
      // Try to query the delivery status columns
      const { error } = await supabase
        .from('orders')
        .select('delivery_status, delivery_status_updated_by, delivery_status_updated_at')
        .limit(1);

      if (error) {
        console.log('⚠️  Delivery status columns not found:', error.message);
        return false;
      }

      return true;
    } catch (error) {
      console.log('⚠️  Error checking delivery columns:', error);
      return false;
    }
  }

  /**
   * Get delivery status history for an order
   */
  async getDeliveryStatusHistory(orderId: string): Promise<DeliveryStatusChange[]> {
    try {
      const { data, error } = await supabase
        .from('delivery_status_changes')
        .select('*')
        .eq('order_id', orderId)
        .order('changed_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching delivery status history:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error in getDeliveryStatusHistory:', error);
      throw error;
    }
  }

  /**
   * Get delivery status statistics
   */
  async getDeliveryStatusStats(): Promise<Record<string, number>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('delivery_status');

      if (error) {
        console.error('❌ Error fetching delivery status stats:', error);
        throw error;
      }

      const stats: Record<string, number> = {
        not_assigned: 0,
        assigned: 0,
        picked: 0,
        out_for_delivery: 0,
        delivered: 0,
        failed: 0,
        returned: 0
      };

      data?.forEach(order => {
        const status = order.delivery_status || 'not_assigned';
        stats[status] = (stats[status] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('❌ Error in getDeliveryStatusStats:', error);
      throw error;
    }
  }

  /**
   * Get recent delivery status changes across all orders
   */
  async getRecentDeliveryStatusChanges(limit: number = 50): Promise<DeliveryStatusChange[]> {
    try {
      const { data, error } = await supabase
        .from('delivery_status_changes')
        .select(`
          *,
          orders!delivery_status_changes_order_id_fkey (
            order_number,
            users!orders_customer_id_fkey (
              full_name
            )
          )
        `)
        .order('changed_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Error fetching recent delivery status changes:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error in getRecentDeliveryStatusChanges:', error);
      throw error;
    }
  }

  /**
   * Validate delivery status transition
   */
  validateStatusTransition(currentStatus: string, newStatus: string, userRole: string): boolean {
    const statusFlow = {
      not_assigned: ['assigned'],
      assigned: ['picked', 'failed', 'returned'],
      picked: ['out_for_delivery', 'failed', 'returned'],
      out_for_delivery: ['delivered', 'failed', 'returned'],
      delivered: [], // Final state
      failed: ['assigned', 'returned'],
      returned: ['assigned']
    };

    // Admins and managers can make any transition
    if (userRole === 'admin' || userRole === 'manager') {
      return true;
    }

    // Delivery persons can only follow the normal flow
    if (userRole === 'delivery_person') {
      return statusFlow[currentStatus as keyof typeof statusFlow]?.includes(newStatus) || false;
    }

    // Other roles cannot change status
    return false;
  }

  /**
   * Get delivery status label for display
   */
  getStatusLabel(status: string): string {
    const labels: Record<string, string> = {
      not_assigned: 'Not Assigned',
      assigned: 'Assigned',
      picked: 'Picked',
      out_for_delivery: 'Out for Delivery',
      delivered: 'Delivered',
      failed: 'Failed',
      returned: 'Returned'
    };

    return labels[status] || status;
  }

  /**
   * Get delivery status color for UI
   */
  getStatusColor(status: string): string {
    const colors: Record<string, string> = {
      not_assigned: 'bg-gray-100 text-gray-800',
      assigned: 'bg-blue-100 text-blue-800',
      picked: 'bg-yellow-100 text-yellow-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      delivered: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      returned: 'bg-purple-100 text-purple-800'
    };

    return colors[status] || 'bg-gray-100 text-gray-800';
  }
}

export const deliveryStatusService = new DeliveryStatusService();
