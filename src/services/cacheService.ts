/**
 * Cache Service for YalaOffice
 * Implements intelligent caching to reduce database queries and improve performance
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

class CacheService {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private stats: CacheStats = { hits: 0, misses: 0, size: 0, hitRate: 0 };
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 1000; // Maximum number of entries

  constructor() {
    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  /**
   * Get data from cache or execute fetch function
   */
  async get<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = this.DEFAULT_TTL
  ): Promise<T> {
    const cacheKey = this.normalizeKey(key);
    const entry = this.cache.get(cacheKey);

    // Check if cache entry exists and is still valid
    if (entry && this.isValid(entry)) {
      this.stats.hits++;
      this.updateStats();
      console.log(`Cache HIT for key: ${cacheKey}`);
      return entry.data;
    }

    // Cache miss - fetch fresh data
    this.stats.misses++;
    console.log(`Cache MISS for key: ${cacheKey}`);
    
    try {
      const data = await fetchFunction();
      this.set(cacheKey, data, ttl);
      this.updateStats();
      return data;
    } catch (error) {
      console.error(`Error fetching data for cache key ${cacheKey}:`, error);
      throw error;
    }
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    const cacheKey = this.normalizeKey(key);
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldest();
    }

    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl
    });

    this.stats.size = this.cache.size;
    console.log(`Cache SET for key: ${cacheKey}, TTL: ${ttl}ms`);
  }

  /**
   * Remove specific key from cache
   */
  invalidate(key: string): void {
    const cacheKey = this.normalizeKey(key);
    const deleted = this.cache.delete(cacheKey);
    if (deleted) {
      this.stats.size = this.cache.size;
      console.log(`Cache INVALIDATED for key: ${cacheKey}`);
    }
  }

  /**
   * Remove all keys matching a pattern
   */
  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    let deletedCount = 0;

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    this.stats.size = this.cache.size;
    console.log(`Cache INVALIDATED ${deletedCount} keys matching pattern: ${pattern}`);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats.size = 0;
    console.log('Cache CLEARED');
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Check if cache entry is still valid
   */
  private isValid(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  /**
   * Normalize cache key
   */
  private normalizeKey(key: string): string {
    return key.toLowerCase().trim();
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= entry.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.stats.size = this.cache.size;
      console.log(`Cache CLEANUP: Removed ${cleanedCount} expired entries`);
    }
  }

  /**
   * Evict oldest entries when cache is full
   */
  private evictOldest(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`Cache EVICTED oldest entry: ${oldestKey}`);
    }
  }

  /**
   * Generate cache key for paginated data
   */
  static generatePaginationKey(
    baseKey: string,
    page: number,
    limit: number,
    filters?: Record<string, any>
  ): string {
    const filterString = filters ? JSON.stringify(filters) : '';
    return `${baseKey}:page:${page}:limit:${limit}:filters:${filterString}`;
  }

  /**
   * Generate cache key for specific resource
   */
  static generateResourceKey(resource: string, id: string): string {
    return `${resource}:${id}`;
  }
}

// Create singleton instance
export const cacheService = new CacheService();

// Cache key constants
export const CACHE_KEYS = {
  PRODUCTS: 'products',
  ORDERS: 'orders',
  CUSTOMERS: 'customers',
  USERS: 'users',
  CATEGORIES: 'categories',
  BRANCHES: 'branches',
  DASHBOARD_STATS: 'dashboard_stats',
  REVIEWS: 'reviews'
} as const;

// Cache TTL constants (in milliseconds)
export const CACHE_TTL = {
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
} as const;

export default cacheService;
