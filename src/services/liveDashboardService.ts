/**
 * Live Dashboard Service for YalaOffice
 * Provides real-time dashboard statistics from Supabase database
 */

import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

export interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalCustomers: number;
  totalRevenue: number;
  lowStockProducts: number;
  pendingOrders: number;
  completedOrders: number;
  processingOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  todaysSales: number;
  thisMonthSales: number;
  totalUsers: number;
  activeUsers: number;
  totalPromoCodes: number;
  activePromoCodes: number;
  totalPromoUsage: number;
  totalBranches: number;
  activeBranches: number;
  pendingTransfers: number;
  inventoryItems: number;
  assignedDeliveries: number;
  unassignedOrders: number;
  recentOrders: any[];
  topProducts: any[];
  salesTrend: any[];
}

export interface BranchStats {
  totalProducts: number;
  lowStockItems: number;
  pendingOrders: number;
  todaysSales: number;
  completedToday: number;
  branchRevenue: number;
}

export interface StoreManagerStats {
  totalProducts: number;
  lowStockItems: number;
  pendingOrders: number;
  completedToday: number;
  processingOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  totalOrders: number;
  totalCustomers: number;
  inventoryItems: number;
  recentOrders: any[];
  topProducts: any[];
}

export interface DeliveryStats {
  assignedOrders: number;
  completedToday: number;
  inTransit: number;
  totalDistance: number;
  pendingDeliveries: number;
  deliveredThisWeek: number;
}

export interface ClientStats {
  totalOrders: number;
  totalSpent: number;
  pendingOrders: number;
  completedOrders: number;
  favoriteProducts: any[];
  recentOrders: any[];
  availableCredit: number;
  loyaltyPoints: number;
}

export class LiveDashboardService {
  private static instance: LiveDashboardService;

  private constructor() {}

  static getInstance(): LiveDashboardService {
    if (!LiveDashboardService.instance) {
      LiveDashboardService.instance = new LiveDashboardService();
    }
    return LiveDashboardService.instance;
  }

  /**
   * Get comprehensive admin dashboard statistics with enhanced error handling
   */
  async getAdminDashboardStats(): Promise<DashboardStats> {
    try {
      console.log('LiveDashboardService: Fetching admin dashboard stats...');
      const today = new Date().toISOString().split('T')[0];
      const thisMonth = new Date().toISOString().slice(0, 7);

      // Use RPC functions for better performance and to avoid RLS issues
      const [
        productsResult,
        ordersResult,
        customersResult,
        completedOrdersResult,
        pendingOrdersResult,
        usersStatsResult,
        branchesResult,
        todayOrdersResult,
        monthOrdersResult,
        lowStockResult,
        recentOrdersResult,
        topProductsResult,
        pendingTransfersResult,
        inventoryItemsResult,
        processingOrdersResult,
        shippedOrdersResult,
        deliveredOrdersResult,
        cancelledOrdersResult,
        assignedDeliveriesResult,
        unassignedOrdersResult,
        totalPromoCodesResult,
        activePromoCodesResult,
        promoUsageResult
      ] = await Promise.all([
        // Total products - use direct query with better error handling
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),

        // Total orders - handle missing orders table gracefully
        supabase.from('orders').select('id', { count: 'exact' }).limit(1),

        // Total customers - use RPC function if available
        supabase.rpc('get_user_statistics').then(result => result).catch(() =>
          supabase.from('users').select('id', { count: 'exact' }).in('user_type', ['client', 'reseller']).eq('is_active', true)
        ),

        // Completed orders with revenue - handle missing orders gracefully
        supabase.from('orders').select('total').eq('status', 'completed').limit(100).then(result => result).catch(() => ({ data: [], error: null })),

        // Pending orders - handle missing orders gracefully
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'pending').then(result => result).catch(() => ({ count: 0, error: null })),

        // Total users - use RPC function for better performance
        supabase.rpc('get_user_statistics').then(result => result).catch(() =>
          supabase.from('users').select('id, is_active', { count: 'exact' })
        ),

        // Total branches - handle missing branches table gracefully (fetch ALL branches, not just active)
        supabase.from('branches').select('id, is_active', { count: 'exact' }).then(result => {
          console.log('LiveDashboardService: Branches query result:', result);
          return result;
        }).catch(() => ({ count: 0, data: [], error: null })),

        // Today's orders - handle missing orders gracefully
        supabase.from('orders').select('total').gte('created_at', today).eq('status', 'completed').then(result => result).catch(() => ({ data: [], error: null })),

        // This month's orders - handle missing orders gracefully
        supabase.from('orders').select('total').gte('created_at', thisMonth + '-01').eq('status', 'completed').then(result => result).catch(() => ({ data: [], error: null })),

        // Low stock products
        supabase.from('products').select('id', { count: 'exact' }).lt('stock', 'min_stock').eq('is_active', true),

        // Recent orders - handle missing orders gracefully
        supabase.from('orders').select(`
          id, order_number, total, status, created_at,
          users!orders_customer_id_fkey (full_name, email)
        `).order('created_at', { ascending: false }).limit(5).then(result => result).catch(() => ({ data: [], error: null })),

        // Top products by stock
        supabase.from('products').select(`
          id, title, stock, price,
          categories (name)
        `).eq('is_active', true).order('stock', { ascending: false }).limit(5),

        // Pending transfers - handle missing stock_transfers table gracefully
        supabase.from('stock_transfers').select('id', { count: 'exact' }).eq('status', 'pending').then(result => result).catch(() => ({ count: 0, error: null })),

        // Total inventory items (all products) - handle missing products table gracefully
        supabase.from('products').select('id', { count: 'exact' }).then(result => result).catch(() => ({ count: 0, error: null })),

        // Additional order statistics
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'processing').then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'shipped').then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'delivered').then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'cancelled').then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('orders').select('id', { count: 'exact' }).not('assigned_delivery_person', 'is', null).then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('orders').select('id', { count: 'exact' }).is('assigned_delivery_person', null).then(result => result).catch(() => ({ count: 0, error: null })),

        // Promo codes statistics
        supabase.from('promo_codes').select('id', { count: 'exact' }).then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('promo_codes').select('id', { count: 'exact' }).eq('is_active', true).then(result => result).catch(() => ({ count: 0, error: null })),
        supabase.from('promo_codes').select('used_count').then(result => result).catch(() => ({ data: [], error: null }))
      ]);

      // Enhanced error handling and data processing
      console.log('LiveDashboardService: Query results:', {
        products: { count: productsResult.count, error: productsResult.error },
        orders: { count: ordersResult.count, error: ordersResult.error },
        customers: { count: typeof customersResult === 'object' && 'count' in customersResult ? customersResult.count : 0, error: customersResult.error },
        users: { count: typeof usersStatsResult === 'object' && 'count' in usersStatsResult ? usersStatsResult.count : 0, error: usersStatsResult.error },
        branches: { count: branchesResult.count, error: branchesResult.error },
        pendingTransfers: { count: pendingTransfersResult.count, error: pendingTransfersResult.error },
        inventoryItems: { count: inventoryItemsResult.count, error: inventoryItemsResult.error },
        processingOrders: { count: processingOrdersResult.count, error: processingOrdersResult.error },
        shippedOrders: { count: shippedOrdersResult.count, error: shippedOrdersResult.error },
        deliveredOrders: { count: deliveredOrdersResult.count, error: deliveredOrdersResult.error },
        cancelledOrders: { count: cancelledOrdersResult.count, error: cancelledOrdersResult.error },
        assignedDeliveries: { count: assignedDeliveriesResult.count, error: assignedDeliveriesResult.error },
        unassignedOrders: { count: unassignedOrdersResult.count, error: unassignedOrdersResult.error }
      });

      // Process user statistics from RPC function or direct query
      let userStats = { total: 0, active: 0, customers: 0 };
      if (usersStatsResult && typeof usersStatsResult === 'object') {
        if ('data' in usersStatsResult && usersStatsResult.data) {
          // RPC function result
          const rpcData = usersStatsResult.data;
          userStats = {
            total: rpcData.total || 0,
            active: rpcData.active || 0,
            customers: (rpcData.clients || 0) + (rpcData.resellers || 0)
          };
        } else if ('count' in usersStatsResult) {
          // Direct query result
          userStats = {
            total: usersStatsResult.count || 0,
            active: usersStatsResult.data?.filter((u: any) => u.is_active).length || 0,
            customers: 0
          };
        }
      }

      // Process customer count
      let customerCount = 0;
      if (typeof customersResult === 'object' && 'count' in customersResult) {
        customerCount = customersResult.count || 0;
      } else if (userStats.customers > 0) {
        customerCount = userStats.customers;
      }

      // Calculate revenue and sales with error handling
      const totalRevenue = completedOrdersResult.data?.reduce((sum: number, order: any) => sum + (order.total || 0), 0) || 0;
      const todaysSales = todayOrdersResult.data?.reduce((sum: number, order: any) => sum + (order.total || 0), 0) || 0;
      const thisMonthSales = monthOrdersResult.data?.reduce((sum: number, order: any) => sum + (order.total || 0), 0) || 0;

      // Generate sales trend (last 7 days) with error handling
      let salesTrend = [];
      try {
        salesTrend = await this.getSalesTrend(7);
      } catch (error) {
        console.warn('Could not fetch sales trend:', error);
        salesTrend = [];
      }

      const totalBranches = branchesResult.count || 0;
      const activeBranches = branchesResult.data?.filter((b: any) => b.is_active).length || 0;

      console.log('LiveDashboardService: Branch statistics calculated:', {
        totalBranches,
        activeBranches,
        branchesData: branchesResult.data,
        branchesCount: branchesResult.count
      });

      // Calculate promo codes statistics
      const totalPromoUsage = promoUsageResult.data?.reduce((sum: number, promo: any) => sum + (promo.used_count || 0), 0) || 0;

      const stats = {
        totalProducts: productsResult.count || 0,
        totalOrders: ordersResult.count || 0,
        totalCustomers: customerCount,
        totalRevenue,
        lowStockProducts: lowStockResult.count || 0,
        pendingOrders: pendingOrdersResult.count || 0,
        completedOrders: completedOrdersResult.data?.length || 0,
        processingOrders: processingOrdersResult.count || 0,
        shippedOrders: shippedOrdersResult.count || 0,
        deliveredOrders: deliveredOrdersResult.count || 0,
        cancelledOrders: cancelledOrdersResult.count || 0,
        todaysSales,
        thisMonthSales,
        totalUsers: userStats.total,
        activeUsers: userStats.active,
        totalBranches,
        activeBranches,
        pendingTransfers: pendingTransfersResult.count || 0,
        inventoryItems: inventoryItemsResult.count || 0,
        assignedDeliveries: assignedDeliveriesResult.count || 0,
        unassignedOrders: unassignedOrdersResult.count || 0,
        totalPromoCodes: totalPromoCodesResult.count || 0,
        activePromoCodes: activePromoCodesResult.count || 0,
        totalPromoUsage,
        recentOrders: recentOrdersResult.data || [],
        topProducts: topProductsResult.data || [],
        salesTrend
      };

      console.log('LiveDashboardService: Final enhanced stats:', stats);
      return stats;
    } catch (error) {
      console.error('Error fetching admin dashboard stats:', error);
      return this.getEmptyDashboardStats();
    }
  }

  /**
   * Get manager/branch dashboard statistics
   */
  async getBranchDashboardStats(branchId?: string): Promise<BranchStats> {
    try {
      const today = new Date().toISOString().split('T')[0];

      const [
        productsResult,
        lowStockResult,
        pendingOrdersResult,
        todayOrdersResult,
        completedTodayResult
      ] = await Promise.all([
        // Total products (branch-specific if branchId provided)
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),
        
        // Low stock items
        supabase.from('products').select('id', { count: 'exact' }).lt('stock', 'min_stock').eq('is_active', true),
        
        // Pending orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'pending'),
        
        // Today's sales
        supabase.from('orders').select('total').gte('created_at', today).eq('status', 'completed'),
        
        // Completed today
        supabase.from('orders').select('id', { count: 'exact' }).gte('created_at', today).eq('status', 'completed')
      ]);

      const todaysSales = todayOrdersResult.data?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;

      return {
        totalProducts: productsResult.count || 0,
        lowStockItems: lowStockResult.count || 0,
        pendingOrders: pendingOrdersResult.count || 0,
        todaysSales,
        completedToday: completedTodayResult.count || 0,
        branchRevenue: todaysSales
      };
    } catch (error) {
      console.error('Error fetching branch dashboard stats:', error);
      return {
        totalProducts: 0,
        lowStockItems: 0,
        pendingOrders: 0,
        todaysSales: 0,
        completedToday: 0,
        branchRevenue: 0
      };
    }
  }

  /**
   * Get Store Manager dashboard statistics (excludes financial data)
   */
  async getStoreManagerDashboardStats(branchId?: string): Promise<StoreManagerStats> {
    try {
      console.log('LiveDashboardService: Fetching Store Manager dashboard stats...');

      const [
        productsResult,
        lowStockResult,
        pendingOrdersResult,
        completedTodayResult,
        processingOrdersResult,
        shippedOrdersResult,
        deliveredOrdersResult,
        cancelledOrdersResult,
        totalOrdersResult,
        customersResult,
        inventoryItemsResult,
        recentOrdersResult,
        topProductsResult
      ] = await Promise.all([
        // Total products (branch-specific if branchId provided)
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),

        // Low stock items
        supabase.from('products').select('id', { count: 'exact' }).lt('stock', 'min_stock').eq('is_active', true),

        // Pending orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'pending'),

        // Completed today
        supabase.from('orders').select('id', { count: 'exact' })
          .gte('created_at', new Date().toISOString().split('T')[0])
          .eq('status', 'completed'),

        // Processing orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'processing'),

        // Shipped orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'shipped'),

        // Delivered orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'delivered'),

        // Cancelled orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'cancelled'),

        // Total orders
        supabase.from('orders').select('id', { count: 'exact' }),

        // Total customers
        supabase.from('users').select('id', { count: 'exact' })
          .in('user_type', ['client', 'reseller'])
          .eq('is_active', true),

        // Inventory items
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),

        // Recent orders (last 10)
        supabase.from('orders').select(`
          id, order_number, status, created_at,
          users!orders_customer_id_fkey (
            full_name, email
          ),
          order_items (
            quantity,
            products (title, featured_image)
          )
        `).order('created_at', { ascending: false }).limit(10),

        // Top products by stock level (as proxy for popularity)
        supabase.from('products').select(`
          id, title, stock, price, featured_image
        `).eq('is_active', true).order('stock', { ascending: false }).limit(5)
      ]);

      console.log('LiveDashboardService: Store Manager stats loaded successfully');

      return {
        totalProducts: productsResult.count || 0,
        lowStockItems: lowStockResult.count || 0,
        pendingOrders: pendingOrdersResult.count || 0,
        completedToday: completedTodayResult.count || 0,
        processingOrders: processingOrdersResult.count || 0,
        shippedOrders: shippedOrdersResult.count || 0,
        deliveredOrders: deliveredOrdersResult.count || 0,
        cancelledOrders: cancelledOrdersResult.count || 0,
        totalOrders: totalOrdersResult.count || 0,
        totalCustomers: customersResult.count || 0,
        inventoryItems: inventoryItemsResult.count || 0,
        recentOrders: recentOrdersResult.data || [],
        topProducts: topProductsResult.data || []
      };
    } catch (error) {
      console.error('Error fetching Store Manager dashboard stats:', error);
      return {
        totalProducts: 0,
        lowStockItems: 0,
        pendingOrders: 0,
        completedToday: 0,
        processingOrders: 0,
        shippedOrders: 0,
        deliveredOrders: 0,
        cancelledOrders: 0,
        totalOrders: 0,
        totalCustomers: 0,
        inventoryItems: 0,
        recentOrders: [],
        topProducts: []
      };
    }
  }

  /**
   * Get delivery dashboard statistics
   */
  async getDeliveryDashboardStats(deliveryPersonId?: string): Promise<DeliveryStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const [
        assignedResult,
        completedTodayResult,
        inTransitResult,
        deliveredWeekResult
      ] = await Promise.all([
        // Assigned orders (pending delivery)
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'shipped'),
        
        // Completed today
        supabase.from('orders').select('id', { count: 'exact' }).gte('created_at', today).eq('status', 'delivered'),
        
        // In transit
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'shipped'),
        
        // Delivered this week
        supabase.from('orders').select('id', { count: 'exact' }).gte('created_at', weekAgo).eq('status', 'delivered')
      ]);

      return {
        assignedOrders: assignedResult.count || 0,
        completedToday: completedTodayResult.count || 0,
        inTransit: inTransitResult.count || 0,
        totalDistance: 45, // This would need GPS tracking integration
        pendingDeliveries: assignedResult.count || 0,
        deliveredThisWeek: deliveredWeekResult.count || 0
      };
    } catch (error) {
      console.error('Error fetching delivery dashboard stats:', error);
      return {
        assignedOrders: 0,
        completedToday: 0,
        inTransit: 0,
        totalDistance: 0,
        pendingDeliveries: 0,
        deliveredThisWeek: 0
      };
    }
  }

  /**
   * Get client dashboard statistics
   */
  async getClientDashboardStats(userId: string): Promise<ClientStats> {
    try {
      const [
        ordersResult,
        completedOrdersResult,
        pendingOrdersResult,
        customerProfileResult,
        recentOrdersResult
      ] = await Promise.all([
        // Total orders
        supabase.from('orders').select('total').eq('customer_id', userId),
        
        // Completed orders
        supabase.from('orders').select('total').eq('customer_id', userId).eq('status', 'completed'),
        
        // Pending orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('customer_id', userId).eq('status', 'pending'),
        
        // Customer profile
        supabase.from('customer_profiles').select('*').eq('user_id', userId).single(),
        
        // Recent orders
        supabase.from('orders').select(`
          id, order_number, total, status, created_at,
          order_items (
            quantity,
            products (title, featured_image)
          )
        `).eq('customer_id', userId).order('created_at', { ascending: false }).limit(5)
      ]);

      const totalSpent = completedOrdersResult.data?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;
      const profile = customerProfileResult.data;

      return {
        totalOrders: ordersResult.data?.length || 0,
        totalSpent,
        pendingOrders: pendingOrdersResult.count || 0,
        completedOrders: completedOrdersResult.data?.length || 0,
        favoriteProducts: [], // Would need favorites tracking
        recentOrders: recentOrdersResult.data || [],
        availableCredit: (profile?.credit_limit || 0) - totalSpent,
        loyaltyPoints: profile?.loyalty_points || 0
      };
    } catch (error) {
      console.error('Error fetching client dashboard stats:', error);
      return {
        totalOrders: 0,
        totalSpent: 0,
        pendingOrders: 0,
        completedOrders: 0,
        favoriteProducts: [],
        recentOrders: [],
        availableCredit: 0,
        loyaltyPoints: 0
      };
    }
  }

  /**
   * Get sales trend data for charts
   */
  private async getSalesTrend(days: number): Promise<any[]> {
    try {
      const salesTrend = [];
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const { data } = await supabase
          .from('orders')
          .select('total')
          .gte('created_at', dateStr)
          .lt('created_at', new Date(date.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0])
          .eq('status', 'completed');

        const dayTotal = data?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;
        
        salesTrend.push({
          date: dateStr,
          sales: dayTotal,
          day: date.toLocaleDateString('en-US', { weekday: 'short' })
        });
      }
      
      return salesTrend;
    } catch (error) {
      console.error('Error fetching sales trend:', error);
      return [];
    }
  }

  /**
   * Get empty dashboard stats as fallback
   */
  private getEmptyDashboardStats(): DashboardStats {
    return {
      totalProducts: 0,
      totalOrders: 0,
      totalCustomers: 0,
      totalRevenue: 0,
      lowStockProducts: 0,
      pendingOrders: 0,
      completedOrders: 0,
      processingOrders: 0,
      shippedOrders: 0,
      deliveredOrders: 0,
      cancelledOrders: 0,
      todaysSales: 0,
      thisMonthSales: 0,
      totalUsers: 0,
      activeUsers: 0,
      totalBranches: 0,
      activeBranches: 0,
      pendingTransfers: 0,
      inventoryItems: 0,
      assignedDeliveries: 0,
      unassignedOrders: 0,
      totalPromoCodes: 0,
      activePromoCodes: 0,
      totalPromoUsage: 0,
      recentOrders: [],
      topProducts: [],
      salesTrend: []
    };
  }

  /**
   * Subscribe to real-time dashboard updates
   */
  subscribeToUpdates(callback: (payload: any) => void) {
    const channels = [
      supabase.channel('dashboard-orders').on('postgres_changes',
        { event: '*', schema: 'public', table: 'orders' }, callback),
      supabase.channel('dashboard-products').on('postgres_changes',
        { event: '*', schema: 'public', table: 'products' }, callback),
      supabase.channel('dashboard-users').on('postgres_changes',
        { event: '*', schema: 'public', table: 'users' }, callback),
      supabase.channel('dashboard-branches').on('postgres_changes',
        { event: '*', schema: 'public', table: 'branches' }, callback),
      supabase.channel('dashboard-categories').on('postgres_changes',
        { event: '*', schema: 'public', table: 'categories' }, callback),
      supabase.channel('dashboard-transfers').on('postgres_changes',
        { event: '*', schema: 'public', table: 'stock_transfers' }, callback),
      supabase.channel('dashboard-order-items').on('postgres_changes',
        { event: '*', schema: 'public', table: 'order_items' }, callback)
    ];

    channels.forEach(channel => channel.subscribe());

    return {
      unsubscribe: () => {
        channels.forEach(channel => channel.unsubscribe());
      }
    };
  }
}

// Export singleton instance
export const liveDashboardService = LiveDashboardService.getInstance();
export default liveDashboardService;
