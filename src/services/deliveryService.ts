import { supabase } from '../integrations/supabase/client';
import { realTimeService, syncDeliveryStatusUpdate, syncDeliveryCompleted } from './realTimeService';

export interface DeliveryOrder {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  delivery_address: any;
  total: number;
  status: string;
  priority: 'high' | 'medium' | 'low';
  created_at: string;
  delivery_date: string;
  delivery_person_id?: string;
  items_count: number;
  order_items: Array<{
    quantity: number;
    products: {
      title: string;
      featured_image: string;
    };
  }>;
}

export interface DeliveryRoute {
  id: string;
  name: string;
  delivery_person_id: string;
  orders: string[];
  estimated_time: string;
  distance: string;
  status: 'planned' | 'active' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface DeliveryStats {
  assignedOrders: number;
  completedToday: number;
  inTransit: number;
  totalDistance: number;
  pendingDeliveries: number;
  deliveredThisWeek: number;
}

class DeliveryService {
  /**
   * Get assigned orders for a delivery person
   */
  async getAssignedOrders(deliveryPersonId: string): Promise<DeliveryOrder[]> {
    try {
      console.log('Fetching assigned orders for delivery person:', deliveryPersonId);

      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total,
          status,
          created_at,
          delivery_address,
          assigned_delivery_person,
          delivery_person_name,
          users!orders_customer_id_fkey (
            full_name,
            phone
          ),
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .eq('assigned_delivery_person', deliveryPersonId)
        .in('status', ['assigned', 'picked', 'out_for_delivery'])
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching assigned orders:', error);
        console.error('Error details:', error);
        return [];
      }

      console.log('Raw assigned orders data:', data);
      console.log('Number of assigned orders found:', data?.length || 0);

      return (data || []).map(order => ({
        id: order.id,
        order_number: order.order_number,
        customer_name: order.users?.full_name || 'Unknown Customer',
        customer_phone: order.users?.phone || 'No phone',
        delivery_address: order.delivery_address,
        total: order.total,
        status: order.status,
        priority: 'medium' as const, // Default priority since it's not in schema
        created_at: order.created_at,
        delivery_date: order.created_at, // Use created_at as delivery_date fallback
        delivery_person_id: order.assigned_delivery_person,
        items_count: order.order_items?.reduce((sum, item) => sum + item.quantity, 0) || 0,
        order_items: order.order_items || []
      }));
    } catch (error) {
      console.error('Error in getAssignedOrders:', error);
      return [];
    }
  }

  /**
   * Update order delivery status
   */
  async updateOrderStatus(orderId: string, newStatus: string, deliveryPersonId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('orders')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString(),
          ...(newStatus === 'delivered' && { delivered_at: new Date().toISOString() })
        })
        .eq('id', orderId)
        .eq('assigned_delivery_person', deliveryPersonId);

      if (error) {
        console.error('Error updating order status:', error);
        return false;
      }

      // Emit real-time events for cross-user synchronization
      if (newStatus === 'delivered') {
        syncDeliveryCompleted(orderId, deliveryPersonId, new Date().toISOString());
      } else {
        syncDeliveryStatusUpdate(orderId, newStatus, deliveryPersonId);
      }

      return true;
    } catch (error) {
      console.error('Error in updateOrderStatus:', error);
      return false;
    }
  }

  /**
   * Get delivery history for a delivery person
   */
  async getDeliveryHistory(deliveryPersonId: string, limit: number = 20): Promise<DeliveryOrder[]> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          customer_name,
          customer_phone,
          delivery_address,
          total,
          status,
          priority,
          created_at,
          delivery_date,
          delivered_at,
          assigned_delivery_person,
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .eq('assigned_delivery_person', deliveryPersonId)
        .eq('status', 'delivered')
        .order('delivered_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching delivery history:', error);
        return [];
      }

      return (data || []).map(order => ({
        ...order,
        items_count: order.order_items?.reduce((sum, item) => sum + item.quantity, 0) || 0
      }));
    } catch (error) {
      console.error('Error in getDeliveryHistory:', error);
      return [];
    }
  }

  /**
   * Get delivery statistics for a delivery person
   */
  async getDeliveryStats(deliveryPersonId: string): Promise<DeliveryStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();

      const [assignedResult, completedTodayResult, inTransitResult, deliveredWeekResult] = await Promise.all([
        // Assigned orders
        supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('assigned_delivery_person', deliveryPersonId)
          .in('status', ['assigned', 'picked', 'out_for_delivery']),

        // Completed today
        supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('assigned_delivery_person', deliveryPersonId)
          .eq('status', 'delivered')
          .gte('delivered_at', today),

        // In transit
        supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('assigned_delivery_person', deliveryPersonId)
          .eq('status', 'out_for_delivery'),

        // Delivered this week
        supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('assigned_delivery_person', deliveryPersonId)
          .eq('status', 'delivered')
          .gte('delivered_at', weekAgo)
      ]);

      return {
        assignedOrders: assignedResult.count || 0,
        completedToday: completedTodayResult.count || 0,
        inTransit: inTransitResult.count || 0,
        totalDistance: 0, // Would need GPS tracking integration
        pendingDeliveries: assignedResult.count || 0,
        deliveredThisWeek: deliveredWeekResult.count || 0
      };
    } catch (error) {
      console.error('Error fetching delivery stats:', error);
      return {
        assignedOrders: 0,
        completedToday: 0,
        inTransit: 0,
        totalDistance: 0,
        pendingDeliveries: 0,
        deliveredThisWeek: 0
      };
    }
  }

  /**
   * Subscribe to real-time order updates
   */
  subscribeToOrderUpdates(deliveryPersonId: string, callback: (payload: any) => void) {
    return supabase
      .channel('delivery-orders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
          filter: `assigned_delivery_person=eq.${deliveryPersonId}`
        },
        callback
      )
      .subscribe();
  }

  /**
   * Make a phone call (opens phone app)
   */
  makePhoneCall(phoneNumber: string) {
    if (phoneNumber) {
      window.open(`tel:${phoneNumber}`, '_self');
    }
  }

  /**
   * Open navigation to address
   */
  navigateToAddress(address: string) {
    if (address) {
      const encodedAddress = encodeURIComponent(address);
      // Try Google Maps first, fallback to Apple Maps on iOS
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      
      if (isIOS) {
        window.open(`maps://maps.google.com/maps?daddr=${encodedAddress}`, '_blank');
      } else {
        window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');
      }
    }
  }
}

export const deliveryService = new DeliveryService();
export default deliveryService;
