
import { User, UserProfile, Address, PaymentMethod } from '../types/userDefinitions';
import { generateId } from '../utils/inventoryUtils';

// Mock data storage
let users: User[] = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    fullName: 'Admin User',
    userType: 'admin',
    phone: '+212 6 12 34 56 78',
    city: 'Casablanca',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'user-002',
    email: '<EMAIL>',
    fullName: 'Manager User',
    userType: 'manager',
    phone: '+212 6 87 65 43 21',
    city: 'Rabat',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'user-003',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    userType: 'client',
    phone: '+212 6 11 22 33 44',
    city: 'Marrakech',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    isCompany: false
  },
  {
    id: 'user-004',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    userType: 'reseller',
    phone: '+212 6 55 44 33 22',
    city: 'Casablanca',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    isCompany: true,
    companyName: 'Entreprise Moderne SARL',
    iceNumber: '001234567890123',
    companyAddress: '123 Boulevard Mohammed V, Casablanca',
    companyPhone: '+212 5 22 44 55 66',
    companyCity: 'Casablanca',
    companyEmail: '<EMAIL>',
    taxId: 'IF12345678',
    legalForm: 'SARL'
  }
];

let userProfiles: UserProfile[] = [];
let addresses: Address[] = [];
let paymentMethods: PaymentMethod[] = [];

// User Profile Management
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  const profile = userProfiles.find(p => p.userId === userId);
  if (profile) return profile;

  // Create default profile if none exists
  const defaultProfile: UserProfile = {
    userId,
    preferences: {
      language: 'en',
      currency: 'MAD',
      notifications: {
        email: true,
        sms: false,
        push: true,
        orderUpdates: true,
        promotions: false,
        stockAlerts: false
      },
      orderDefaults: {
        deliveryPreference: 'standard'
      }
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  userProfiles.push(defaultProfile);
  return defaultProfile;
};

export const updateUserProfile = async (userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> => {
  const index = userProfiles.findIndex(p => p.userId === userId);
  if (index === -1) {
    // Create new profile if none exists
    const newProfile = await getUserProfile(userId);
    if (!newProfile) return null;
    return updateUserProfile(userId, updates);
  }

  userProfiles[index] = {
    ...userProfiles[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  return userProfiles[index];
};

export const updateUser = async (userId: string, updates: Partial<User>): Promise<User | null> => {
  const index = users.findIndex(u => u.id === userId);
  if (index === -1) return null;

  users[index] = {
    ...users[index],
    ...updates
  };

  return users[index];
};

// Address Management
export const getUserAddresses = async (userId: string): Promise<Address[]> => {
  return addresses.filter(a => a.userId === userId);
};

export const addAddress = async (userId: string, addressData: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Address> => {
  const newAddress: Address = {
    ...addressData,
    id: generateId('ADDR'),
    userId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // If this is the first address or marked as default, make it default
  const userAddresses = await getUserAddresses(userId);
  if (userAddresses.length === 0 || newAddress.isDefault) {
    // Remove default from other addresses
    addresses = addresses.map(addr => 
      addr.userId === userId && addr.type === newAddress.type
        ? { ...addr, isDefault: false }
        : addr
    );
  }

  addresses.push(newAddress);
  return newAddress;
};

export const updateAddress = async (addressId: string, updates: Partial<Address>): Promise<Address | null> => {
  const index = addresses.findIndex(a => a.id === addressId);
  if (index === -1) return null;

  addresses[index] = {
    ...addresses[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  return addresses[index];
};

export const deleteAddress = async (addressId: string): Promise<boolean> => {
  const index = addresses.findIndex(a => a.id === addressId);
  if (index === -1) return false;

  addresses.splice(index, 1);
  return true;
};

// Payment Methods Management
export const getUserPaymentMethods = async (userId: string): Promise<PaymentMethod[]> => {
  return paymentMethods.filter(p => p.userId === userId);
};

export const addPaymentMethod = async (userId: string, methodData: Omit<PaymentMethod, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<PaymentMethod> => {
  const newMethod: PaymentMethod = {
    ...methodData,
    id: generateId('PAY'),
    userId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // If this is the first payment method or marked as default, make it default
  const userMethods = await getUserPaymentMethods(userId);
  if (userMethods.length === 0 || newMethod.isDefault) {
    // Remove default from other methods
    paymentMethods = paymentMethods.map(method => 
      method.userId === userId ? { ...method, isDefault: false } : method
    );
  }

  paymentMethods.push(newMethod);
  return newMethod;
};

export const updatePaymentMethod = async (methodId: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod | null> => {
  const index = paymentMethods.findIndex(p => p.id === methodId);
  if (index === -1) return null;

  paymentMethods[index] = {
    ...paymentMethods[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  return paymentMethods[index];
};

export const deletePaymentMethod = async (methodId: string): Promise<boolean> => {
  const index = paymentMethods.findIndex(p => p.id === methodId);
  if (index === -1) return false;

  paymentMethods.splice(index, 1);
  return true;
};
