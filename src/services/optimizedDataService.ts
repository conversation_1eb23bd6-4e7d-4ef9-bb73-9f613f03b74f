/**
 * Optimized Data Service for YalaOffice
 * Implements caching, pagination, and performance optimizations
 */

import { liveDataService } from './liveDataService';
import { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';
import { Product, Order, UserRow, CustomerProfileRow } from '../types';

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class OptimizedDataService {
  private readonly DEFAULT_PAGE_SIZE = 20;
  private readonly MAX_PAGE_SIZE = 100;

  /**
   * Get paginated products with caching
   */
  async getProducts(
    page: number = 1,
    limit: number = this.DEFAULT_PAGE_SIZE,
    categoryId?: string
  ): Promise<PaginatedResponse<Product>> {
    // Validate and sanitize parameters
    const sanitizedPage = Math.max(1, page);
    const sanitizedLimit = Math.min(this.MAX_PAGE_SIZE, Math.max(1, limit));

    // Generate cache key
    const cacheKey = cacheService.generatePaginationKey(
      CACHE_KEYS.PRODUCTS,
      sanitizedPage,
      sanitizedLimit,
      { categoryId }
    );

    return cacheService.get(
      cacheKey,
      async () => {
        if (categoryId) {
          return liveDataService.getProductsByCategory(categoryId, sanitizedPage, sanitizedLimit);
        }
        return liveDataService.getAllProducts(sanitizedPage, sanitizedLimit);
      },
      CACHE_TTL.MEDIUM
    );
  }

  /**
   * Get paginated orders with caching
   */
  async getOrders(
    page: number = 1,
    limit: number = this.DEFAULT_PAGE_SIZE,
    status?: string
  ): Promise<PaginatedResponse<Order>> {
    const sanitizedPage = Math.max(1, page);
    const sanitizedLimit = Math.min(this.MAX_PAGE_SIZE, Math.max(1, limit));

    const cacheKey = cacheService.generatePaginationKey(
      CACHE_KEYS.ORDERS,
      sanitizedPage,
      sanitizedLimit,
      { status }
    );

    return cacheService.get(
      cacheKey,
      async () => {
        if (status) {
          return liveDataService.getOrdersByStatus(status, sanitizedPage, sanitizedLimit);
        }
        return liveDataService.getAllOrders(sanitizedPage, sanitizedLimit);
      },
      CACHE_TTL.SHORT // Orders change frequently
    );
  }

  /**
   * Get paginated customers with caching
   */
  async getCustomers(
    page: number = 1,
    limit: number = this.DEFAULT_PAGE_SIZE
  ): Promise<PaginatedResponse<UserRow & { customer_profiles?: CustomerProfileRow }>> {
    const sanitizedPage = Math.max(1, page);
    const sanitizedLimit = Math.min(this.MAX_PAGE_SIZE, Math.max(1, limit));

    const cacheKey = cacheService.generatePaginationKey(
      CACHE_KEYS.CUSTOMERS,
      sanitizedPage,
      sanitizedLimit
    );

    return cacheService.get(
      cacheKey,
      async () => liveDataService.getAllCustomers(sanitizedPage, sanitizedLimit),
      CACHE_TTL.MEDIUM
    );
  }

  /**
   * Get dashboard stats with caching
   */
  async getDashboardStats(): Promise<any> {
    return cacheService.get(
      CACHE_KEYS.DASHBOARD_STATS,
      async () => liveDataService.getDashboardStats(),
      CACHE_TTL.SHORT // Dashboard stats should be relatively fresh
    );
  }

  /**
   * Get single product with caching
   */
  async getProduct(id: string): Promise<Product | null> {
    const cacheKey = cacheService.generateResourceKey(CACHE_KEYS.PRODUCTS, id);
    
    return cacheService.get(
      cacheKey,
      async () => liveDataService.getProductById(id),
      CACHE_TTL.MEDIUM
    );
  }

  /**
   * Get single order with caching
   */
  async getOrder(id: string): Promise<Order | null> {
    const cacheKey = cacheService.generateResourceKey(CACHE_KEYS.ORDERS, id);
    
    return cacheService.get(
      cacheKey,
      async () => liveDataService.getOrderById(id),
      CACHE_TTL.SHORT
    );
  }

  /**
   * Search products with caching and debouncing
   */
  async searchProducts(
    query: string,
    page: number = 1,
    limit: number = this.DEFAULT_PAGE_SIZE
  ): Promise<PaginatedResponse<Product>> {
    if (!query.trim()) {
      return { data: [], pagination: { page: 1, limit, total: 0, totalPages: 0 } };
    }

    const sanitizedPage = Math.max(1, page);
    const sanitizedLimit = Math.min(this.MAX_PAGE_SIZE, Math.max(1, limit));

    const cacheKey = cacheService.generatePaginationKey(
      `${CACHE_KEYS.PRODUCTS}:search`,
      sanitizedPage,
      sanitizedLimit,
      { query: query.toLowerCase().trim() }
    );

    return cacheService.get(
      cacheKey,
      async () => liveDataService.searchProducts(query, sanitizedPage, sanitizedLimit),
      CACHE_TTL.MEDIUM
    );
  }

  /**
   * Invalidate cache for specific resource type
   */
  invalidateCache(resourceType: keyof typeof CACHE_KEYS): void {
    cacheService.invalidatePattern(`^${CACHE_KEYS[resourceType]}`);
  }

  /**
   * Invalidate cache for specific resource
   */
  invalidateResourceCache(resourceType: keyof typeof CACHE_KEYS, id: string): void {
    const cacheKey = cacheService.generateResourceKey(CACHE_KEYS[resourceType], id);
    cacheService.invalidate(cacheKey);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    return cacheService.getStats();
  }

  /**
   * Preload critical data for better user experience
   */
  async preloadCriticalData(): Promise<void> {
    try {
      console.log('OptimizedDataService: Preloading critical data...');
      
      // Preload first page of products, orders, and dashboard stats
      await Promise.all([
        this.getProducts(1, this.DEFAULT_PAGE_SIZE),
        this.getOrders(1, this.DEFAULT_PAGE_SIZE),
        this.getDashboardStats()
      ]);

      console.log('OptimizedDataService: Critical data preloaded successfully');
    } catch (error) {
      console.error('OptimizedDataService: Error preloading critical data:', error);
    }
  }

  /**
   * Batch invalidate cache when data changes
   */
  handleDataChange(resourceType: keyof typeof CACHE_KEYS, operation: 'create' | 'update' | 'delete'): void {
    // Invalidate the specific resource type
    this.invalidateCache(resourceType);
    
    // Also invalidate dashboard stats as they depend on most data
    if (resourceType !== 'DASHBOARD_STATS') {
      this.invalidateCache('DASHBOARD_STATS');
    }

    console.log(`OptimizedDataService: Cache invalidated for ${resourceType} due to ${operation} operation`);
  }
}

// Create singleton instance
export const optimizedDataService = new OptimizedDataService();

export default optimizedDataService;
