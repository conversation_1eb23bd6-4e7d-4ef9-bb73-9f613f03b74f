/**
 * Unified Profile Picture Management Service for YalaOffice
 * Handles profile picture storage, retrieval, and real-time synchronization across all components
 */

import { supabase } from '../integrations/supabase/client';
import { liveDataService } from './liveDataService';

export interface ProfilePicture {
  userId: string;
  avatarUrl: string | null;
  updatedAt: string;
}

class ProfilePictureService {
  private subscribers: ((profilePicture: ProfilePicture) => void)[] = [];
  private profilePictureCache: Map<string, ProfilePicture> = new Map();

  constructor() {
    this.initializeRealTimeSubscription();
  }

  /**
   * Initialize real-time subscription for profile picture updates
   */
  private initializeRealTimeSubscription() {
    // Subscribe to user_profiles table changes
    supabase
      .channel('profile-pictures-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'user_profiles' }, 
        (payload) => {
          console.log('Profile picture update:', payload);
          this.handleProfilePictureUpdate(payload);
        }
      )
      .subscribe();
  }

  /**
   * Handle real-time profile picture updates
   */
  private handleProfilePictureUpdate(payload: any) {
    const { new: newRecord, old: oldRecord, eventType } = payload;
    
    if (eventType === 'UPDATE' || eventType === 'INSERT') {
      const profilePicture: ProfilePicture = {
        userId: newRecord.user_id,
        avatarUrl: newRecord.avatar_url,
        updatedAt: newRecord.updated_at
      };
      
      // Update cache
      this.profilePictureCache.set(newRecord.user_id, profilePicture);
      
      // Notify all subscribers
      this.notifySubscribers(profilePicture);
    } else if (eventType === 'DELETE' && oldRecord) {
      // Handle profile deletion
      this.profilePictureCache.delete(oldRecord.user_id);
      
      const profilePicture: ProfilePicture = {
        userId: oldRecord.user_id,
        avatarUrl: null,
        updatedAt: new Date().toISOString()
      };
      
      this.notifySubscribers(profilePicture);
    }
  }

  /**
   * Notify all subscribers of profile picture changes
   */
  private notifySubscribers(profilePicture: ProfilePicture) {
    this.subscribers.forEach(callback => {
      try {
        callback(profilePicture);
      } catch (error) {
        console.error('Error in profile picture subscriber:', error);
      }
    });
  }

  /**
   * Subscribe to profile picture updates for a specific user
   */
  subscribe(callback: (profilePicture: ProfilePicture) => void): () => void {
    this.subscribers.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * Get profile picture for a specific user
   */
  async getProfilePicture(userId: string): Promise<ProfilePicture | null> {
    try {
      // Check cache first
      if (this.profilePictureCache.has(userId)) {
        return this.profilePictureCache.get(userId)!;
      }

      // Fetch from database
      const { data, error } = await supabase
        .from('user_profiles')
        .select('user_id, avatar_url, updated_at')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No profile found, return null
          return null;
        }
        throw error;
      }

      const profilePicture: ProfilePicture = {
        userId: data.user_id,
        avatarUrl: data.avatar_url,
        updatedAt: data.updated_at
      };

      // Cache the result
      this.profilePictureCache.set(userId, profilePicture);

      return profilePicture;
    } catch (error) {
      console.error('Error fetching profile picture:', error);
      return null;
    }
  }

  /**
   * Update profile picture for a user
   */
  async updateProfilePicture(userId: string, avatarUrl: string): Promise<boolean> {
    try {
      console.log('Updating profile picture for user:', userId);

      // First, try to get existing profile
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error fetching existing profile:', fetchError);
        throw fetchError;
      }

      // Upsert the profile with avatar URL
      const profileData = {
        user_id: userId,
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
        // Preserve existing data if profile exists
        ...(existingProfile && {
          bio: existingProfile.bio,
          job_title: existingProfile.job_title,
          language: existingProfile.language || 'en',
          currency: existingProfile.currency || 'MAD',
          timezone: existingProfile.timezone || 'Africa/Casablanca'
        })
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .upsert(profileData)
        .select();

      if (error) {
        console.error('Error updating profile picture in database:', error);
        throw error;
      }

      console.log('Profile picture updated successfully:', data);
      return true;
    } catch (error) {
      console.error('Error updating profile picture:', error);
      return false;
    }
  }

  /**
   * Upload profile picture file to Supabase storage
   */
  async uploadProfilePicture(userId: string, file: File): Promise<{ success: boolean; profilePictureUrl?: string; message: string }> {
    try {
      console.log('🔄 Starting profile picture upload:', { userId, fileName: file.name, fileSize: file.size });

      // Validate file type
      if (!file.type.startsWith('image/')) {
        return {
          success: false,
          message: 'Please select an image file'
        };
      }

      // Validate file size (max 2MB to avoid database row size issues)
      if (file.size > 2 * 1024 * 1024) {
        return {
          success: false,
          message: 'File size must be less than 2MB'
        };
      }

      // Resize image if it's too large
      const processedFile = await this.resizeImage(file, 400, 400);

      // Create a unique filename
      const fileExt = processedFile.name.split('.').pop();
      const fileName = `avatar-${userId}-${Date.now()}.${fileExt}`;

      console.log('🔄 Attempting to upload to Supabase storage...');

      // Check if avatars bucket exists, create if it doesn't
      const bucketSetupResult = await this.ensureAvatarsBucketExists();

      if (!bucketSetupResult.success) {
        console.error('❌ Bucket setup failed:', bucketSetupResult.message);
        return {
          success: false,
          message: `Storage setup failed: ${bucketSetupResult.message}. Please contact administrator to set up the avatars bucket.`
        };
      }

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(fileName, processedFile, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error('❌ Supabase upload failed:', error);
        return {
          success: false,
          message: `Upload failed: ${error.message}`
        };
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      console.log('✅ Successfully uploaded to Supabase:', publicUrl);

      // Update user profile with new avatar URL
      await this.updateUserProfilePicture(userId, publicUrl);

      return {
        success: true,
        profilePictureUrl: publicUrl,
        message: 'Profile picture uploaded successfully!'
      };

    } catch (error) {
      console.error('❌ Error uploading profile picture:', error);
      return {
        success: false,
        message: `Upload error: ${(error as Error).message}`
      };
    }
  }

  /**
   * Generate fallback avatar URL using initials
   */
  generateFallbackAvatar(fullName: string, size: number = 400): string {
    const initials = fullName
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2) || 'U';
    
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=0d9488&color=fff&size=${size}&bold=true`;
  }

  /**
   * Get display avatar URL with fallback
   */
  async getDisplayAvatar(userId: string, fullName: string, size: number = 400): Promise<string> {
    try {
      const profilePicture = await this.getProfilePicture(userId);
      
      if (profilePicture?.avatarUrl) {
        return profilePicture.avatarUrl;
      }
      
      return this.generateFallbackAvatar(fullName, size);
    } catch (error) {
      console.error('Error getting display avatar:', error);
      return this.generateFallbackAvatar(fullName, size);
    }
  }

  /**
   * Clear cache for a specific user
   */
  clearCache(userId: string) {
    this.profilePictureCache.delete(userId);
  }

  /**
   * Clear all cache
   */
  clearAllCache() {
    this.profilePictureCache.clear();
  }

  /**
   * Resize image to specified dimensions
   */
  private async resizeImage(file: File, maxWidth: number, maxHeight: number): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress the image
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: 'image/jpeg', // Always convert to JPEG for smaller size
              lastModified: Date.now()
            });
            console.log('🔄 Image resized:', {
              originalSize: file.size,
              newSize: resizedFile.size,
              originalDimensions: `${img.width}x${img.height}`,
              newDimensions: `${width}x${height}`
            });
            resolve(resizedFile);
          } else {
            resolve(file);
          }
        }, 'image/jpeg', 0.8); // 80% quality for good balance of size/quality
      };

      img.onerror = () => resolve(file);
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Ensure avatars bucket exists and is properly configured
   */
  private async ensureAvatarsBucketExists(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔍 Checking if avatars bucket exists...');

      // First, try to list buckets to check if avatars bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.warn('⚠️  Could not list buckets:', listError);
        // Try to proceed anyway - bucket might exist but we can't list it
        return { success: true, message: 'Proceeding with upload attempt' };
      }

      const avatarsBucket = buckets?.find(bucket => bucket.name === 'avatars');

      if (avatarsBucket) {
        console.log('✅ Avatars bucket exists');
        return { success: true, message: 'Avatars bucket is ready' };
      }

      console.log('⚠️  Avatars bucket does not exist, attempting to create...');

      // Try to create the avatars bucket
      const { error: createError } = await supabase.storage.createBucket('avatars', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        fileSizeLimit: 2097152 // 2MB
      });

      if (createError) {
        console.error('❌ Could not create avatars bucket:', createError);

        // Check if it's a permission error
        if (createError.message.includes('permission') || createError.message.includes('unauthorized')) {
          return {
            success: false,
            message: 'No permission to create storage bucket. Please ask an administrator to create the "avatars" bucket in Supabase Storage.'
          };
        }

        // Check if bucket already exists (race condition)
        if (createError.message.includes('already exists')) {
          console.log('✅ Bucket already exists (created by another process)');
          return { success: true, message: 'Avatars bucket is ready' };
        }

        return {
          success: false,
          message: `Failed to create avatars bucket: ${createError.message}`
        };
      }

      console.log('✅ Successfully created avatars bucket');
      return { success: true, message: 'Avatars bucket created successfully' };

    } catch (error) {
      console.error('❌ Error checking/creating avatars bucket:', error);
      return {
        success: false,
        message: `Bucket setup error: ${(error as Error).message}`
      };
    }
  }

  /**
   * Update user profile picture URL in database
   */
  private async updateUserProfilePicture(userId: string, profilePictureUrl: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          profile_picture_url: profilePictureUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating user profile picture URL:', error);
        throw error;
      }

      console.log('✅ User profile picture URL updated in database');
    } catch (error) {
      console.error('❌ Failed to update user profile picture URL:', error);
      throw error;
    }
  }
}

// Create singleton instance
export const profilePictureService = new ProfilePictureService();

export default profilePictureService;
