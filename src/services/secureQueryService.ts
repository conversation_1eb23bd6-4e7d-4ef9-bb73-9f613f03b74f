/**
 * Secure Query Service for YalaOffice
 * Provides SQL injection protection and input sanitization for database queries
 */

import { supabase } from '../integrations/supabase/client';
import { PostgrestFilterBuilder } from '@supabase/postgrest-js';

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize search input to prevent SQL injection
   */
  static sanitizeSearchInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    // Remove potentially dangerous characters and patterns
    return input
      .trim()
      .replace(/['"\\;--]/g, '') // Remove quotes, backslashes, semicolons, SQL comments
      .replace(/\s+/g, ' ') // Normalize whitespace
      .substring(0, 100); // Limit length
  }
  
  /**
   * Sanitize email input
   */
  static sanitizeEmail(email: string): string {
    if (!email || typeof email !== 'string') {
      return '';
    }
    
    // Basic email sanitization
    return email
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9@._-]/g, '')
      .substring(0, 254); // RFC 5321 limit
  }
  
  /**
   * Sanitize numeric input
   */
  static sanitizeNumber(input: any): number {
    const num = parseFloat(input);
    return isNaN(num) ? 0 : num;
  }
  
  /**
   * Sanitize boolean input
   */
  static sanitizeBoolean(input: any): boolean {
    if (typeof input === 'boolean') return input;
    if (typeof input === 'string') {
      return input.toLowerCase() === 'true' || input === '1';
    }
    return Boolean(input);
  }
  
  /**
   * Sanitize UUID input
   */
  static sanitizeUUID(uuid: string): string {
    if (!uuid || typeof uuid !== 'string') {
      return '';
    }
    
    // UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid) ? uuid : '';
  }
  
  /**
   * Sanitize array input
   */
  static sanitizeArray(input: any[]): any[] {
    if (!Array.isArray(input)) {
      return [];
    }
    
    return input
      .filter(item => item !== null && item !== undefined)
      .slice(0, 100); // Limit array size
  }
}

/**
 * Secure query builder for common database operations
 */
export class SecureQueryBuilder {
  /**
   * Build secure search query with ILIKE
   */
  static buildSearchQuery<T>(
    query: PostgrestFilterBuilder<any, T, any>,
    searchTerm: string,
    searchFields: string[]
  ): PostgrestFilterBuilder<any, T, any> {
    if (!searchTerm || !searchFields.length) {
      return query;
    }
    
    // Sanitize search input
    const sanitizedSearch = InputSanitizer.sanitizeSearchInput(searchTerm);
    if (!sanitizedSearch) {
      return query;
    }
    
    // Build safe ILIKE conditions using Supabase's built-in parameterization
    const conditions = searchFields.map(field => `${field}.ilike.%${sanitizedSearch}%`);
    return query.or(conditions.join(','));
  }
  
  /**
   * Build secure filter query
   */
  static buildFilterQuery<T>(
    query: PostgrestFilterBuilder<any, T, any>,
    filters: Record<string, any>
  ): PostgrestFilterBuilder<any, T, any> {
    let filteredQuery = query;
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        return;
      }
      
      // Sanitize filter key (prevent injection through field names)
      const sanitizedKey = key.replace(/[^a-zA-Z0-9_]/g, '');
      if (!sanitizedKey) {
        return;
      }
      
      // Apply appropriate filter based on value type
      if (Array.isArray(value)) {
        const sanitizedArray = InputSanitizer.sanitizeArray(value);
        if (sanitizedArray.length > 0) {
          filteredQuery = filteredQuery.in(sanitizedKey, sanitizedArray);
        }
      } else if (typeof value === 'string') {
        const sanitizedValue = InputSanitizer.sanitizeSearchInput(value);
        if (sanitizedValue) {
          filteredQuery = filteredQuery.eq(sanitizedKey, sanitizedValue);
        }
      } else if (typeof value === 'number') {
        const sanitizedValue = InputSanitizer.sanitizeNumber(value);
        filteredQuery = filteredQuery.eq(sanitizedKey, sanitizedValue);
      } else if (typeof value === 'boolean') {
        const sanitizedValue = InputSanitizer.sanitizeBoolean(value);
        filteredQuery = filteredQuery.eq(sanitizedKey, sanitizedValue);
      }
    });
    
    return filteredQuery;
  }
  
  /**
   * Build secure date range query
   */
  static buildDateRangeQuery<T>(
    query: PostgrestFilterBuilder<any, T, any>,
    field: string,
    startDate?: string,
    endDate?: string
  ): PostgrestFilterBuilder<any, T, any> {
    // Sanitize field name
    const sanitizedField = field.replace(/[^a-zA-Z0-9_]/g, '');
    if (!sanitizedField) {
      return query;
    }
    
    let filteredQuery = query;
    
    if (startDate) {
      // Validate date format
      const start = new Date(startDate);
      if (!isNaN(start.getTime())) {
        filteredQuery = filteredQuery.gte(sanitizedField, start.toISOString());
      }
    }
    
    if (endDate) {
      // Validate date format
      const end = new Date(endDate);
      if (!isNaN(end.getTime())) {
        filteredQuery = filteredQuery.lte(sanitizedField, end.toISOString());
      }
    }
    
    return filteredQuery;
  }
  
  /**
   * Build secure pagination query
   */
  static buildPaginationQuery<T>(
    query: PostgrestFilterBuilder<any, T, any>,
    page: number = 1,
    limit: number = 20
  ): PostgrestFilterBuilder<any, T, any> {
    // Sanitize and validate pagination parameters
    const sanitizedPage = Math.max(1, InputSanitizer.sanitizeNumber(page));
    const sanitizedLimit = Math.min(100, Math.max(1, InputSanitizer.sanitizeNumber(limit)));
    
    const offset = (sanitizedPage - 1) * sanitizedLimit;
    return query.range(offset, offset + sanitizedLimit - 1);
  }
  
  /**
   * Build secure ordering query
   */
  static buildOrderQuery<T>(
    query: PostgrestFilterBuilder<any, T, any>,
    orderBy: string,
    ascending: boolean = true
  ): PostgrestFilterBuilder<any, T, any> {
    // Sanitize order field
    const sanitizedOrderBy = orderBy.replace(/[^a-zA-Z0-9_]/g, '');
    if (!sanitizedOrderBy) {
      return query.order('created_at', { ascending: false }); // Default ordering
    }
    
    const sanitizedAscending = InputSanitizer.sanitizeBoolean(ascending);
    return query.order(sanitizedOrderBy, { ascending: sanitizedAscending });
  }
}

/**
 * Secure database service with built-in protection
 */
export class SecureDatabaseService {
  /**
   * Execute secure select query with comprehensive protection
   */
  static async secureSelect<T>(
    tableName: string,
    options: {
      select?: string;
      filters?: Record<string, any>;
      search?: { term: string; fields: string[] };
      dateRange?: { field: string; start?: string; end?: string };
      pagination?: { page: number; limit: number };
      ordering?: { field: string; ascending: boolean };
    } = {}
  ) {
    try {
      // Sanitize table name
      const sanitizedTable = tableName.replace(/[^a-zA-Z0-9_]/g, '');
      if (!sanitizedTable) {
        throw new Error('Invalid table name');
      }
      
      // Start building query
      let query = supabase
        .from(sanitizedTable)
        .select(options.select || '*', { count: 'exact' });
      
      // Apply filters
      if (options.filters) {
        query = SecureQueryBuilder.buildFilterQuery(query, options.filters);
      }
      
      // Apply search
      if (options.search?.term && options.search?.fields) {
        query = SecureQueryBuilder.buildSearchQuery(
          query,
          options.search.term,
          options.search.fields
        );
      }
      
      // Apply date range
      if (options.dateRange?.field) {
        query = SecureQueryBuilder.buildDateRangeQuery(
          query,
          options.dateRange.field,
          options.dateRange.start,
          options.dateRange.end
        );
      }
      
      // Apply ordering
      if (options.ordering?.field) {
        query = SecureQueryBuilder.buildOrderQuery(
          query,
          options.ordering.field,
          options.ordering.ascending
        );
      }
      
      // Apply pagination
      if (options.pagination) {
        query = SecureQueryBuilder.buildPaginationQuery(
          query,
          options.pagination.page,
          options.pagination.limit
        );
      }
      
      // Execute query
      const result = await query;
      
      if (result.error) {
        console.error('Secure query error:', result.error);
        throw result.error;
      }
      
      return {
        data: result.data || [],
        count: result.count || 0,
        error: null
      };
    } catch (error) {
      console.error('SecureDatabaseService error:', error);
      return {
        data: [],
        count: 0,
        error: error as Error
      };
    }
  }
}
