import { realTimeService } from './realTimeService';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';

// Type aliases for better readability
type UserRow = Database['public']['Tables']['users']['Row'];
type UserInsert = Database['public']['Tables']['users']['Insert'];
type UserUpdate = Database['public']['Tables']['users']['Update'];
type CustomerProfileRow = Database['public']['Tables']['customer_profiles']['Row'];
type CustomerProfileInsert = Database['public']['Tables']['customer_profiles']['Insert'];
type CustomerProfileUpdate = Database['public']['Tables']['customer_profiles']['Update'];

export interface Client {
  id: string;
  email: string;
  fullName: string;
  userType: 'client' | 'reseller';
  isActive: boolean;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  company?: string;
  taxId?: string;
  discountRate?: number; // For resellers
  creditLimit?: number; // For resellers
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  registrationDate: string;
  lastLogin?: string;
  notes?: string;
  avatar?: string;
  preferredPaymentMethod?: string;
  loyaltyPoints?: number;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
}

export interface CreateClientData {
  email: string;
  fullName: string;
  userType: 'client' | 'reseller';
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  company?: string;
  taxId?: string;
  discountRate?: number;
  creditLimit?: number;
  notes?: string;
  preferredPaymentMethod?: string;
}

export interface ClientFilters {
  userType?: 'all' | 'client' | 'reseller';
  status?: 'all' | 'active' | 'inactive' | 'suspended' | 'pending';
  city?: string;
  country?: string;
  registrationDateFrom?: string;
  registrationDateTo?: string;
  totalSpentMin?: number;
  totalSpentMax?: number;
}

// Helper function to convert database rows to Client type
const convertToClient = (userRow: UserRow, profileRow?: CustomerProfileRow): Client => {
  return {
    id: userRow.id,
    email: userRow.email,
    fullName: userRow.full_name,
    userType: userRow.user_type as 'client' | 'reseller',
    isActive: userRow.is_active,
    phone: userRow.phone || undefined,
    address: userRow.company_address || undefined,
    city: userRow.city || undefined,
    country: 'Morocco', // Default for Morocco-based system
    company: userRow.company_name || undefined,
    taxId: userRow.tax_id || undefined,
    discountRate: profileRow?.discount_rate ? Number(profileRow.discount_rate) : 0,
    creditLimit: profileRow?.credit_limit ? Number(profileRow.credit_limit) : 0,
    totalOrders: profileRow?.total_orders || 0,
    totalSpent: profileRow?.total_spent ? Number(profileRow.total_spent) : 0,
    lastOrderDate: profileRow?.last_order_date || undefined,
    registrationDate: userRow.created_at,
    lastLogin: userRow.last_login || undefined,
    notes: profileRow?.notes || undefined,
    avatar: undefined, // Would come from user_profiles table
    preferredPaymentMethod: profileRow?.preferred_payment_method || undefined,
    loyaltyPoints: profileRow?.loyalty_points || 0,
    status: profileRow?.status as Client['status'] || 'active',
  };
};

// Enhanced Client Management with database integration
export const getClients = async (filters?: ClientFilters): Promise<Client[]> => {
  try {
    let query = supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          preferred_payment_method,
          status,
          notes
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters?.userType && filters.userType !== 'all') {
      query = query.eq('user_type', filters.userType);
    }

    if (filters?.city) {
      query = query.eq('city', filters.city);
    }

    if (filters?.registrationDateFrom) {
      query = query.gte('created_at', filters.registrationDateFrom);
    }

    if (filters?.registrationDateTo) {
      query = query.lte('created_at', filters.registrationDateTo);
    }

    const { data: users, error } = await query;

    if (error) {
      console.error('Error fetching clients:', error);
      throw error;
    }

    let clients = users?.map(user =>
      convertToClient(user, user.customer_profiles?.[0])
    ) || [];

    // Apply additional filters that need to be done after conversion
    if (filters?.status && filters.status !== 'all') {
      clients = clients.filter(client => client.status === filters.status);
    }

    if (filters?.totalSpentMin !== undefined) {
      clients = clients.filter(client => client.totalSpent >= filters.totalSpentMin!);
    }

    if (filters?.totalSpentMax !== undefined) {
      clients = clients.filter(client => client.totalSpent <= filters.totalSpentMax!);
    }

    return clients;
  } catch (error) {
    console.error('Error in getClients:', error);
    return [];
  }
};
export const getClientById = async (id: string): Promise<Client | null> => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          preferred_payment_method,
          status,
          notes
        )
      `)
      .eq('id', id)
      .in('user_type', ['client', 'reseller'])
      .single();

    if (error) {
      console.error('Error fetching client:', error);
      return null;
    }

    return user ? convertToClient(user, user.customer_profiles?.[0]) : null;
  } catch (error) {
    console.error('Error in getClientById:', error);
    return null;
  }
};

export const searchClients = async (query: string): Promise<Client[]> => {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;

    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          preferred_payment_method,
          status,
          notes
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .or(`full_name.ilike.${searchTerm},email.ilike.${searchTerm},company_name.ilike.${searchTerm}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching clients:', error);
      throw error;
    }

    return users?.map(user =>
      convertToClient(user, user.customer_profiles?.[0])
    ) || [];
  } catch (error) {
    console.error('Error in searchClients:', error);
    return [];
  }
};
export const createClient = async (clientData: CreateClientData, createdBy: string): Promise<Client | null> => {
  try {
    // Check if email already exists using secure database function
    const { data: emailExists, error: checkError } = await supabase
      .rpc('check_email_exists', { email_to_check: clientData.email });

    if (checkError) {
      console.error('Email uniqueness check error:', checkError);
      throw new Error('Unable to verify email availability. Please try again.');
    }

    if (emailExists) {
      throw new Error('Email already exists');
    }

    // Create user in users table
    const userInsert: UserInsert = {
      email: clientData.email,
      full_name: clientData.fullName,
      user_type: clientData.userType,
      phone: clientData.phone,
      city: clientData.city,
      is_company: clientData.userType === 'reseller',
      company_name: clientData.company,
      company_address: clientData.address,
      tax_id: clientData.taxId,
    };

    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert(userInsert)
      .select()
      .single();

    if (userError) {
      console.error('Error creating user:', userError);
      throw userError;
    }

    if (!newUser) {
      throw new Error('Failed to create user');
    }

    // Create customer profile
    const profileInsert: CustomerProfileInsert = {
      user_id: newUser.id,
      discount_rate: clientData.discountRate || 0,
      credit_limit: clientData.creditLimit || 0,
      total_orders: 0,
      total_spent: 0,
      loyalty_points: 0,
      preferred_payment_method: clientData.preferredPaymentMethod,
      status: 'active',
      notes: clientData.notes,
    };

    const { error: profileError } = await supabase
      .from('customer_profiles')
      .insert(profileInsert);

    if (profileError) {
      console.error('Error creating customer profile:', profileError);
      // Don't throw here, user was created successfully
    }

    const client = convertToClient(newUser);

    // Emit real-time event
    realTimeService.emit('client-created', {
      client,
      createdBy
    });

    return client;
  } catch (error) {
    console.error('Error in createClient:', error);
    return null;
  }
};
export const updateClient = async (id: string, updates: Partial<Client>, updatedBy: string): Promise<Client | null> => {
  try {
    // Update user in users table
    const userUpdate: UserUpdate = {
      full_name: updates.fullName,
      phone: updates.phone,
      city: updates.city,
      company_name: updates.company,
      company_address: updates.address,
      tax_id: updates.taxId,
      is_active: updates.isActive,
    };

    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .update(userUpdate)
      .eq('id', id)
      .select()
      .single();

    if (userError) {
      console.error('Error updating user:', userError);
      throw userError;
    }

    // Update customer profile
    const profileUpdate: CustomerProfileUpdate = {
      discount_rate: updates.discountRate,
      credit_limit: updates.creditLimit,
      preferred_payment_method: updates.preferredPaymentMethod,
      status: updates.status,
      notes: updates.notes,
    };

    const { error: profileError } = await supabase
      .from('customer_profiles')
      .update(profileUpdate)
      .eq('user_id', id);

    if (profileError) {
      console.error('Error updating customer profile:', profileError);
      // Don't throw here, user was updated successfully
    }

    if (updatedUser) {
      const client = convertToClient(updatedUser);

      // Emit real-time event
      realTimeService.emit('client-updated', {
        clientId: id,
        newData: client,
        updatedBy
      });

      return client;
    }

    return null;
  } catch (error) {
    console.error('Error in updateClient:', error);
    return null;
  }
};
export const deleteClient = async (id: string, deletedBy: string): Promise<boolean> => {
  try {
    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('users')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting client:', error);
      throw error;
    }

    // Emit real-time event
    realTimeService.emit('client-deleted', { clientId: id, deletedBy });

    return true;
  } catch (error) {
    console.error('Error in deleteClient:', error);
    return false;
  }
};

export const bulkUpdateClients = async (clientIds: string[], updates: Partial<Client>, updatedBy: string): Promise<Client[]> => {
  try {
    const updatedClients: Client[] = [];

    for (const id of clientIds) {
      const client = await updateClient(id, updates, updatedBy);
      if (client) {
        updatedClients.push(client);
      }
    }

    return updatedClients;
  } catch (error) {
    console.error('Error in bulkUpdateClients:', error);
    return [];
  }
};

export const bulkDeleteClients = async (clientIds: string[], deletedBy: string): Promise<boolean> => {
  try {
    for (const id of clientIds) {
      await deleteClient(id, deletedBy);
    }

    return true;
  } catch (error) {
    console.error('Error in bulkDeleteClients:', error);
    return false;
  }
};

export const getClientStats = async (): Promise<{
  total: number;
  clients: number;
  resellers: number;
  active: number;
  inactive: number;
  totalRevenue: number;
  averageOrderValue: number;
  topSpenders: Client[];
}> => {
  try {
    // Get all clients and resellers
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        user_type,
        is_active,
        customer_profiles (
          total_orders,
          total_spent,
          status
        )
      `)
      .in('user_type', ['client', 'reseller']);

    if (error) {
      console.error('Error fetching client stats:', error);
      throw error;
    }

    // Get top spenders
    const topSpendersData = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          preferred_payment_method,
          status,
          notes
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .order('customer_profiles(total_spent)', { ascending: false })
      .limit(5);

    const topSpenders = topSpendersData.data?.map(user =>
      convertToClient(user, user.customer_profiles?.[0])
    ) || [];

    // Calculate statistics
    const totalRevenue = users?.reduce((sum, user) => {
      const spent = user.customer_profiles?.[0]?.total_spent || 0;
      return sum + Number(spent);
    }, 0) || 0;

    const totalOrders = users?.reduce((sum, user) => {
      const orders = user.customer_profiles?.[0]?.total_orders || 0;
      return sum + orders;
    }, 0) || 0;

    const stats = {
      total: users?.length || 0,
      clients: users?.filter(u => u.user_type === 'client').length || 0,
      resellers: users?.filter(u => u.user_type === 'reseller').length || 0,
      active: users?.filter(u => u.is_active).length || 0,
      inactive: users?.filter(u => !u.is_active).length || 0,
      totalRevenue,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
      topSpenders
    };

    return stats;
  } catch (error) {
    console.error('Error in getClientStats:', error);
    return {
      total: 0,
      clients: 0,
      resellers: 0,
      active: 0,
      inactive: 0,
      totalRevenue: 0,
      averageOrderValue: 0,
      topSpenders: []
    };
  }
};

export default {
  getClients,
  getClientById,
  searchClients,
  createClient,
  updateClient,
  deleteClient,
  bulkUpdateClients,
  bulkDeleteClients,
  getClientStats
};
