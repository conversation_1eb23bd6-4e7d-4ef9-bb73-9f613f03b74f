/**
 * Optimized Real-Time Service for YalaOffice
 * Implements incremental updates, debouncing, and intelligent cache invalidation
 */

import { realTimeService, EventType, RealTimeEvent } from './realTimeService';
import { optimizedDataService } from './optimizedDataService';

interface UpdateBatch {
  events: RealTimeEvent[];
  timestamp: number;
}

interface SubscriptionConfig {
  debounceMs?: number;
  batchUpdates?: boolean;
  cacheInvalidation?: boolean;
}

class OptimizedRealTimeService {
  private updateBatches: Map<string, UpdateBatch> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private readonly DEFAULT_DEBOUNCE_MS = 500;
  private readonly BATCH_TIMEOUT_MS = 1000;

  /**
   * Subscribe to real-time events with optimization options
   */
  subscribe(
    eventType: EventType,
    callback: (event: RealTimeEvent) => void,
    config: SubscriptionConfig = {}
  ): () => void {
    const {
      debounceMs = this.DEFAULT_DEBOUNCE_MS,
      batchUpdates = true,
      cacheInvalidation = true
    } = config;

    // Create optimized callback wrapper
    const optimizedCallback = (event: RealTimeEvent) => {
      // Handle cache invalidation
      if (cacheInvalidation) {
        this.handleCacheInvalidation(event);
      }

      // Handle debouncing
      if (debounceMs > 0) {
        this.debounceCallback(eventType, event, callback, debounceMs);
      } else if (batchUpdates) {
        this.batchCallback(eventType, event, callback);
      } else {
        callback(event);
      }
    };

    // Subscribe to the underlying real-time service
    return realTimeService.subscribe(eventType, optimizedCallback);
  }

  /**
   * Subscribe to multiple events with shared optimization
   */
  subscribeToMultiple(
    eventTypes: EventType[],
    callback: (event: RealTimeEvent) => void,
    config: SubscriptionConfig = {}
  ): () => void {
    const unsubscribeFunctions = eventTypes.map(eventType =>
      this.subscribe(eventType, callback, config)
    );

    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }

  /**
   * Debounce callback execution to prevent excessive updates
   */
  private debounceCallback(
    eventType: EventType,
    event: RealTimeEvent,
    callback: (event: RealTimeEvent) => void,
    debounceMs: number
  ): void {
    const key = `${eventType}:${event.userId || 'global'}`;
    
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      callback(event);
      this.debounceTimers.delete(key);
    }, debounceMs);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Batch multiple events together for efficient processing
   */
  private batchCallback(
    eventType: EventType,
    event: RealTimeEvent,
    callback: (event: RealTimeEvent) => void
  ): void {
    const key = `${eventType}:batch`;
    const now = Date.now();

    // Get or create batch
    let batch = this.updateBatches.get(key);
    if (!batch) {
      batch = { events: [], timestamp: now };
      this.updateBatches.set(key, batch);

      // Schedule batch processing
      setTimeout(() => {
        const currentBatch = this.updateBatches.get(key);
        if (currentBatch) {
          // Process the most recent event from the batch
          const latestEvent = currentBatch.events[currentBatch.events.length - 1];
          callback(latestEvent);
          this.updateBatches.delete(key);
        }
      }, this.BATCH_TIMEOUT_MS);
    }

    // Add event to batch
    batch.events.push(event);
  }

  /**
   * Handle intelligent cache invalidation based on event type
   */
  private handleCacheInvalidation(event: RealTimeEvent): void {
    const { type, data } = event;

    switch (type) {
      case 'product-updated':
      case 'product-added':
      case 'product-deleted':
        optimizedDataService.invalidateCache('PRODUCTS');
        if (data?.id) {
          optimizedDataService.invalidateResourceCache('PRODUCTS', data.id);
        }
        break;

      case 'order-created':
      case 'order-updated':
      case 'order-deleted':
      case 'order-status-changed':
        optimizedDataService.invalidateCache('ORDERS');
        if (data?.id) {
          optimizedDataService.invalidateResourceCache('ORDERS', data.id);
        }
        break;

      case 'user-created':
      case 'user-updated':
      case 'user-deleted':
        optimizedDataService.invalidateCache('USERS');
        optimizedDataService.invalidateCache('CUSTOMERS');
        if (data?.id) {
          optimizedDataService.invalidateResourceCache('USERS', data.id);
        }
        break;

      case 'category-created':
      case 'category-updated':
      case 'category-deleted':
        optimizedDataService.invalidateCache('CATEGORIES');
        optimizedDataService.invalidateCache('PRODUCTS'); // Products depend on categories
        break;

      case 'branch-created':
      case 'branch-updated':
      case 'branch-deleted':
        optimizedDataService.invalidateCache('BRANCHES');
        break;

      default:
        // For any other events, invalidate dashboard stats
        optimizedDataService.invalidateCache('DASHBOARD_STATS');
        break;
    }

    console.log(`OptimizedRealTimeService: Cache invalidated for event type: ${type}`);
  }

  /**
   * Subscribe to products with optimized updates
   */
  subscribeToProducts(callback: (products: any[]) => void): () => void {
    return this.subscribe(
      'product-updated',
      () => {
        // Instead of passing products directly, trigger a cache-aware refetch
        console.log('OptimizedRealTimeService: Product update detected, triggering optimized refetch');
        callback([]); // Signal that data should be refetched
      },
      {
        debounceMs: 1000, // Debounce product updates
        batchUpdates: true,
        cacheInvalidation: true
      }
    );
  }

  /**
   * Subscribe to orders with optimized updates
   */
  subscribeToOrders(callback: (orders: any[]) => void): () => void {
    return this.subscribeToMultiple(
      ['order-created', 'order-updated', 'order-deleted', 'order-status-changed'],
      () => {
        console.log('OptimizedRealTimeService: Order update detected, triggering optimized refetch');
        callback([]);
      },
      {
        debounceMs: 500, // Orders need faster updates
        batchUpdates: true,
        cacheInvalidation: true
      }
    );
  }

  /**
   * Subscribe to users with optimized updates
   */
  subscribeToUsers(callback: (users: any[]) => void): () => void {
    return this.subscribeToMultiple(
      ['user-created', 'user-updated', 'user-deleted'],
      () => {
        console.log('OptimizedRealTimeService: User update detected, triggering optimized refetch');
        callback([]);
      },
      {
        debounceMs: 1000,
        batchUpdates: true,
        cacheInvalidation: true
      }
    );
  }

  /**
   * Get connection status from underlying service
   */
  getConnectionStatus(): boolean {
    return realTimeService.getConnectionStatus();
  }

  /**
   * Emit events through underlying service
   */
  emit(eventType: EventType, data: any, userId?: string, userType?: string): void {
    realTimeService.emit(eventType, data, userId, userType);
  }

  /**
   * Clear all debounce timers and batches
   */
  cleanup(): void {
    // Clear all debounce timers
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    // Clear all update batches
    this.updateBatches.clear();

    console.log('OptimizedRealTimeService: Cleanup completed');
  }

  /**
   * Get performance metrics
   */
  getMetrics(): {
    activeBatches: number;
    activeTimers: number;
    cacheStats: any;
  } {
    return {
      activeBatches: this.updateBatches.size,
      activeTimers: this.debounceTimers.size,
      cacheStats: optimizedDataService.getCacheStats()
    };
  }
}

// Create singleton instance
export const optimizedRealTimeService = new OptimizedRealTimeService();

export default optimizedRealTimeService;
