import { realTimeService } from './realTimeService';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';
import { User } from '../types/userDefinitions';

// Type aliases for better readability
type UserRow = Database['public']['Tables']['users']['Row'];
type UserInsert = Database['public']['Tables']['users']['Insert'];
type UserUpdate = Database['public']['Tables']['users']['Update'];
type UserProfileRow = Database['public']['Tables']['user_profiles']['Row'];
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];

export interface CreateUserData {
  email: string;
  fullName: string;
  userType: 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
  password?: string;
  phone?: string;
  department?: string;
  branch?: string;
  permissions?: string[];
  city?: string;
  isActive?: boolean;
  isCompany?: boolean;
  companyName?: string;
  iceNumber?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyCity?: string;
  companyEmail?: string;
  taxId?: string;
  legalForm?: string;
  profilePicture?: File | null;
}

// Helper function to generate a secure password
const generateSecurePassword = (): string => {
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';

  // Ensure at least one of each type
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
  password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special char

  // Fill the rest randomly
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

// Helper function to convert database row to User type
const convertToUser = (userRow: UserRow, profileRow?: any): User => {
  // Determine status based on is_active field only
  const status: User['status'] = userRow.is_active ? 'active' : 'inactive';

  return {
    // Core user fields
    id: userRow.id,
    email: userRow.email,
    fullName: userRow.full_name,
    userType: userRow.user_type as User['userType'],
    phone: userRow.phone || undefined,
    city: userRow.city || undefined,
    isActive: userRow.is_active,
    status: status,

    // Timestamp fields
    createdAt: userRow.created_at,
    updatedAt: userRow.updated_at || undefined,
    lastLogin: userRow.last_login || undefined,

    // Company information fields
    isCompany: userRow.is_company || false,
    companyName: userRow.company_name || undefined,
    companyAddress: userRow.company_address || undefined,
    companyCity: userRow.company_city || undefined,
    companyPhone: userRow.company_phone || undefined,
    companyEmail: userRow.company_email || undefined,
    iceNumber: userRow.ice_number || undefined,
    taxId: userRow.tax_id || undefined,
    legalForm: userRow.legal_form || undefined,

    // Additional fields for UI compatibility
    createdBy: 'system', // Default value, would be tracked separately
    permissions: [], // This would come from a separate permissions system
    avatar: profileRow?.avatar_url || undefined,
    department: profileRow?.job_title || undefined,
    branch: undefined, // Would be determined by user's assigned branch
  };
};

// User management functions using Supabase
export const getUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getUsers:', error);
    return [];
  }
};

export const getUserById = async (id: string): Promise<User | null> => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return null;
    }

    return user ? convertToUser(user, user.user_profiles?.[0]) : null;
  } catch (error) {
    console.error('Error in getUserById:', error);
    return null;
  }
};

export const createUser = async (userData: CreateUserData, createdBy: string): Promise<User | null> => {
  try {
    console.log('Creating user with data:', { ...userData, password: userData.password ? '[REDACTED]' : 'none' });

    // Step 1: Create user in Supabase Auth with email confirmation bypassed for admin-created accounts
    const tempPassword = userData.password || generateSecurePassword();

    // Use admin client for user creation (requires service role key)
    const { createAdminClient } = await import('../integrations/supabase/client');
    const adminClient = createAdminClient();

    console.log('Using admin client for user creation with service role key');

    const { data: authData, error: authError } = await adminClient.auth.admin.createUser({
      email: userData.email,
      password: tempPassword,
      email_confirm: true, // Bypass email confirmation for admin-created accounts
      user_metadata: {
        full_name: userData.fullName,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city,
        created_by: createdBy,
        admin_created: true // Flag to indicate this was created by admin
      }
    });

    if (authError) {
      console.error('Error creating user in Supabase Auth:', authError);
      throw new Error(`Failed to create user account: ${authError.message}`);
    }

    if (!authData.user) {
      throw new Error('Failed to create user account: No user data returned');
    }

    console.log('User created in Supabase Auth with admin client:', authData.user.id);

    // Step 2: Create user record in our database using the same ID
    const userInsert: UserInsert = {
      id: authData.user.id, // Use the same ID from Supabase Auth
      email: userData.email,
      full_name: userData.fullName,
      user_type: userData.userType,
      phone: userData.phone,
      city: userData.city,
      is_company: userData.isCompany || false,
      company_name: userData.companyName,
      ice_number: userData.iceNumber,
      company_address: userData.companyAddress,
      company_phone: userData.companyPhone,
      company_city: userData.companyCity,
      company_email: userData.companyEmail,
      tax_id: userData.taxId,
      legal_form: userData.legalForm,
      is_active: userData.isActive !== undefined ? userData.isActive : true, // User is active and email confirmed by admin
    };

    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert(userInsert)
      .select()
      .single();

    if (userError) {
      console.error('Error creating user in database:', userError);

      // Clean up: Delete the auth user if database insert failed (use admin client)
      try {
        await adminClient.auth.admin.deleteUser(authData.user.id);
      } catch (cleanupError) {
        console.error('Error cleaning up auth user:', cleanupError);
      }

      throw new Error(`Failed to create user profile: ${userError.message}`);
    }

    // Step 3: Create user profile
    if (newUser) {
      const profileInsert: UserProfileInsert = {
        user_id: newUser.id,
        job_title: userData.department,
        language: 'en',
        currency: 'MAD',
        timezone: 'Africa/Casablanca',
      };

      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert(profileInsert);

      if (profileError) {
        console.error('Error creating user profile:', profileError);
        // Don't throw here, user was created successfully
      }

      const user = convertToUser(newUser);

      // Step 4: User created with email confirmation bypassed (admin-created account)
      console.log('✅ User created successfully. Email confirmation bypassed for admin-created account:', userData.email);

      // Emit real-time event
      realTimeService.emit('user-created', { user, createdBy });

      return user;
    }

    return null;
  } catch (error) {
    console.error('Error in createUser:', error);
    throw error; // Re-throw to show user the actual error
  }
};
// Function to update user password (admin-initiated, bypasses email confirmation)
export const updateUserPassword = async (userId: string, newPassword: string, updatedBy: string): Promise<boolean> => {
  try {
    console.log('Updating password for user:', userId);

    // Update password in Supabase Auth (bypasses email confirmation)
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      password: newPassword,
      user_metadata: {
        password_updated_by: updatedBy,
        password_updated_at: new Date().toISOString(),
        admin_updated: true
      }
    });

    if (error) {
      console.error('Error updating user password:', error);
      throw error;
    }

    console.log('✅ Password updated successfully for user:', userId);

    // Update the users table with timestamp
    const { error: dbError } = await supabase
      .from('users')
      .update({
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (dbError) {
      console.warn('Warning: Failed to update timestamp in users table:', dbError);
    }

    // Emit real-time event using existing event type
    realTimeService.emit('user-updated', { user: { id: userId }, updatedBy });

    return true;
  } catch (error) {
    console.error('Error in updateUserPassword:', error);
    return false;
  }
};

export const updateUser = async (id: string, updates: any, updatedBy: string): Promise<User | null> => {
  try {
    console.log('updateUser: Starting update for user:', id);
    console.log('updateUser: Updates received:', updates);

    // Handle password update separately if provided
    if (updates.password) {
      console.log('updateUser: Password update detected, updating password first');
      const passwordUpdateSuccess = await updateUserPassword(id, updates.password, updatedBy);
      if (!passwordUpdateSuccess) {
        throw new Error('Failed to update user password');
      }
      // Remove password from updates object to avoid processing it again
      delete updates.password;
    }

    // Separate updates for users table and user_profiles table
    // Users table fields (based on actual schema)
    const userUpdate: UserUpdate = {
      full_name: updates.full_name || updates.fullName,
      user_type: updates.user_type || updates.userType,
      phone: updates.phone,
      city: updates.city,
      is_active: updates.is_active !== undefined ? updates.is_active : updates.isActive,
      // Company information (these exist in users table)
      is_company: updates.is_company !== undefined ? updates.is_company : updates.isCompany,
      company_name: updates.company_name || updates.companyName,
      ice_number: updates.ice_number || updates.iceNumber,
      company_address: updates.company_address || updates.companyAddress,
      company_phone: updates.company_phone || updates.companyPhone,
      company_city: updates.company_city || updates.companyCity,
      company_email: updates.company_email || updates.companyEmail,
      tax_id: updates.tax_id || updates.taxId,
      legal_form: updates.legal_form || updates.legalForm,
      updated_at: new Date().toISOString()
    };

    // User profiles table fields (bio, job_title, etc.)
    const profileUpdate = {
      bio: updates.bio,
      job_title: updates.job_title || updates.jobTitle,
      updated_at: new Date().toISOString()
    };

    // Remove undefined values to avoid database errors
    Object.keys(userUpdate).forEach(key => {
      if (userUpdate[key as keyof UserUpdate] === undefined) {
        delete userUpdate[key as keyof UserUpdate];
      }
    });

    Object.keys(profileUpdate).forEach(key => {
      if (profileUpdate[key as keyof typeof profileUpdate] === undefined) {
        delete profileUpdate[key as keyof typeof profileUpdate];
      }
    });

    console.log('updateUser: Prepared users table update:', userUpdate);
    console.log('updateUser: Prepared user_profiles table update:', profileUpdate);

    // Update users table and fetch with profile data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .update(userUpdate)
      .eq('id', id)
      .select(`
        *,
        user_profiles (
          bio,
          job_title,
          avatar_url
        )
      `)
      .single();

    if (userError) {
      console.error('updateUser: Database error:', userError);
      console.error('updateUser: Error details:', JSON.stringify(userError, null, 2));
      console.error('updateUser: Update data that caused error:', JSON.stringify(userUpdate, null, 2));
      throw userError;
    }

    console.log('updateUser: Users table updated successfully:', updatedUser);

    // Update user profile if we have profile data
    if (Object.keys(profileUpdate).length > 1) { // More than just updated_at
      console.log('updateUser: Updating user profile:', profileUpdate);

      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .upsert({ user_id: id, ...profileUpdate })
        .select();

      if (profileError) {
        console.error('updateUser: Error updating user profile:', profileError);
        console.error('updateUser: Profile update data that caused error:', JSON.stringify(profileUpdate, null, 2));
        // Don't throw here, user was updated successfully
      } else {
        console.log('updateUser: User profile updated successfully:', profileData);
        console.log('updateUser: Profile update data:', profileUpdate);
      }
    }

    if (updatedUser) {
      // After updating both tables, wait a moment for database consistency, then fetch the complete user data
      console.log('updateUser: Waiting for database consistency...');
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay for database consistency

      console.log('updateUser: Fetching complete updated user data with profile...');
      const { data: completeUserData, error: fetchError } = await supabase
        .from('users')
        .select(`
          *,
          user_profiles (
            bio,
            job_title,
            avatar_url
          )
        `)
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('updateUser: Error fetching complete user data:', fetchError);
        // Fallback to original data
        const user = convertToUser(updatedUser, updatedUser.user_profiles?.[0]);
        console.log('updateUser: Using fallback user object:', user);
        return user;
      }

      console.log('updateUser: Complete user data fetched:', completeUserData);
      console.log('updateUser: Profile data from fetch:', completeUserData.user_profiles?.[0]);

      const user = convertToUser(completeUserData, completeUserData.user_profiles?.[0]);
      console.log('updateUser: Converted complete user object with updated profile:', user);
      console.log('updateUser: Final user department:', user.department);
      console.log('updateUser: Final user avatar:', user.avatar);

      // Emit real-time event
      realTimeService.emit('user-updated', { user, updatedBy });
      console.log('updateUser: Real-time event emitted');

      return user;
    }

    console.error('updateUser: No updated user data returned from database');
    return null;
  } catch (error) {
    console.error('Error in updateUser:', error);
    return null;
  }
};

export const getAdminUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('user_type', 'admin')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getAdminUsers:', error);
    return [];
  }
};

export const getManagerUsers = async (): Promise<User[]> => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .eq('user_type', 'manager')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching manager users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in getManagerUsers:', error);
    return [];
  }
};

export const deleteUser = async (id: string, deletedBy: string): Promise<boolean> => {
  try {
    console.log('deleteUser: Starting user deletion with cascade handling:', { id, deletedBy });

    // Step 1: Delete related records that don't have CASCADE constraints
    console.log('deleteUser: Cleaning up related records...');

    // Delete notifications (if not CASCADE)
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .eq('user_id', id);

    if (notificationsError) {
      console.warn('deleteUser: Error deleting notifications (continuing):', notificationsError);
    }

    // Delete wishlists
    const { error: wishlistsError } = await supabase
      .from('wishlists')
      .delete()
      .eq('customer_id', id);

    if (wishlistsError) {
      console.warn('deleteUser: Error deleting wishlists (continuing):', wishlistsError);
    }

    // Delete product reviews
    const { error: reviewsError } = await supabase
      .from('product_reviews')
      .delete()
      .eq('customer_id', id);

    if (reviewsError) {
      console.warn('deleteUser: Error deleting reviews (continuing):', reviewsError);
    }

    // Delete customer behavior tracking
    const { error: behaviorError } = await supabase
      .from('customer_behavior')
      .delete()
      .eq('customer_id', id);

    if (behaviorError) {
      console.warn('deleteUser: Error deleting customer behavior (continuing):', behaviorError);
    }

    // Delete order templates
    const { error: templatesError } = await supabase
      .from('order_templates')
      .delete()
      .eq('customer_id', id);

    if (templatesError) {
      console.warn('deleteUser: Error deleting order templates (continuing):', templatesError);
    }

    console.log('deleteUser: Related records cleaned up, proceeding with user deletion...');

    // Step 2: Now delete the user (CASCADE should handle user_profiles and customer_profiles)
    const { data, error } = await supabase
      .from('users')
      .delete()
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('deleteUser: Database error during user deletion:', error);
      throw error;
    }

    console.log('deleteUser: User deleted successfully from database:', data);

    // Step 3: Delete from auth
    try {
      await supabase.auth.admin.deleteUser(id);
      console.log('deleteUser: User deleted from auth successfully');
    } catch (authError) {
      console.warn('deleteUser: Auth deletion failed (non-critical):', authError);
    }

    // Step 4: Emit real-time event for immediate UI updates
    realTimeService.emit('user-deleted', { userId: id, deletedBy, deletedUser: data });

    console.log('deleteUser: User deletion completed successfully');
    return true;
  } catch (error) {
    console.error('Error in deleteUser:', error);
    return false;
  }
};
export const toggleUserStatus = async (id: string, updatedBy: string): Promise<User | null> => {
  try {
    // First get the current user to check status
    const currentUser = await getUserById(id);
    if (!currentUser) return null;

    // Prevent deactivating the last admin
    if (currentUser.userType === 'admin' && currentUser.isActive) {
      const activeAdmins = await getAdminUsers();
      const activeAdminCount = activeAdmins.filter(u => u.isActive).length;
      if (activeAdminCount <= 1) {
        throw new Error('Cannot deactivate the last active admin user');
      }
    }

    const newStatus = !currentUser.isActive;

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update({ is_active: newStatus })
      .eq('id', id)
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .single();

    if (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }

    if (updatedUser) {
      const user = convertToUser(updatedUser, updatedUser.user_profiles?.[0]);

      // Emit real-time event
      realTimeService.emit('user-status-changed', {
        userId: id,
        isActive: newStatus,
        updatedBy
      });

      return user;
    }

    return null;
  } catch (error) {
    console.error('Error in toggleUserStatus:', error);
    return null;
  }
};

// New function to update user status with multiple status options
export const updateUserStatus = async (id: string, status: 'active' | 'inactive' | 'pending' | 'suspended', updatedBy: string): Promise<User | null> => {
  try {
    console.log('updateUserStatus: Starting update:', { id, status, updatedBy });

    // First get the current user to check permissions
    const currentUser = await getUserById(id);
    console.log('updateUserStatus: Current user:', currentUser);

    if (!currentUser) {
      console.error('updateUserStatus: User not found');
      return null;
    }

    // Prevent changing status of the last admin
    if (currentUser.userType === 'admin' && status !== 'active') {
      const activeAdmins = await getAdminUsers();
      const activeAdminCount = activeAdmins.filter(u => u.isActive).length;
      if (activeAdminCount <= 1) {
        throw new Error('Cannot change status of the last active admin user');
      }
    }

    // Determine isActive based on status
    const isActive = status === 'active';
    console.log('updateUserStatus: Computed isActive:', isActive);

    const updateData = {
      is_active: isActive,
      updated_at: new Date().toISOString()
    };
    console.log('updateUserStatus: Update data:', updateData);

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .single();

    if (error) {
      console.error('updateUserStatus: Database error:', error);
      throw error;
    }

    console.log('updateUserStatus: Database update successful:', updatedUser);

    if (updatedUser) {
      const user = convertToUser(updatedUser, updatedUser.user_profiles?.[0]);
      console.log('updateUserStatus: Converted user:', user);

      // Emit real-time event
      realTimeService.emit('user-status-changed', {
        userId: id,
        isActive: isActive,
        updatedBy
      });

      console.log('updateUserStatus: Real-time event emitted');
      return user;
    }

    console.error('updateUserStatus: No updated user data returned');
    return null;
  } catch (error) {
    console.error('Error in updateUserStatus:', error);
    return null;
  }
};

export const searchUsers = async (query: string): Promise<User[]> => {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;

    const { data: users, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (
          avatar_url,
          job_title
        )
      `)
      .or(`full_name.ilike.${searchTerm},email.ilike.${searchTerm},company_name.ilike.${searchTerm}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching users:', error);
      throw error;
    }

    return users?.map(user => convertToUser(user, user.user_profiles?.[0])) || [];
  } catch (error) {
    console.error('Error in searchUsers:', error);
    return [];
  }
};

export const getUserStats = async (): Promise<{
  total: number;
  admins: number;
  managers: number;
  clients: number;
  resellers: number;
  active: number;
  inactive: number;
  recentlyCreated: number;
}> => {
  try {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    // Get total count
    const { count: total, error: totalError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    // Get counts by user type
    const { data: userTypes, error: typesError } = await supabase
      .from('users')
      .select('user_type')
      .not('user_type', 'is', null);

    // Get active/inactive counts
    const { count: active, error: activeError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get recently created count
    const { count: recentlyCreated, error: recentError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', weekAgo.toISOString());

    if (totalError || typesError || activeError || recentError) {
      console.error('Error getting user stats:', { totalError, typesError, activeError, recentError });
      throw new Error('Failed to get user statistics');
    }

    // Count user types
    const typeCounts = userTypes?.reduce((acc, user) => {
      acc[user.user_type] = (acc[user.user_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      total: total || 0,
      admins: typeCounts.admin || 0,
      managers: typeCounts.manager || 0,
      clients: typeCounts.client || 0,
      resellers: typeCounts.reseller || 0,
      active: active || 0,
      inactive: (total || 0) - (active || 0),
      recentlyCreated: recentlyCreated || 0
    };
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return {
      total: 0,
      admins: 0,
      managers: 0,
      clients: 0,
      resellers: 0,
      active: 0,
      inactive: 0,
      recentlyCreated: 0
    };
  }
};

export default {
  getUsers,
  getUserById,
  getAdminUsers,
  getManagerUsers,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  searchUsers,
  getUserStats
};
