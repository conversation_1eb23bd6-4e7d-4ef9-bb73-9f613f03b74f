/**
 * Server-Side Authorization Service for YalaOffice
 * Provides comprehensive permission validation and access control
 */

import { supabase } from '../integrations/supabase/client';
import { User, UserType } from '../types/userDefinitions';

/**
 * Permission definitions for different user types
 */
export const PERMISSIONS = {
  // User Management
  'users.create': ['admin'],
  'users.read': ['admin', 'manager'],
  'users.update': ['admin', 'manager'],
  'users.delete': ['admin'],
  
  // Product Management
  'products.create': ['admin', 'manager'],
  'products.read': ['admin', 'manager', 'client', 'reseller'],
  'products.update': ['admin', 'manager'],
  'products.delete': ['admin'],
  
  // Order Management
  'orders.create': ['admin', 'manager', 'client', 'reseller'],
  'orders.read': ['admin', 'manager', 'client', 'reseller', 'delivery_person'],
  'orders.update': ['admin', 'manager'],
  'orders.delete': ['admin'],
  'orders.assign_delivery': ['admin', 'manager'],
  'orders.update_status': ['admin', 'manager', 'delivery_person'],
  
  // Inventory Management
  'inventory.read': ['admin', 'manager'],
  'inventory.update': ['admin', 'manager'],
  'inventory.manage': ['admin', 'manager'],
  
  // Reports and Analytics
  'reports.read': ['admin', 'manager'],
  'reports.create': ['admin', 'manager'],
  'analytics.read': ['admin', 'manager'],
  
  // System Settings
  'settings.read': ['admin', 'manager'],
  'settings.write': ['admin'],
  'system.admin': ['admin'],
  
  // Branch Management
  'branches.read': ['admin', 'manager'],
  'branches.manage': ['admin', 'manager'],
  
  // Client Management
  'clients.read': ['admin', 'manager'],
  'clients.update': ['admin', 'manager'],
  'clients.manage': ['admin', 'manager'],
  
  // Promo Codes
  'promocodes.create': ['admin', 'manager'],
  'promocodes.read': ['admin', 'manager'],
  'promocodes.update': ['admin', 'manager'],
  'promocodes.delete': ['admin', 'manager'],
  
  // Reviews Management
  'reviews.read': ['admin', 'manager', 'client', 'reseller'],
  'reviews.update': ['admin', 'manager'],
  'reviews.manage': ['admin', 'manager'],
  'reviews.reply': ['admin', 'manager'],
  
  // Profile Management
  'profile.read': ['admin', 'manager', 'client', 'reseller', 'delivery_person'],
  'profile.write': ['admin', 'manager', 'client', 'reseller', 'delivery_person'],
  
  // Delivery Management
  'delivery.read': ['admin', 'manager', 'delivery_person'],
  'delivery.update': ['admin', 'manager', 'delivery_person'],
  'delivery.assign': ['admin', 'manager'],
} as const;

export type Permission = keyof typeof PERMISSIONS;

/**
 * Authorization result interface
 */
export interface AuthorizationResult {
  authorized: boolean;
  reason?: string;
  user?: User;
}

/**
 * Server-side authorization service
 */
export class AuthorizationService {
  /**
   * Validate user session and get user data from server
   */
  static async validateSession(): Promise<{ user: User | null; error: string | null }> {
    try {
      // Get current session from Supabase
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Session validation error:', sessionError);
        return { user: null, error: 'Invalid session' };
      }
      
      if (!session?.user) {
        return { user: null, error: 'No active session' };
      }
      
      // Get user profile from database (server-side validation)
      const { data: userProfile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .eq('is_active', true)
        .single();
      
      if (profileError || !userProfile) {
        console.error('User profile error:', profileError);
        return { user: null, error: 'User profile not found or inactive' };
      }
      
      // Convert database user to User type
      const user: User = {
        id: userProfile.id,
        email: userProfile.email,
        fullName: userProfile.full_name,
        userType: userProfile.user_type as UserType,
        phone: userProfile.phone,
        city: userProfile.city,
        isActive: userProfile.is_active,
        status: userProfile.status,
        createdAt: userProfile.created_at,
        lastLogin: userProfile.last_login,
        isCompany: userProfile.is_company,
        companyName: userProfile.company_name,
        iceNumber: userProfile.ice_number,
        companyAddress: userProfile.company_address,
        companyPhone: userProfile.company_phone,
        companyCity: userProfile.company_city,
        companyEmail: userProfile.company_email,
        taxId: userProfile.tax_id,
        legalForm: userProfile.legal_form,
        permissions: this.getUserPermissions(userProfile.user_type as UserType)
      };
      
      return { user, error: null };
    } catch (error) {
      console.error('Session validation error:', error);
      return { user: null, error: 'Session validation failed' };
    }
  }
  
  /**
   * Get permissions for a user type
   */
  static getUserPermissions(userType: UserType): string[] {
    const permissions: string[] = [];
    
    Object.entries(PERMISSIONS).forEach(([permission, allowedRoles]) => {
      if (allowedRoles.includes(userType)) {
        permissions.push(permission);
      }
    });
    
    return permissions;
  }
  
  /**
   * Check if user has specific permission (server-side validation)
   */
  static async hasPermission(permission: Permission): Promise<AuthorizationResult> {
    try {
      // Validate session and get user
      const { user, error } = await this.validateSession();
      
      if (error || !user) {
        return {
          authorized: false,
          reason: error || 'User not authenticated',
        };
      }
      
      // Check if user type has the required permission
      const allowedRoles = PERMISSIONS[permission];
      const hasPermission = allowedRoles.includes(user.userType);
      
      if (!hasPermission) {
        return {
          authorized: false,
          reason: `User type '${user.userType}' does not have permission '${permission}'`,
          user,
        };
      }
      
      return {
        authorized: true,
        user,
      };
    } catch (error) {
      console.error('Permission check error:', error);
      return {
        authorized: false,
        reason: 'Permission check failed',
      };
    }
  }
  
  /**
   * Check multiple permissions at once
   */
  static async hasAnyPermission(permissions: Permission[]): Promise<AuthorizationResult> {
    try {
      const { user, error } = await this.validateSession();
      
      if (error || !user) {
        return {
          authorized: false,
          reason: error || 'User not authenticated',
        };
      }
      
      // Check if user has any of the required permissions
      const hasAnyPermission = permissions.some(permission => {
        const allowedRoles = PERMISSIONS[permission];
        return allowedRoles.includes(user.userType);
      });
      
      if (!hasAnyPermission) {
        return {
          authorized: false,
          reason: `User type '${user.userType}' does not have any of the required permissions: ${permissions.join(', ')}`,
          user,
        };
      }
      
      return {
        authorized: true,
        user,
      };
    } catch (error) {
      console.error('Multiple permission check error:', error);
      return {
        authorized: false,
        reason: 'Permission check failed',
      };
    }
  }
  
  /**
   * Check if user can access specific resource
   */
  static async canAccessResource(
    resourceType: string,
    resourceId: string,
    action: 'read' | 'write' | 'delete' = 'read'
  ): Promise<AuthorizationResult> {
    try {
      const { user, error } = await this.validateSession();
      
      if (error || !user) {
        return {
          authorized: false,
          reason: error || 'User not authenticated',
        };
      }
      
      // Build permission string
      const permission = `${resourceType}.${action}` as Permission;
      
      // Check basic permission
      const basicPermissionResult = await this.hasPermission(permission);
      if (!basicPermissionResult.authorized) {
        return basicPermissionResult;
      }
      
      // Additional resource-specific checks
      switch (resourceType) {
        case 'orders':
          return await this.checkOrderAccess(user, resourceId, action);
        case 'users':
          return await this.checkUserAccess(user, resourceId, action);
        default:
          return basicPermissionResult;
      }
    } catch (error) {
      console.error('Resource access check error:', error);
      return {
        authorized: false,
        reason: 'Resource access check failed',
      };
    }
  }
  
  /**
   * Check order-specific access rules
   */
  private static async checkOrderAccess(
    user: User,
    orderId: string,
    action: string
  ): Promise<AuthorizationResult> {
    // Admins and managers can access all orders
    if (['admin', 'manager'].includes(user.userType)) {
      return { authorized: true, user };
    }
    
    // Clients can only access their own orders
    if (['client', 'reseller'].includes(user.userType)) {
      const { data: order } = await supabase
        .from('orders')
        .select('customer_id')
        .eq('id', orderId)
        .single();
      
      if (order?.customer_id === user.id) {
        return { authorized: true, user };
      }
      
      return {
        authorized: false,
        reason: 'Can only access own orders',
        user,
      };
    }
    
    // Delivery persons can access assigned orders
    if (user.userType === 'delivery_person') {
      const { data: order } = await supabase
        .from('orders')
        .select('assigned_delivery_person')
        .eq('id', orderId)
        .single();
      
      if (order?.assigned_delivery_person === user.id) {
        return { authorized: true, user };
      }
      
      return {
        authorized: false,
        reason: 'Can only access assigned orders',
        user,
      };
    }
    
    return {
      authorized: false,
      reason: 'Insufficient permissions for order access',
      user,
    };
  }
  
  /**
   * Check user-specific access rules
   */
  private static async checkUserAccess(
    user: User,
    targetUserId: string,
    action: string
  ): Promise<AuthorizationResult> {
    // Admins can access all users
    if (user.userType === 'admin') {
      return { authorized: true, user };
    }
    
    // Users can access their own profile
    if (user.id === targetUserId && action === 'read') {
      return { authorized: true, user };
    }
    
    // Managers can read client/reseller profiles
    if (user.userType === 'manager' && action === 'read') {
      const { data: targetUser } = await supabase
        .from('users')
        .select('user_type')
        .eq('id', targetUserId)
        .single();
      
      if (targetUser && ['client', 'reseller'].includes(targetUser.user_type)) {
        return { authorized: true, user };
      }
    }
    
    return {
      authorized: false,
      reason: 'Insufficient permissions for user access',
      user,
    };
  }
}
