/**
 * Supabase Optimization Configuration
 * Optimizes connection settings, pooling, and performance configurations
 */

import { createClient, SupabaseClientOptions } from '@supabase/supabase-js';
import type { Database } from '../integrations/supabase/types';
import { env } from './environment';

// Performance-optimized Supabase client configuration
export const optimizedSupabaseConfig: SupabaseClientOptions<'public'> = {
  auth: {
    // Enhanced security and performance settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: window.sessionStorage, // Use sessionStorage for better security
    flowType: 'pkce'
  },
  db: {
    schema: 'public'
  },
  realtime: {
    // Optimize real-time connections
    params: {
      eventsPerSecond: 10, // Limit events to prevent overwhelming
      heartbeatIntervalMs: 30000, // 30 second heartbeat
      reconnectDelayMs: 1000, // 1 second reconnect delay
      timeoutMs: 20000 // 20 second timeout
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'yalaoffice-optimized',
      'Cache-Control': 'max-age=300', // 5 minute cache for static requests
      'Connection': 'keep-alive'
    },
    // Add request timeout
    fetch: (url, options = {}) => {
      return fetch(url, {
        ...options,
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });
    }
  }
};

// Create optimized Supabase client
export const createOptimizedSupabaseClient = () => {
  return createClient<Database>(
    env.supabase.url,
    env.supabase.anonKey,
    optimizedSupabaseConfig
  );
};

// Connection pool management
class SupabaseConnectionManager {
  private static instance: SupabaseConnectionManager;
  private client: ReturnType<typeof createOptimizedSupabaseClient>;
  private connectionCount = 0;
  private maxConnections = 10;

  private constructor() {
    this.client = createOptimizedSupabaseClient();
    this.setupConnectionMonitoring();
  }

  static getInstance(): SupabaseConnectionManager {
    if (!SupabaseConnectionManager.instance) {
      SupabaseConnectionManager.instance = new SupabaseConnectionManager();
    }
    return SupabaseConnectionManager.instance;
  }

  getClient() {
    return this.client;
  }

  private setupConnectionMonitoring() {
    // Monitor connection health
    setInterval(() => {
      this.checkConnectionHealth();
    }, 60000); // Check every minute
  }

  private async checkConnectionHealth() {
    try {
      // Simple health check query
      const { error } = await this.client
        .from('products')
        .select('id')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.warn('Supabase connection health check failed:', error);
        this.reconnect();
      }
    } catch (error) {
      console.error('Connection health check error:', error);
    }
  }

  private reconnect() {
    console.log('Reconnecting to Supabase...');
    this.client = createOptimizedSupabaseClient();
  }

  getConnectionStats() {
    return {
      connectionCount: this.connectionCount,
      maxConnections: this.maxConnections,
      isHealthy: true // Would implement actual health check
    };
  }
}

// Database query optimization helpers
export class QueryOptimizer {
  /**
   * Optimize select queries by limiting fields
   */
  static optimizeSelect(baseSelect: string, fields?: string[]): string {
    if (!fields || fields.length === 0) {
      return baseSelect;
    }
    return fields.join(', ');
  }

  /**
   * Add performance hints to queries
   */
  static addPerformanceHints(query: any) {
    // Add query hints for better performance
    return query
      .limit(1000) // Prevent runaway queries
      .abortSignal(AbortSignal.timeout(30000)); // 30 second timeout
  }

  /**
   * Optimize pagination queries
   */
  static optimizePagination(query: any, page: number, limit: number) {
    const offset = (page - 1) * limit;
    return query.range(offset, offset + limit - 1);
  }

  /**
   * Add caching headers to queries
   */
  static addCachingHeaders(query: any, cacheSeconds: number = 300) {
    return query.headers({
      'Cache-Control': `max-age=${cacheSeconds}`,
      'Vary': 'Authorization'
    });
  }
}

// Real-time optimization
export class RealtimeOptimizer {
  private static subscriptions = new Map<string, any>();
  private static maxSubscriptions = 20;

  /**
   * Create optimized real-time subscription
   */
  static createOptimizedSubscription(
    client: any,
    table: string,
    callback: (payload: any) => void,
    options: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
      filter?: string;
      throttleMs?: number;
    } = {}
  ) {
    const { event = '*', filter, throttleMs = 1000 } = options;
    const subscriptionKey = `${table}:${event}:${filter || 'all'}`;

    // Check if we already have this subscription
    if (this.subscriptions.has(subscriptionKey)) {
      console.warn(`Subscription already exists for ${subscriptionKey}`);
      return this.subscriptions.get(subscriptionKey);
    }

    // Check subscription limit
    if (this.subscriptions.size >= this.maxSubscriptions) {
      console.warn('Maximum subscriptions reached, cleaning up old ones');
      this.cleanupOldSubscriptions();
    }

    // Create throttled callback
    let lastCall = 0;
    const throttledCallback = (payload: any) => {
      const now = Date.now();
      if (now - lastCall >= throttleMs) {
        lastCall = now;
        callback(payload);
      }
    };

    // Create subscription
    const subscription = client
      .channel(`${table}-changes-${Date.now()}`)
      .on('postgres_changes', {
        event,
        schema: 'public',
        table,
        filter
      }, throttledCallback)
      .subscribe();

    this.subscriptions.set(subscriptionKey, subscription);
    return subscription;
  }

  /**
   * Clean up old subscriptions
   */
  private static cleanupOldSubscriptions() {
    const subscriptionsArray = Array.from(this.subscriptions.entries());
    const toRemove = subscriptionsArray.slice(0, 5); // Remove oldest 5

    toRemove.forEach(([key, subscription]) => {
      subscription.unsubscribe();
      this.subscriptions.delete(key);
    });
  }

  /**
   * Get subscription statistics
   */
  static getStats() {
    return {
      activeSubscriptions: this.subscriptions.size,
      maxSubscriptions: this.maxSubscriptions
    };
  }
}

// Export optimized client instance
export const optimizedSupabase = SupabaseConnectionManager.getInstance().getClient();

// Performance monitoring
export const getSupabasePerformanceMetrics = () => {
  const connectionManager = SupabaseConnectionManager.getInstance();
  return {
    connection: connectionManager.getConnectionStats(),
    realtime: RealtimeOptimizer.getStats()
  };
};

export default {
  optimizedSupabase,
  QueryOptimizer,
  RealtimeOptimizer,
  getSupabasePerformanceMetrics
};
