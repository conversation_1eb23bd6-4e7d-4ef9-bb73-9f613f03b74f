/**
 * Secure Environment Configuration for YalaOffice
 * Centralizes environment variable access with validation and type safety
 */

interface EnvironmentConfig {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
  };
  
  // Application Configuration
  app: {
    name: string;
    version: string;
    description: string;
  };
  
  // API Configuration
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  
  // Security Configuration
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  
  // Feature Flags
  features: {
    analytics: boolean;
    reports: boolean;
    notifications: boolean;
    mobileApp: boolean;
  };
  
  // Development Settings
  development: {
    debug: boolean;
    devtools: boolean;
    mockApi: boolean;
  };
  
  // Localization
  localization: {
    defaultLanguage: string;
    defaultCurrency: string;
    defaultTimezone: string;
  };
  
  // Monitoring
  monitoring: {
    enabled: boolean;
    logLevel: string;
  };
}

/**
 * Validates required environment variables
 */
function validateEnvironment(): void {
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missingVars = requiredVars.filter(varName => !import.meta.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }
  
  // Validate Supabase URL format
  try {
    new URL(import.meta.env.VITE_SUPABASE_URL);
  } catch {
    throw new Error('VITE_SUPABASE_URL must be a valid URL');
  }
  
  // Validate Supabase key format (basic JWT structure check)
  const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  if (!anonKey || !anonKey.includes('.') || anonKey.split('.').length !== 3) {
    throw new Error('VITE_SUPABASE_ANON_KEY appears to be invalid (not a valid JWT format)');
  }
}

/**
 * Safely gets environment variable with fallback
 */
function getEnvVar(key: string, fallback: string = ''): string {
  return import.meta.env[key] || fallback;
}

/**
 * Safely gets boolean environment variable
 */
function getBooleanEnvVar(key: string, fallback: boolean = false): boolean {
  const value = import.meta.env[key];
  if (value === undefined) return fallback;
  return value === 'true' || value === '1';
}

/**
 * Safely gets number environment variable
 */
function getNumberEnvVar(key: string, fallback: number = 0): number {
  const value = import.meta.env[key];
  if (value === undefined) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
}

// Validate environment on module load
validateEnvironment();

/**
 * Centralized environment configuration
 */
export const env: EnvironmentConfig = {
  supabase: {
    url: getEnvVar('VITE_SUPABASE_URL'),
    anonKey: getEnvVar('VITE_SUPABASE_ANON_KEY'),
  },
  
  app: {
    name: getEnvVar('VITE_APP_NAME', 'YalaOffice'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    description: getEnvVar('VITE_APP_DESCRIPTION', 'Smart Supply Management System'),
  },
  
  api: {
    baseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000'),
    timeout: getNumberEnvVar('VITE_API_TIMEOUT', 30000),
    retryAttempts: getNumberEnvVar('VITE_API_RETRY_ATTEMPTS', 3),
  },
  
  security: {
    sessionTimeout: getNumberEnvVar('VITE_SESSION_TIMEOUT', 86400), // 24 hours
    maxLoginAttempts: getNumberEnvVar('VITE_MAX_LOGIN_ATTEMPTS', 5),
    lockoutDuration: getNumberEnvVar('VITE_LOCKOUT_DURATION', 15), // minutes
  },
  
  features: {
    analytics: getBooleanEnvVar('VITE_FEATURE_ANALYTICS', true),
    reports: getBooleanEnvVar('VITE_FEATURE_REPORTS', true),
    notifications: getBooleanEnvVar('VITE_FEATURE_NOTIFICATIONS', true),
    mobileApp: getBooleanEnvVar('VITE_FEATURE_MOBILE_APP', true),
  },
  
  development: {
    debug: getBooleanEnvVar('VITE_DEBUG', false),
    devtools: getBooleanEnvVar('VITE_DEVTOOLS', import.meta.env.DEV),
    mockApi: getBooleanEnvVar('VITE_MOCK_API', false),
  },
  
  localization: {
    defaultLanguage: getEnvVar('VITE_DEFAULT_LANGUAGE', 'en'),
    defaultCurrency: getEnvVar('VITE_DEFAULT_CURRENCY', 'MAD'),
    defaultTimezone: getEnvVar('VITE_DEFAULT_TIMEZONE', 'Africa/Casablanca'),
  },
  
  monitoring: {
    enabled: getBooleanEnvVar('VITE_MONITORING_ENABLED', false),
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'info'),
  },
};

/**
 * Check if running in development mode
 */
export const isDevelopment = import.meta.env.DEV;

/**
 * Check if running in production mode
 */
export const isProduction = import.meta.env.PROD;

/**
 * Get current environment name
 */
export const environmentName = import.meta.env.MODE || 'development';

/**
 * Utility to safely log environment info (without sensitive data)
 */
export function logEnvironmentInfo(): void {
  if (env.development.debug) {
    console.log('🔧 Environment Configuration:', {
      mode: environmentName,
      app: env.app,
      features: env.features,
      development: env.development,
      localization: env.localization,
      // Never log sensitive data like keys or URLs
      supabase: {
        configured: !!env.supabase.url && !!env.supabase.anonKey,
      },
    });
  }
}

// Log environment info on module load (development only)
if (isDevelopment) {
  logEnvironmentInfo();
}
