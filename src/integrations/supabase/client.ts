// Secure Supabase client configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { env } from '../../config/environment';

// Get validated Supabase configuration from secure environment config
const SUPABASE_URL = env.supabase.url;
const SUPABASE_PUBLISHABLE_KEY = env.supabase.anonKey;

// Create Supabase client with security configurations
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    // Enhanced security settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Prevent session storage in localStorage for enhanced security
    storage: window.sessionStorage,
    // Add security headers
    flowType: 'pkce'
  },
  db: {
    // Add connection security
    schema: 'public'
  },
  global: {
    // Add request headers for security
    headers: {
      'X-Client-Info': 'yalaoffice-web'
    }
  }
});

// Create admin client for admin operations using service role key
// WARNING: This should only be used for server-side operations or in secure contexts
// Never expose the service role key in client-side code in production
export const createAdminClient = () => {
  // For development, we'll temporarily use VITE_ prefix to access the service role key
  // In production, this should be handled server-side only
  const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
                         import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!serviceRoleKey) {
    console.warn('Service role key not available, falling back to anon key');
    return createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        flowType: 'pkce'
      },
      db: {
        schema: 'public'
      },
      global: {
        headers: {
          'X-Client-Info': 'yalaoffice-admin'
        }
      }
    });
  }

  console.log('Using service role key for admin operations');
  return createClient<Database>(SUPABASE_URL, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
      flowType: 'pkce'
    },
    db: {
      schema: 'public'
    },
    global: {
      headers: {
        'X-Client-Info': 'yalaoffice-admin-service'
      }
    }
  });
};