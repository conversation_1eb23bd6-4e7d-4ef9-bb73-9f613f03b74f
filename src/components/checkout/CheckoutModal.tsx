
import { useState } from 'react';
import { X, MapPin, Truck, Tag } from 'lucide-react';
import { PromoCode } from '../../types/promoCode';
import { MOROCCAN_CITIES } from '../../constants/cities';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  cartItems: any[];
  subtotal: number;
  deliveryFee: number;
  discountAmount: number;
  appliedPromo: PromoCode | null;
  onCheckout: (orderData: any) => void;
}

const CheckoutModal = ({ 
  isOpen, 
  onClose, 
  cartItems, 
  subtotal, 
  deliveryFee, 
  discountAmount, 
  appliedPromo, 
  onCheckout 
}: CheckoutModalProps) => {
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    postalCode: '',
    paymentMethod: 'cash',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const total = subtotal + deliveryFee - discountAmount;



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      const orderData = {
        ...formData,
        items: cartItems,
        subtotal,
        deliveryFee,
        discountAmount,
        total,
        promoCode: appliedPromo?.code || undefined,
        orderDate: new Date().toISOString()
      };

      await onCheckout(orderData);
      onClose();
    } catch (error) {
      console.error('Checkout error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Checkout</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Delivery Address */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              Delivery Address
            </h3>
            <div className="space-y-4">
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Street Address
                </label>
                <input
                  type="text"
                  placeholder="Enter your complete street address"
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <select
                  value={formData.city}
                  onChange={(e) => setFormData({...formData, city: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                >
                  <option value="">Select City</option>
                  {MOROCCAN_CITIES.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
                <input
                  type="text"
                  placeholder="Postal Code"
                  value={formData.postalCode}
                  onChange={(e) => setFormData({...formData, postalCode: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                />
              </div>


            </div>
          </div>

          {/* Applied Promo Code Display */}
          {appliedPromo && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                Applied Promo Code
              </h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-green-800 font-medium">Code: {appliedPromo.code}</p>
                    <p className="text-green-600 text-sm">{appliedPromo.description}</p>
                    <p className="text-green-600 text-sm">Discount: -{discountAmount.toFixed(2)} DH</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payment Method */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Payment Method
            </h3>
            <div className="space-y-3">
              {/* Cash Payment */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="cash"
                    checked={formData.paymentMethod === 'cash'}
                    onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                    className="text-teal-600 focus:ring-teal-500"
                  />
                  <div>
                    <span className="font-medium text-gray-900">Cash</span>
                    <p className="text-sm text-gray-600">Pay when your order is delivered</p>
                  </div>
                </label>
              </div>

              {/* Bank Transfer */}
              <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank_transfer"
                    checked={formData.paymentMethod === 'bank_transfer'}
                    onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                    className="text-teal-600 focus:ring-teal-500"
                  />
                  <div>
                    <span className="font-medium text-gray-900">Bank Transfer</span>
                    <p className="text-sm text-gray-600">Transfer payment to our bank account</p>
                  </div>
                </label>
              </div>

              {/* Check Payment */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="check"
                    checked={formData.paymentMethod === 'check'}
                    onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                    className="text-teal-600 focus:ring-teal-500"
                  />
                  <div>
                    <span className="font-medium text-gray-900">Check</span>
                    <p className="text-sm text-gray-600">Pay by check upon delivery or pickup</p>
                  </div>
                </label>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{subtotal.toFixed(2)} DH</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee</span>
                <span>{deliveryFee.toFixed(2)} DH</span>
              </div>
              {discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount</span>
                  <span>-{discountAmount.toFixed(2)} DH</span>
                </div>
              )}
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{total.toFixed(2)} DH</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Notes (Optional)</h3>
            <textarea
              placeholder="Special instructions or notes for your order"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 resize-none"
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-teal-600 text-white py-3 px-6 rounded-lg hover:bg-teal-700 transition-colors flex items-center justify-center space-x-2 disabled:bg-gray-400"
          >
            <Truck className="h-5 w-5" />
            <span>
              {isSubmitting ? 'Processing...' : `Place Order - ${total.toFixed(2)} DH`}
            </span>
          </button>
        </form>
      </div>
    </div>
  );
};

export default CheckoutModal;
