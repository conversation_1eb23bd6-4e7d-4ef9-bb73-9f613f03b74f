
import { useState } from 'react';
import { Download, FileText, Send, Eye } from 'lucide-react';
import { Order } from '../../types/order';
import Invoice from '../orders/Invoice';
import { useAuth } from '../../contexts/AuthContext';

interface InvoiceGeneratorProps {
  order: Order;
}

const InvoiceGenerator = ({ order }: InvoiceGeneratorProps) => {
  const [showInvoice, setShowInvoice] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  // Get authenticated user data for Bill To section
  const { user } = useAuth();

  const handleGeneratePDF = () => {
    // In a real app, this would generate and download a PDF
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Invoice ${order.id}</title>
            <style>
              body { font-family: Arial, sans-serif; }
              .invoice-header { border-bottom: 2px solid #333; padding-bottom: 20px; }
              .invoice-details { margin: 20px 0; }
              table { width: 100%; border-collapse: collapse; }
              th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
              th { background-color: #f5f5f5; }
              .total { font-weight: bold; font-size: 18px; }
            </style>
          </head>
          <body>
            <div class="invoice-header">
              <h1>YalaOffice Invoice</h1>
              <p>Invoice #: INV-${order.id}</p>
              <p>Order #: ${order.id}</p>
              <p>Date: ${new Date(order.createdAt).toLocaleDateString()}</p>
            </div>
            
            <div class="invoice-details">
              <h2>Bill To:</h2>
              <p><strong>${user?.fullName || 'Unknown User'}</strong></p>
              <p>${user?.email || 'No email provided'}</p>
              ${user?.isCompany && user?.companyName ? `<p>Company: ${user.companyName}</p>` : ''}
              ${user?.companyAddress ? `<p>${user.companyAddress}</p>` : ''}
              ${user?.city || user?.companyCity ? `<p>${user.city || user.companyCity}</p>` : ''}
              <p>Phone: ${user?.phone || user?.companyPhone || 'No phone provided'}</p>
              ${user?.taxId ? `<p>Tax ID: ${user.taxId}</p>` : ''}
              ${user?.iceNumber ? `<p>ICE Number: ${user.iceNumber}</p>` : ''}
            </div>

            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                ${order.items.map(item => `
                  <tr>
                    <td>${item.title}</td>
                    <td>${item.quantity}</td>
                    <td>${item.price.toFixed(2)} Dh</td>
                    <td>${(item.quantity * item.price).toFixed(2)} Dh</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div style="margin-top: 20px; text-align: right;">
              <p>Subtotal: ${order.subtotal.toFixed(2)} Dh</p>
              <p>Delivery: ${order.deliveryFee === 0 ? 'Free' : order.deliveryFee.toFixed(2) + ' Dh'}</p>
              <p class="total">Total: ${order.total.toFixed(2)} Dh</p>
            </div>

            <div style="margin-top: 40px; text-align: center; color: #666;">
              <p>Thank you for your business!</p>
              <p>YalaOffice - Office & School Supplies</p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleEmailInvoice = async () => {
    // In a real app, this would send the invoice via email
    setEmailSent(true);
    setTimeout(() => setEmailSent(false), 3000);
  };

  return (
    <>
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6 text-teal-600" />
            <h3 className="text-lg font-semibold text-gray-900">Invoice Actions</h3>
          </div>
          <span className="text-sm text-gray-500">Order: {order.id}</span>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => setShowInvoice(true)}
              className="flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Eye className="h-4 w-4" />
              <span>Preview</span>
            </button>

            <button
              onClick={handleGeneratePDF}
              className="flex items-center justify-center space-x-2 p-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Download PDF</span>
            </button>

            <button
              onClick={handleEmailInvoice}
              disabled={emailSent}
              className={`flex items-center justify-center space-x-2 p-3 rounded-lg transition-colors ${
                emailSent
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'border border-gray-300 hover:bg-gray-50'
              }`}
            >
              <Send className="h-4 w-4" />
              <span>{emailSent ? 'Email Sent!' : 'Email Invoice'}</span>
            </button>
          </div>

          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Customer:</strong> {user?.fullName || 'Unknown User'}</p>
            <p><strong>Email:</strong> {user?.email || 'No email provided'}</p>
            {user?.isCompany && user?.companyName && (
              <p><strong>Company:</strong> {user.companyName}</p>
            )}
            <p><strong>Amount:</strong> {order.total.toFixed(2)} Dh</p>
            <p><strong>Payment Status:</strong> {order.paymentStatus}</p>
          </div>
        </div>
      </div>

      {showInvoice && (
        <Invoice
          order={order}
          onClose={() => setShowInvoice(false)}
          paymentMethod={order.paymentMethod as 'bank_transfer' | 'check' | 'cash'}
        />
      )}
    </>
  );
};

export default InvoiceGenerator;
