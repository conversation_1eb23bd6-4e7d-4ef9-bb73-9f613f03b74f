
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Package, Upload, DollarSign, Hash } from 'lucide-react';
import { useSyncedCategories, useProductOperations } from '../../hooks/useSyncedData';
import { Category } from '../../types/inventory';

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProductCreated?: () => void;
}

const AddProductModal = ({ isOpen, onClose, onProductCreated }: AddProductModalProps) => {
  // Use synchronized categories from database
  const { data: categories, loading: categoriesLoading, error: categoriesError } = useSyncedCategories();
  const { createProduct, loading: creatingProduct, error: createError } = useProductOperations();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    brand: '',
    price: '',
    resellerPrice: '',
    stock: '',
    minStock: '',
    sku: '',
    weight: '',
    tags: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title || !formData.category || !formData.price || !formData.sku) {
        alert('Please fill in all required fields');
        return;
      }

      // Prepare product data
      const productData = {
        title: formData.title,
        description: formData.description,
        category_id: formData.category, // This should be a UUID from the select
        brand: formData.brand,
        price: parseFloat(formData.price),
        resellerPrice: formData.resellerPrice ? parseFloat(formData.resellerPrice) : undefined,
        stock: parseInt(formData.stock) || 0,
        min_stock: parseInt(formData.minStock) || 0,
        sku: formData.sku,
        weight: formData.weight ? parseFloat(formData.weight) : undefined,
        featured_image: '/placeholder.svg',
        thumbnail_images: [],
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : []
      };

      console.log('Creating product with data:', productData);

      const result = await createProduct(productData);

      if (result.success) {
        alert('Product created successfully!');
        // Reset form
        setFormData({
          title: '',
          description: '',
          category: '',
          brand: '',
          price: '',
          resellerPrice: '',
          stock: '',
          minStock: '',
          sku: '',
          weight: '',
          tags: ''
        });
        onProductCreated?.();
        onClose();
      } else {
        alert(result.error || 'Failed to create product');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      alert('Error creating product: ' + (error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Add New Product
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Product Title</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                placeholder="Enter product title"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                required
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                disabled={categoriesLoading}
              >
                <option value="">
                  {categoriesLoading ? 'Loading categories...' : 'Select category'}
                </option>
                {categories.map((category: Category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {categoriesError && (
                <p className="text-sm text-red-500 mt-1">Error loading categories</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Brand</label>
              <input
                type="text"
                value={formData.brand}
                onChange={(e) => setFormData({...formData, brand: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                placeholder="Enter brand name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">SKU</label>
              <div className="relative">
                <Hash className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  required
                  value={formData.sku}
                  onChange={(e) => setFormData({...formData, sku: e.target.value})}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter SKU"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Regular Price (Dh)</label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  type="number"
                  step="0.01"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: e.target.value})}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  placeholder="0.00"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Reseller Price (Dh)</label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  type="number"
                  step="0.01"
                  value={formData.resellerPrice}
                  onChange={(e) => setFormData({...formData, resellerPrice: e.target.value})}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  placeholder="0.00"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Stock Quantity</label>
              <input
                type="number"
                required
                value={formData.stock}
                onChange={(e) => setFormData({...formData, stock: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                placeholder="0"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Minimum Stock</label>
              <input
                type="number"
                value={formData.minStock}
                onChange={(e) => setFormData({...formData, minStock: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                placeholder="0"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
              rows={3}
              placeholder="Enter product description"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Tags (comma separated)</label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData({...formData, tags: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
              placeholder="office, supplies, writing"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Product Images</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Click to upload images or drag and drop</p>
              <p className="text-sm text-gray-500">PNG, JPG, JPEG up to 5MB each</p>
            </div>
          </div>
          
          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1" disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" className="flex-1" disabled={isSubmitting || categoriesLoading}>
              {isSubmitting ? 'Creating...' : 'Create Product'}
            </Button>
          </div>

          {createError && (
            <div className="text-red-500 text-sm mt-2">
              Error: {createError}
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddProductModal;
