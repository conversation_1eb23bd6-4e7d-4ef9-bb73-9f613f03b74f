import { useState, useEffect } from 'react';
import { User, Package, MapPin, ShoppingCart, BarChart3, Settings, FolderPlus, Users, Building2, TrendingUp, Plus, UserPlus, FileText, ClipboardList, MessageSquare, Star, Tag, RefreshCw } from 'lucide-react';
import NavigationTabs from './components/NavigationTabs';
import { liveDashboardService, DashboardStats, StoreManagerStats } from '../../services/liveDashboardService';
import { getDashboardAnalytics, DashboardAnalytics } from '../../services/analyticsService';
import { realTimeService } from '../../services/realTimeService';
import TabContent from './components/TabContent';

import { useResponsive, useResponsiveSpacing } from '../../hooks/useResponsive';
import { useMobileUtils } from '../../hooks/use-mobile';
import { formatCurrency } from '../../utils/currency';
import ProfileManagement from '../profile/ProfileManagement';
import PageLayout from '../layout/PageLayout';
import ProductManagement from '../inventory/ProductManagement';
import BranchManagement from '../branches/BranchManagement';
import CategoryManagement from '../inventory/CategoryManagement';
import OrderManagement from '../orders/OrderManagement';
import PromoCodesPage from '../pages/PromoCodesPage';
import ReviewsManagementPage from '../pages/ReviewsManagementPage';
import ClientManagement from '../clients/ClientManagement';
import NotificationsPage from '../pages/NotificationsPage';
import InvoiceManagementPage from '../pages/InvoiceManagementPage';

interface StoreManagerDashboardProps {
  user: any;
  activeTab: string;
  onTabChange: (tab: string) => void;
  onNavigate: (page: string, tab?: string) => void;
  onNavigateFromHeader?: (navigate: (page: string, tab?: string) => void) => void;
}

const StoreManagerDashboard = ({ 
  user, 
  activeTab, 
  onTabChange, 
  onNavigate,
  onNavigateFromHeader 
}: StoreManagerDashboardProps) => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [storeManagerStats, setStoreManagerStats] = useState<StoreManagerStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [lowStockAlerts, setLowStockAlerts] = useState<any[]>([]);
  const [analytics, setAnalytics] = useState<DashboardAnalytics | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const { isMobile, isTablet, isDesktop } = useResponsive();
  const { spacing } = useResponsiveSpacing();
  const { isMobileDevice } = useMobileUtils();

  // Load dashboard statistics
  const loadDashboardStats = async () => {
    try {
      setStatsLoading(true);
      console.log('StoreManagerDashboard: Loading dashboard stats...');

      // Use Store Manager specific stats (excludes financial data)
      const stats = await liveDashboardService.getStoreManagerDashboardStats();
      console.log('StoreManagerDashboard: Loaded Store Manager stats:', stats);

      setStoreManagerStats(stats);

      // Convert StoreManagerStats to DashboardStats format for compatibility with existing components
      const dashboardStatsFormat: DashboardStats = {
        totalProducts: stats.totalProducts,
        totalOrders: stats.totalOrders,
        totalCustomers: stats.totalCustomers,
        totalRevenue: 0, // Excluded for Store Managers
        lowStockProducts: stats.lowStockItems,
        pendingOrders: stats.pendingOrders,
        completedOrders: stats.completedToday,
        processingOrders: stats.processingOrders,
        shippedOrders: stats.shippedOrders,
        deliveredOrders: stats.deliveredOrders,
        cancelledOrders: stats.cancelledOrders,
        todaysSales: 0, // Excluded for Store Managers
        thisMonthSales: 0, // Excluded for Store Managers
        totalUsers: 0,
        activeUsers: 0,
        totalBranches: 0,
        activeBranches: 0,
        pendingTransfers: 0,
        inventoryItems: stats.inventoryItems,
        assignedDeliveries: 0,
        unassignedOrders: 0,
        totalPromoCodes: 0,
        activePromoCodes: 0,
        totalPromoUsage: 0,
        recentOrders: stats.recentOrders,
        topProducts: stats.topProducts,
        salesTrend: []
      };

      setDashboardStats(dashboardStatsFormat);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('StoreManagerDashboard: Error loading dashboard stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  // Reload dashboard statistics
  const reloadDashboardStats = () => {
    loadDashboardStats();
  };

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      setAnalyticsLoading(true);
      const analyticsData = await getDashboardAnalytics();
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('StoreManagerDashboard: Error loading analytics:', error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // Load low stock alerts
  const loadLowStockAlerts = async () => {
    try {
      // This would typically come from a service
      setLowStockAlerts([]);
    } catch (error) {
      console.error('StoreManagerDashboard: Error loading low stock alerts:', error);
    }
  };

  // Initialize dashboard data
  useEffect(() => {
    loadDashboardStats();
    loadAnalytics();
    loadLowStockAlerts();

    // Set up real-time subscriptions for Store Manager dashboard
    const unsubscribeOrderCreated = realTimeService.subscribe('order-created', (event) => {
      console.log('StoreManagerDashboard: Order created event received:', event);
      loadDashboardStats();
    });

    const unsubscribeOrderUpdated = realTimeService.subscribe('order-updated', (event) => {
      console.log('StoreManagerDashboard: Order updated event received:', event);
      loadDashboardStats();
    });

    const unsubscribeOrderStatusChanged = realTimeService.subscribe('order-status-changed', (event) => {
      console.log('StoreManagerDashboard: Order status changed event received:', event);
      loadDashboardStats();
    });

    const unsubscribeProductUpdated = realTimeService.subscribe('product-updated', (event) => {
      console.log('StoreManagerDashboard: Product updated event received:', event);
      loadDashboardStats();
    });

    const unsubscribeInventoryChanged = realTimeService.subscribe('inventory-changed', (event) => {
      console.log('StoreManagerDashboard: Inventory changed event received:', event);
      loadDashboardStats();
    });

    const unsubscribeStatisticsUpdated = realTimeService.subscribe('statistics-updated', (event) => {
      console.log('StoreManagerDashboard: Statistics updated event received:', event);
      loadDashboardStats();
    });

    return () => {
      // Clean up all subscriptions
      unsubscribeOrderCreated();
      unsubscribeOrderUpdated();
      unsubscribeOrderStatusChanged();
      unsubscribeProductUpdated();
      unsubscribeInventoryChanged();
      unsubscribeStatisticsUpdated();
    };
  }, []);

  // Handle user updates
  const handleUserUpdate = (updatedUser: any) => {
    console.log('StoreManagerDashboard: User updated:', updatedUser);
  };

  // Handle navigation
  const handleNavigate = (page: string, tab?: string) => {
    console.log('StoreManagerDashboard.handleNavigate called with:', { page, tab });
    console.log('StoreManagerDashboard.handleNavigate - Current page before:', currentPage);

    setCurrentPage(page);
    console.log('StoreManagerDashboard.handleNavigate - Setting page to:', page);

    // Update URL parameters
    const url = new URL(window.location.href);
    console.log('StoreManagerDashboard.handleNavigate - Current URL before:', url.toString());
    url.searchParams.set('page', page);

    if (page === 'dashboard') {
      const newTab = tab || 'overview';
      onTabChange(newTab);
      url.searchParams.set('tab', newTab);
      localStorage.setItem('store_manager_active_tab', newTab);
    } else {
      url.searchParams.delete('tab');
    }

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());
    console.log('StoreManagerDashboard.handleNavigate - Updated URL:', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('store_manager_current_page', page);

    // Call parent navigation if provided
    if (onNavigate) {
      onNavigate(page, tab);
    }
  };

  // Provide navigation function to header
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }
  }, [onNavigateFromHeader]);

  // Render page content without layout wrapper (for mobile)
  const renderPageContent = () => {
    switch (currentPage) {
      case 'product-management':
        return <ProductManagement />;
      case 'orders':
        return <OrderManagement />;
      case 'clients':
        return <ClientManagement currentUserId={user.id} userRole="manager" />;
      case 'branches':
        return <BranchManagement />;
      case 'category-management':
        return <CategoryManagement />;
      case 'promo-codes':
        return <PromoCodesPage />;
      case 'reviews':
        return <ReviewsManagementPage />;
      case 'notifications':
        return <NotificationsPage />;
      case 'invoices':
        return <InvoiceManagementPage />;
      default:
        return <div>Page not found</div>;
    }
  };

  // Render different pages based on currentPage
  const renderPage = () => {
    switch (currentPage) {
      case 'product-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Product Management"
            subtitle="Manage products, inventory, and pricing"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <ProductManagement />
          </PageLayout>
        );

      case 'orders':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Order Management"
            subtitle="Process and manage customer orders"
            breadcrumbs={[{ label: 'Operations', page: 'dashboard' }]}
          >
            <OrderManagement />
          </PageLayout>
        );

      case 'clients':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Client Management"
            subtitle="Manage client accounts and relationships"
            breadcrumbs={[{ label: 'Operations', page: 'dashboard' }]}
          >
            <ClientManagement currentUserId={user.id} userRole="manager" />
          </PageLayout>
        );

      case 'branches':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Branch Management"
            subtitle="Manage store branches and locations"
            breadcrumbs={[{ label: 'Operations', page: 'dashboard' }]}
          >
            <BranchManagement />
          </PageLayout>
        );

      case 'category-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Category Management"
            subtitle="Organize products into categories and subcategories"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <CategoryManagement />
          </PageLayout>
        );

      case 'notifications':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Notification Center"
            subtitle="Manage system notifications and alerts"
            breadcrumbs={[{ label: 'Communication', page: 'dashboard' }]}
          >
            <NotificationsPage />
          </PageLayout>
        );

      case 'invoices':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Invoice Management"
            subtitle="Generate and manage invoices"
            breadcrumbs={[{ label: 'Finance', page: 'dashboard' }]}
          >
            <InvoiceManagementPage />
          </PageLayout>
        );

      case 'profile':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Profile Management"
            subtitle="Manage your account settings"
          >
            <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
          </PageLayout>
        );

      case 'reviews':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Reviews & Comments"
            subtitle="Manage product reviews and user feedback"
            breadcrumbs={[{ label: 'Management', page: 'dashboard' }]}
          >
            <ReviewsManagementPage />
          </PageLayout>
        );

      case 'promo-codes':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Promo Codes Management"
            subtitle="Create and manage promotional discount codes"
            breadcrumbs={[{ label: 'Dashboard', page: 'dashboard' }]}
          >
            <PromoCodesPage />
          </PageLayout>
        );

      default:
        // Store Manager Dashboard - Mirrors Admin Dashboard (Restricted)
        return (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-6 sm:p-8 text-white">
              <h2 className="text-2xl sm:text-3xl font-bold mb-2">Welcome back, {user.fullName}!</h2>
              <p className="text-base sm:text-lg opacity-90">Store Manager Dashboard - Core Management</p>
            </div>

            {/* Analytics Overview Section - Same as Admin but excluding financial stats */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <TrendingUp className="h-5 w-5 text-teal-600 mr-2" />
                  Live Dashboard Statistics
                </h3>
                <div className="flex items-center space-x-4">
                  {lastUpdated && (
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Updated: {lastUpdated.toLocaleTimeString()}</span>
                    </div>
                  )}
                  <button
                    onClick={loadDashboardStats}
                    disabled={statsLoading}
                    className="flex items-center space-x-1 px-3 py-1 text-sm text-teal-600 hover:text-teal-700 hover:bg-teal-50 rounded-md transition-colors disabled:opacity-50"
                    title="Refresh statistics"
                  >
                    <RefreshCw className={`h-4 w-4 ${statsLoading ? 'animate-spin' : ''}`} />
                    <span>Refresh</span>
                  </button>
                </div>
              </div>

              {statsLoading ? (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[...Array(8)].map((_, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 animate-pulse">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-8 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Total Products */}
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-blue-600">Total Products</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-blue-900">
                          {statsLoading ? '...' : (dashboardStats?.totalProducts || 0)}
                        </p>
                        <p className="text-xs text-blue-600 mt-1">
                          {dashboardStats?.lowStockProducts || 0} low stock items
                        </p>
                      </div>
                      <Package className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>

                  {/* Total Users */}
                  <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-green-600">Total Users</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-green-900">
                          {statsLoading ? '...' : (dashboardStats?.totalUsers || 0)}
                        </p>
                        <p className="text-xs text-green-600 mt-1">
                          {dashboardStats?.activeUsers || 0} active users
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-green-600" />
                    </div>
                  </div>

                  {/* Total Orders */}
                  <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-purple-600">Total Orders</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-purple-900">
                          {statsLoading ? '...' : (dashboardStats?.totalOrders || 0)}
                        </p>
                        <p className="text-xs text-purple-600 mt-1">
                          {dashboardStats?.pendingOrders || 0} pending orders
                        </p>
                      </div>
                      <ShoppingCart className="h-8 w-8 text-purple-600" />
                    </div>
                  </div>

                  {/* EXCLUDED: Total Revenue - Admin Only */}

                  {/* EXCLUDED: Today's Sales - Admin Only */}

                  {/* EXCLUDED: This Month - Admin Only */}

                  {/* Total Customers */}
                  <div className="bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg p-4 border border-pink-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-pink-600">Total Customers</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-pink-900">
                          {statsLoading ? '...' : (dashboardStats?.totalCustomers || 0)}
                        </p>
                        <p className="text-xs text-pink-600 mt-1">Active clients & resellers</p>
                      </div>
                      <Users className="h-8 w-8 text-pink-600" />
                    </div>
                  </div>

                  {/* Total Branches */}
                  <div className="bg-gradient-to-r from-amber-50 to-amber-100 rounded-lg p-4 border border-amber-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-amber-600">Total Branches</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-amber-900">
                          {statsLoading ? '...' : (dashboardStats?.totalBranches || 0)}
                        </p>
                        <p className="text-xs text-amber-600 mt-1">
                          {dashboardStats?.activeBranches || 0} active locations
                        </p>
                      </div>
                      <MapPin className="h-8 w-8 text-amber-600" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Core Management Functions Grid - Same as Admin except restricted cards */}
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {/* EXCLUDED: User Management - Admin Only */}

              {/* Product Management */}
              <div
                onClick={() => handleNavigate('product-management')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <Package className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Product Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage products, inventory, and pricing</p>
              </div>

              {/* Category Management */}
              <div
                onClick={() => handleNavigate('category-management')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <FolderPlus className="h-6 w-6 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Category Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Organize products into categories and subcategories</p>
              </div>

              {/* Branch Management */}
              <div
                onClick={() => handleNavigate('branches')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <MapPin className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Branch Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage branch locations and operations</p>
              </div>

              {/* Order Management */}
              <div
                onClick={() => handleNavigate('orders')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <ShoppingCart className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Order Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Process and manage customer orders</p>
              </div>

              {/* EXCLUDED: Analytics & Reports - Admin Only */}

              {/* Reviews & Comments */}
              <div
                onClick={() => handleNavigate('reviews')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <MessageSquare className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Reviews & Comments</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage product reviews and user feedback</p>
              </div>

              {/* Profile Settings */}
              <div
                onClick={() => handleNavigate('profile')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-indigo-100 rounded-lg">
                    <User className="h-6 w-6 text-indigo-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Profile Settings</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage store manager profile and account settings</p>
              </div>

              {/* Promo Codes Management */}
              <div
                onClick={() => handleNavigate('promo-codes')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <Tag className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Promo Codes Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Create and manage promotional discount codes</p>
              </div>

              {/* EXCLUDED: System Settings - Admin Only */}
            </div>

            {/* Quick Actions Section - Same as Admin but excluding restricted actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Add New Product */}
                <button
                  onClick={() => handleNavigate('product-management')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Plus className="h-5 w-5" />
                  <span className="font-medium">Add New Product</span>
                </button>

                {/* EXCLUDED: Create User Account - Admin Only */}

                {/* EXCLUDED: Generate Report - Admin Only */}

                {/* Check Orders */}
                <button
                  onClick={() => handleNavigate('orders')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <ClipboardList className="h-5 w-5" />
                  <span className="font-medium">Check Orders</span>
                </button>

                {/* Manage Categories */}
                <button
                  onClick={() => handleNavigate('category-management')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FolderPlus className="h-5 w-5" />
                  <span className="font-medium">Manage Categories</span>
                </button>

                {/* Manage Reviews */}
                <button
                  onClick={() => handleNavigate('reviews')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-amber-500 to-amber-600 text-white rounded-lg hover:from-amber-600 hover:to-amber-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <MessageSquare className="h-5 w-5" />
                  <span className="font-medium">Manage Reviews</span>
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return renderPage();
};

export default StoreManagerDashboard;
