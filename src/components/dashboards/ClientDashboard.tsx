import { useState, useEffect } from 'react';
import { ShoppingCart, Package, Star, Filter, Search, Grid, List, Bell, User, Shield, Heart } from 'lucide-react';
import { useMobileUtils } from '../../hooks/use-mobile';
import ProductGrid from '../products/ProductGrid';
import Cart from '../cart/Cart';
import OrderHistory from '../orders/OrderHistory';
import ProfileManagement from '../profile/ProfileManagement';
import NotificationBell from '../ui/NotificationBell';
import AdvancedSearch, { SearchFilters as AdvancedSearchFilters } from '../search/AdvancedSearch';
import { SearchFilters } from '../products/ProductGrid';
import RecentlyViewed from '../search/RecentlyViewed';
import SecurityDashboard from '../security/SecurityDashboard';

import WishlistDashboard from '../wishlist/WishlistDashboard';
import { getActiveCategories } from '../../services/liveCategoryService';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';

interface ClientDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
  dashboardNavigateRef?: React.MutableRefObject<((page: string) => void) | null>;
}

const ClientDashboard = ({ user: initialUser, onNavigateFromHeader, dashboardNavigateRef }: ClientDashboardProps) => {
  const [user, setUser] = useState(initialUser);
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();

  // Initialize state from URL parameters and localStorage
  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const urlPage = urlParams.get('page');
    const storedTab = localStorage.getItem('client_active_tab');

    // If page is 'orders', set tab to 'orders'
    if (urlPage === 'orders') {
      console.log('Page is orders, setting tab to orders');
      return 'orders';
    }

    // If page is 'dashboard' and tab is 'products', set tab to 'products'
    if (urlPage === 'dashboard' && urlTab === 'products') {
      console.log('Page is dashboard with products tab, setting tab to products');
      return 'products';
    }

    // Always default to 'products' tab to ensure content is visible on first load
    const defaultTab = urlTab || storedTab || 'products';
    console.log('Initial tab selected:', defaultTab); // Debug log
    return defaultTab;
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [cart, setCart] = useState<any[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);



  // Ensure products tab is active by default and properly stored
  useEffect(() => {
    if (activeTab === 'products') {
      localStorage.setItem('client_active_tab', 'products');
      console.log('Products tab set as active and stored in localStorage');
    }
  }, [activeTab]);

  // Listen for URL parameter changes
  useEffect(() => {
    const handleUrlChange = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const urlPage = urlParams.get('page');
      const urlTab = urlParams.get('tab');

      if (urlPage === 'orders' && urlTab === 'orders') {
        console.log('URL changed to orders page with orders tab');
        setActiveTab('orders');
      } else if (urlPage === 'dashboard' && urlTab === 'products') {
        console.log('URL changed to dashboard page with products tab');
        setActiveTab('products');
      } else if (urlTab && urlTab !== activeTab) {
        console.log('URL tab changed to:', urlTab);
        setActiveTab(urlTab);
      }
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', handleUrlChange);

    return () => {
      window.removeEventListener('popstate', handleUrlChange);
    };
  }, [activeTab]);

  // Search and filter states
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchTerm: '',
    category: '',
    priceRange: [0, 1000] as [number, number],
    minRating: 0,
    inStock: false,
    isNew: false,
    brand: '',
    sortBy: 'relevance' as const,
    availability: 'all',
    discount: false,
    freeShipping: false,
    page: 1,
    itemsPerPage: 20
  });

  // Live categories from database
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  const brands = ['Pilot', 'BIC', 'Staedtler', 'Faber-Castell', 'Parker', 'Sharpie', 'Post-it', 'Moleskine'];

  // Load categories on mount and set up real-time subscriptions
  useEffect(() => {
    loadCategories();

    // Subscribe to real-time updates
    const categorySubscription = liveDataService.subscribeToCategories((payload) => {
      console.log('Real-time category update in ClientDashboard:', payload);
      loadCategories();
    });

    const productSubscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Real-time product update in ClientDashboard:', payload);
      // Products will be handled by individual components
    });

    const orderSubscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Real-time order update in ClientDashboard:', payload);
      // Orders will be handled by OrderHistory component
    });

    // Subscribe to user profile updates for cross-component synchronization
    const userUpdateSubscription = realTimeService.subscribe('user-data-updated', (payload) => {
      console.log('ClientDashboard: User data update received:', payload);
      if (payload.userId === user.id) {
        setUser(payload.updatedData);
      }
    });

    const profileUpdateSubscription = realTimeService.subscribe('profile-updated', (payload) => {
      console.log('ClientDashboard: Profile update received:', payload);
      if (payload.userId === user.id) {
        // Refresh user data if needed
        console.log('ClientDashboard: Profile updated for current user');
      }
    });

    const securityEventSubscription = realTimeService.subscribe('security-event', (payload) => {
      console.log('ClientDashboard: Security event received:', payload);
      // Handle security events if needed
    });

    return () => {
      categorySubscription.unsubscribe();
      productSubscription.unsubscribe();
      orderSubscription.unsubscribe();
      userUpdateSubscription();
      profileUpdateSubscription();
      securityEventSubscription();
    };
  }, [user.id]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const categoriesData = await getActiveCategories();
      const categoryNames = categoriesData.map(cat => cat.name);
      setCategories(categoryNames);
    } catch (error) {
      console.error('Error loading categories:', error);
      // Fallback to hardcoded categories if live data fails
      setCategories([
        'Writing Instruments',
        'Paper & Notebooks',
        'Office & Desk Accessories',
        'Technology',
        'Storage & Organization',
        'Art & Craft Supplies',
        'Printing & Copying',
        'Furniture',
        'School & Office Supplies',
        'Filing & Organization',
        'Greeting Cards & Gift Supplies',
        'Back-to-School Essentials',
        'Eco-Friendly Stationery',
        'Specialty & Luxury Stationery'
      ]);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (product: any) => {
    try {
      // Update local state immediately for better UX
      setCart(prev => {
        const existing = prev.find(item => item.id === product.id);
        if (existing) {
          return prev.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          );
        }
        return [...prev, { ...product, quantity: 1 }];
      });

      // Cart persistence is handled by cartService using localStorage
      // for better performance and offline capability
    } catch (error) {
      console.error('Error adding to cart:', error);
      // Could show a toast notification here
    }
  };

  const updateCartQuantity = async (productId: number, quantity: number) => {
    try {
      // Update local state immediately
      if (quantity === 0) {
        setCart(prev => prev.filter(item => item.id !== productId));
      } else {
        setCart(prev => prev.map(item =>
          item.id === productId ? { ...item, quantity } : item
        ));
      }

      // Cart persistence is handled by cartService using localStorage
    } catch (error) {
      console.error('Error updating cart quantity:', error);
    }
  };

  const removeFromCart = async (productId: number) => {
    try {
      // Update local state immediately
      setCart(prev => prev.filter(item => item.id !== productId));

      // Cart persistence is handled by cartService using localStorage
    } catch (error) {
      console.error('Error removing from cart:', error);
    }
  };

  const clearCart = () => {
    setCart([]);
  };

  const cartTotal = cart.reduce((sum, item) => {
    const price = user.userType === 'reseller' ? item.resellerPrice : item.price;
    return sum + (price * item.quantity);
  }, 0);

  const handleUserUpdate = (updatedUser: any) => {
    console.log('ClientDashboard: User updated:', updatedUser);
    setUser(updatedUser);

    // Broadcast user update events for system-wide synchronization
    realTimeService.emit('user-data-updated', {
      userId: updatedUser.id,
      updatedData: updatedUser,
      timestamp: new Date().toISOString()
    });

    realTimeService.emit('client-profile-updated', {
      userId: updatedUser.id,
      fullName: updatedUser.full_name || updatedUser.fullName,
      email: updatedUser.email,
      userType: updatedUser.userType,
      timestamp: new Date().toISOString()
    });

    console.log('ClientDashboard: Broadcasting user update across system');
  };

  const handleNavigate = (page: string) => {
    console.log('ClientDashboard: handleNavigate called with:', page);

    // Map external navigation to internal tabs
    let targetTab = page;
    if (page === 'dashboard') {
      targetTab = 'products'; // Map "Shop" (dashboard) to products tab
      console.log('ClientDashboard: Mapping dashboard to products tab');
    }

    console.log('ClientDashboard: Setting active tab to:', targetTab);
    setActiveTab(targetTab);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('tab', targetTab);

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('client_active_tab', targetTab);
  };

  // Convert ProductGrid SearchFilters to AdvancedSearch SearchFilters
  const convertToAdvancedSearchFilters = (filters: SearchFilters): AdvancedSearchFilters => {
    return {
      searchTerm: filters.searchTerm,
      category: filters.category,
      priceRange: filters.priceRange,
      minRating: filters.minRating,
      inStock: filters.inStock,
      isNew: filters.isNew,
      brand: filters.brand,
      sortBy: filters.sortBy === 'name' ? 'relevance' : filters.sortBy,
      availability: filters.availability as 'all' | 'in-stock' | 'low-stock' | 'out-of-stock',
      discount: filters.discount,
      freeShipping: filters.freeShipping
    };
  };

  // Convert AdvancedSearch SearchFilters to ProductGrid SearchFilters
  const convertFromAdvancedSearchFilters = (filters: AdvancedSearchFilters): SearchFilters => {
    return {
      ...searchFilters, // Keep existing page and itemsPerPage
      searchTerm: filters.searchTerm,
      category: filters.category,
      priceRange: filters.priceRange,
      minRating: filters.minRating,
      inStock: filters.inStock,
      isNew: filters.isNew,
      brand: filters.brand,
      sortBy: filters.sortBy,
      availability: filters.availability,
      discount: filters.discount,
      freeShipping: filters.freeShipping
    };
  };

  // Provide navigation function to header and parent dashboard
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }

    // Register navigation function with parent Dashboard component
    if (dashboardNavigateRef) {
      dashboardNavigateRef.current = handleNavigate;
    }
  }, [onNavigateFromHeader, dashboardNavigateRef]);

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-white">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2">
              Welcome back, {user.fullName}!
            </h2>
            <p className="text-lg opacity-90">
              {user.userType === 'reseller' ? 'Reseller' : 'Client'} Account
              {user.userType === 'reseller' && (
                <span className="ml-2 bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  Special Pricing Available
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Notification Bell */}
            <NotificationBell userId={user.id} />
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Enhanced Mobile Responsive */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className={`
          ${isMobile ? 'overflow-x-auto scrollbar-hide' : 'flex'}
          ${isMobile ? 'p-2' : 'p-1'}
        `}>
          <div className={`
            flex
            ${isMobile ? 'space-x-2 min-w-max' : 'space-x-1 w-full'}
          `}>
            {[
              { id: 'products', label: 'Products', icon: Package, shortLabel: 'Products' },
              { id: 'wishlist', label: 'Wishlist', icon: Heart, shortLabel: 'Wishlist' },
              { id: 'orders', label: 'My Orders', icon: ShoppingCart, shortLabel: 'Orders' },
              { id: 'security', label: 'Security', icon: Shield, shortLabel: 'Security' },
              { id: 'profile', label: 'Profile Settings', icon: User, shortLabel: 'Profile' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => {
                  console.log('Tab clicked:', tab.id, tab.label);
                  handleNavigate(tab.id);
                }}
                className={`
                  flex items-center justify-center rounded-md transition-all duration-200 font-medium
                  ${isMobile ? 'flex-shrink-0' : 'flex-1'}
                  ${shouldUseTouchUI ? 'min-h-[44px] min-w-[44px]' : ''}
                  ${isMobile ? 'px-4 py-3' : 'px-4 py-3'}
                  ${isSmallMobile ? 'space-x-1' : 'space-x-2'}
                  ${activeTab === tab.id
                    ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100'
                  }
                `}
                aria-label={tab.label}
                role="tab"
                aria-selected={activeTab === tab.id}
              >
                <tab.icon className={`
                  ${isSmallMobile ? 'h-4 w-4' : 'h-5 w-5'}
                  flex-shrink-0
                `} />
                <span className={`
                  ${isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-base'}
                  ${isMobile && tab.label.length > 8 ? 'hidden sm:inline' : ''}
                  whitespace-nowrap
                `}>
                  {isMobile && isSmallMobile ? tab.shortLabel : tab.label}
                </span>
                {/* Mobile: Show short label on very small screens */}
                {isMobile && tab.label.length > 8 && (
                  <span className="sm:hidden text-xs whitespace-nowrap">
                    {tab.shortLabel}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Mobile scroll indicator */}
        {isMobile && (
          <div className="flex justify-center py-1">
            <div className="flex space-x-1">
              {[...Array(5)].map((_, index) => (
                <div
                  key={index}
                  className={`w-1.5 h-1.5 rounded-full transition-colors ${
                    index === ['products', 'wishlist', 'orders', 'security', 'profile'].indexOf(activeTab)
                      ? 'bg-teal-600'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        )}
      </div>



      {/* Products Tab */}
      {activeTab === 'products' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Quick Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Quick search products..."
                  value={searchFilters.searchTerm}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              {/* Quick Category Filter */}
              <div className="lg:w-64">
                <select
                  value={searchFilters.category}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Advanced Search Button */}
              <button
                onClick={() => setShowAdvancedSearch(true)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
              >
                <Filter className="h-5 w-5" />
                <span>Advanced</span>
              </button>

              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 ${viewMode === 'grid' ? 'bg-teal-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-3 ${viewMode === 'list' ? 'bg-teal-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>

              {/* Cart Button */}
              <button
                onClick={() => setShowCart(true)}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 font-semibold flex items-center space-x-2"
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Cart ({cart.length})</span>
                {cartTotal > 0 && (
                  <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-sm">
                    {cartTotal.toFixed(2)} Dh
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Recently Viewed */}
          <RecentlyViewed
            userType={user.userType}
            onAddToCart={addToCart}
          />

          {/* Products Grid/List */}
          <ProductGrid
            viewMode={viewMode}
            searchFilters={searchFilters}
            userType={user.userType}
            customerId={user.id}
            customerName={user.fullName}
            onAddToCart={addToCart}
            onAddToWishlist={(product) => console.log('Add to wishlist:', product)}
          />
        </div>
      )}

      {/* Wishlist Tab */}
      {activeTab === 'wishlist' && (
        <WishlistDashboard
          customerId={user.id}
          customerName={user.fullName}
          userType={user.userType as 'client' | 'reseller'}
          onAddToCart={addToCart}
          onNavigateToProducts={() => handleNavigate('products')}
        />
      )}

      {/* Orders Tab */}
      {activeTab === 'orders' && (
        <OrderHistory customerId={user.id} />
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <SecurityDashboard userRole={user.userType} />
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
      )}



      {/* Modals */}
      {showCart && (
        <Cart
          isOpen={showCart}
          items={cart}
          userType={user.userType}
          user={{
            id: user.id,
            fullName: user.fullName,
            email: user.email
          }}
          onClose={() => setShowCart(false)}
          onUpdateQuantity={updateCartQuantity}
          onRemoveItem={removeFromCart}
          onClearCart={clearCart}
        />
      )}

      {showAdvancedSearch && (
        <AdvancedSearch
          filters={convertToAdvancedSearchFilters(searchFilters)}
          onFiltersChange={(filters) => setSearchFilters(convertFromAdvancedSearchFilters(filters))}
          categories={categories.map(cat => cat.name || cat.title || cat.id)}
          brands={brands}
          priceRange={[0, 1000]}
          isOpen={showAdvancedSearch}
          onClose={() => setShowAdvancedSearch(false)}
        />
      )}


    </div>
  );
};

export default ClientDashboard;
