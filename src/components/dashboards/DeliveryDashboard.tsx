
import { useState, useEffect } from 'react';
import { Truck, MapPin, Clock, CheckCircle, Package, Phone, Navigation, User, Key, Loader, AlertCircle } from 'lucide-react';
import { liveDashboardService, DeliveryStats } from '../../services/liveDashboardService';
import { useDeliveryOrders, useDeliveryHistory, useDeliveryStats, useDeliveryActions } from '../../hooks/useDeliveryData';
import { robustDeliveryService } from '../../services/deliveryServiceRobust';
import ProfileManagement from '../profile/ProfileManagement';
import PasswordManagement from '../security/PasswordManagement';

interface DeliveryDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
}

const DeliveryDashboard = ({ user: initialUser, onNavigateFromHeader }: DeliveryDashboardProps) => {
  const [user, setUser] = useState(initialUser);

  // Debug user information
  useEffect(() => {
    console.log('🔍 DeliveryDashboard: Component initialized with user:', {
      id: user?.id,
      fullName: user?.fullName,
      email: user?.email,
      role: user?.role
    });

    if (!user?.id) {
      console.error('❌ DeliveryDashboard: No user ID provided!');
    }
  }, [user]);

  // Initialize state from URL parameters and localStorage
  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const storedTab = localStorage.getItem('delivery_active_tab');
    return urlTab || storedTab || 'assigned';
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());

  // Use real delivery data hooks
  const { orders: assignedOrders, loading: ordersLoading, error: ordersError, updateOrderStatus } = useDeliveryOrders(user.id);
  const { history: deliveryHistory, loading: historyLoading, error: historyError } = useDeliveryHistory(user.id);
  const { stats: deliveryStats, loading: statsLoading, error: statsError } = useDeliveryStats(user.id);
  const { makePhoneCall, navigateToAddress } = useDeliveryActions();

  // Use real stats or fallback
  const displayStats = deliveryStats || {
    assignedOrders: 0,
    completedToday: 0,
    inTransit: 0,
    totalDistance: 0
  };



  // State for loading and error handling
  const [updatingOrderId, setUpdatingOrderId] = useState<string | null>(null);

  const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
    setUpdatingOrderId(orderId);
    try {
      const success = await updateOrderStatus(orderId, newStatus);
      if (!success) {
        alert('Failed to update order status. Please try again.');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Failed to update order status. Please try again.');
    } finally {
      setUpdatingOrderId(null);
    }
  };

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string) => {
    setActiveTab(page);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('tab', page);

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('delivery_active_tab', page);
  };

  // Provide navigation function to header
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }
  }, [onNavigateFromHeader]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return 'bg-blue-100 text-blue-800';
      case 'picked': return 'bg-yellow-100 text-yellow-800';
      case 'out_for_delivery': return 'bg-orange-100 text-orange-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'assigned': return 'Assigned';
      case 'picked': return 'Picked';
      case 'out_for_delivery': return 'Out for Delivery';
      case 'delivered': return 'Delivered';
      default: return status;
    }
  };

  const handleOptimizeRoute = (routeId: string) => {
    // TODO: Implement real route optimization
    alert('Route optimization feature coming soon!');
  };

  const handleStartRoute = (routeId: string) => {
    // TODO: Implement route starting functionality
    alert('Route starting feature coming soon!');
  };

  const handleViewMap = (address: string) => {
    navigateToAddress(address);
  };

  const handlePhoneCall = (phoneNumber: string) => {
    makePhoneCall(phoneNumber);
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-white">
        <h2 className="text-3xl font-bold mb-2">Welcome back, {user.fullName}!</h2>
        <p className="text-lg opacity-90">Delivery Personnel Dashboard</p>
      </div>

      {/* Quick Stats */}
      {activeTab !== 'profile' && activeTab !== 'password' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Assigned Orders</p>
                <p className="text-2xl font-bold text-blue-600">{statsLoading ? '...' : displayStats.assignedOrders}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed Today</p>
                <p className="text-2xl font-bold text-green-600">{statsLoading ? '...' : displayStats.completedToday}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Transit</p>
                <p className="text-2xl font-bold text-orange-600">{statsLoading ? '...' : displayStats.inTransit}</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <Truck className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Distance Today</p>
                <p className="text-2xl font-bold text-purple-600">{statsLoading ? '...' : displayStats.totalDistance} km</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <Navigation className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs - Mobile Responsive */}
      <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {/* Mobile: Scrollable horizontal tabs */}
        <div className="flex md:hidden overflow-x-auto space-x-1 pb-1">
          {[
            { id: 'assigned', label: 'Orders', icon: Package },
            { id: 'history', label: 'History', icon: Clock },
            { id: 'route', label: 'Route', icon: MapPin },
            { id: 'password', label: 'Password', icon: Key },
            { id: 'profile', label: 'Profile', icon: User }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => handleNavigate(tab.id)}
              className={`flex-shrink-0 flex flex-col items-center justify-center space-y-1 py-3 px-4 rounded-md transition-all duration-200 min-w-[80px] ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Desktop: Full width tabs */}
        <div className="hidden md:flex space-x-1">
          {[
            { id: 'assigned', label: 'Assigned Orders', icon: Package },
            { id: 'history', label: 'Delivery History', icon: Clock },
            { id: 'route', label: 'Route Planning', icon: MapPin },
            { id: 'password', label: 'Password', icon: Key },
            { id: 'profile', label: 'Profile Settings', icon: User }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => handleNavigate(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Password Tab */}
      {activeTab === 'password' && (
        <PasswordManagement />
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
      )}

      {/* Assigned Orders Tab */}
      {activeTab === 'assigned' && (
        <div className="space-y-4">
          {ordersLoading ? (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <div className="flex items-center justify-center">
                <Loader className="h-8 w-8 animate-spin text-teal-600 mr-3" />
                <span className="text-gray-600">Loading assigned orders...</span>
              </div>
            </div>
          ) : ordersError ? (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <div className="text-center">
                <AlertCircle className="h-16 w-16 text-red-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Orders</h3>
                <p className="text-red-600 mb-4">{ordersError}</p>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-left">
                  <h4 className="font-semibold text-red-800 mb-2">Debugging Information:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• User ID: {user?.id || 'Not provided'}</li>
                    <li>• Check browser console (F12) for detailed error messages</li>
                    <li>• Verify database migration was completed successfully</li>
                    <li>• Ensure user has delivery person role</li>
                  </ul>
                </div>
              </div>
            </div>
          ) : assignedOrders.length === 0 ? (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <div className="text-center">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Assigned Orders</h3>
                <p className="text-gray-500 mb-4">You don't have any orders assigned for delivery</p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left max-w-md mx-auto">
                  <h4 className="font-semibold text-blue-800 mb-2">Troubleshooting:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Check browser console (F12) for debug messages</li>
                    <li>• Verify orders are assigned to your user ID: {user?.id}</li>
                    <li>• Ask admin to assign orders via Order Management</li>
                    <li>• Refresh page to check for new assignments</li>
                  </ul>
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-3 w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Refresh Page
                  </button>
                </div>
              </div>
            </div>
          ) : (
            assignedOrders.map(order => (
              <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6">
                {/* Mobile-first responsive header */}
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 space-y-3 sm:space-y-0">
                  <div className="flex-1">
                    {/* Order number and status - mobile stacked */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3 mb-2 space-y-2 sm:space-y-0">
                      <h3 className="text-lg font-semibold text-gray-900">{order.order_number}</h3>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                          {getStatusLabel(order.status)}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          order.priority === 'high' ? 'bg-red-100 text-red-800' :
                          order.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {order.priority} priority
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-600 font-medium mb-2">{order.customer_name}</p>
                    {/* Dates - mobile stacked */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 space-y-1 sm:space-y-0">
                      <span>Order: {new Date(order.created_at).toLocaleDateString()}</span>
                      <span>Delivery: {new Date(order.delivery_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                  {/* Price - prominent on mobile */}
                  <div className="flex justify-between sm:block">
                    <span className="text-sm text-gray-500 sm:hidden">Total:</span>
                    <p className="text-xl font-bold text-teal-600">{order.total.toFixed(2)} Dh</p>
                  </div>
                </div>

                {/* Contact info - mobile optimized */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                  <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                    <Phone className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <span className="text-gray-600 truncate">{order.customer_phone}</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                    <Package className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <span className="text-gray-600">{order.items_count} items</span>
                  </div>
                </div>

                {/* Address - mobile friendly */}
                <div className="flex items-start space-x-3 mb-4 p-3 bg-blue-50 rounded-lg">
                  <MapPin className="h-5 w-5 text-blue-500 mt-1 flex-shrink-0" />
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {typeof order.delivery_address === 'string'
                      ? order.delivery_address
                      : `${order.delivery_address?.street || ''}, ${order.delivery_address?.city || ''}`
                    }
                  </p>
                </div>

                {/* Action buttons - mobile optimized */}
                <div className="space-y-3">
                  {/* Primary status update button - full width on mobile */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {order.status === 'assigned' && (
                      <button
                        onClick={() => handleUpdateOrderStatus(order.id, 'picked')}
                        disabled={updatingOrderId === order.id}
                        className="w-full sm:flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 active:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium touch-manipulation"
                      >
                        {updatingOrderId === order.id ? (
                          <Loader className="h-5 w-5 animate-spin" />
                        ) : null}
                        <span>Mark as Picked</span>
                      </button>
                    )}
                    {order.status === 'picked' && (
                      <button
                        onClick={() => handleUpdateOrderStatus(order.id, 'out_for_delivery')}
                        disabled={updatingOrderId === order.id}
                        className="w-full sm:flex-1 bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 active:bg-orange-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium touch-manipulation"
                      >
                        {updatingOrderId === order.id ? (
                          <Loader className="h-5 w-5 animate-spin" />
                        ) : null}
                        <span>Out for Delivery</span>
                      </button>
                    )}
                    {order.status === 'out_for_delivery' && (
                      <button
                        onClick={() => handleUpdateOrderStatus(order.id, 'delivered')}
                        disabled={updatingOrderId === order.id}
                        className="w-full sm:flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 active:bg-green-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium touch-manipulation"
                      >
                        {updatingOrderId === order.id ? (
                          <Loader className="h-5 w-5 animate-spin" />
                        ) : null}
                        <span>Mark as Delivered</span>
                      </button>
                    )}
                  </div>

                  {/* Secondary action buttons - mobile grid */}
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => handleViewMap(
                        typeof order.delivery_address === 'string'
                          ? order.delivery_address
                          : `${order.delivery_address?.street || ''}, ${order.delivery_address?.city || ''}`
                      )}
                      className="flex items-center justify-center space-x-2 px-4 py-3 border-2 border-teal-300 text-teal-700 rounded-lg hover:bg-teal-50 active:bg-teal-100 transition-colors font-medium touch-manipulation"
                    >
                      <MapPin className="h-5 w-5" />
                      <span>Navigate</span>
                    </button>
                    <button
                      onClick={() => handlePhoneCall(order.customer_phone)}
                      className="flex items-center justify-center space-x-2 px-4 py-3 border-2 border-green-300 text-green-700 rounded-lg hover:bg-green-50 active:bg-green-100 transition-colors font-medium touch-manipulation"
                    >
                      <Phone className="h-5 w-5" />
                      <span>Call</span>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Delivery History</h3>

          {historyLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader className="h-8 w-8 animate-spin text-teal-600 mr-3" />
              <span className="text-gray-600">Loading delivery history...</span>
            </div>
          ) : historyError ? (
            <div className="flex items-center justify-center py-12 text-red-600">
              <AlertCircle className="h-8 w-8 mr-3" />
              <span>Error loading history: {historyError}</span>
            </div>
          ) : deliveryHistory.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">No delivery history</p>
              <p className="text-gray-400">Completed deliveries will appear here</p>
            </div>
          ) : (
            <div className="space-y-4">
              {deliveryHistory.map(order => (
                <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="flex items-center space-x-3 mb-1">
                        <h4 className="font-semibold text-gray-900">{order.order_number}</h4>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                          Delivered
                        </span>
                      </div>
                      <p className="text-gray-600">{order.customer_name}</p>
                      <p className="text-sm text-gray-500">
                        Delivered: {new Date(order.delivered_at || order.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <p className="font-bold text-gray-900">{order.total.toFixed(2)} Dh</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Package className="h-4 w-4 text-gray-400" />
                      <span>{order.items_count} items</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>
                        {typeof order.delivery_address === 'string'
                          ? order.delivery_address
                          : `${order.delivery_address?.city || 'Unknown location'}`
                        }
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Route Planning Tab */}
      {activeTab === 'route' && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Route Planning</h3>

            <div className="text-center py-12">
              <Navigation className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">Route Planning Coming Soon</p>
              <p className="text-gray-400 mb-6">Advanced route optimization and planning features will be available soon</p>

              <div className="bg-teal-50 border border-teal-200 rounded-lg p-4 max-w-md mx-auto">
                <h4 className="font-semibold text-teal-900 mb-2">Planned Features:</h4>
                <ul className="text-sm text-teal-800 space-y-1 text-left">
                  <li>• Automatic route optimization</li>
                  <li>• GPS-based navigation integration</li>
                  <li>• Real-time traffic updates</li>
                  <li>• Multi-stop route planning</li>
                  <li>• Delivery time estimation</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Current Navigation Options</h4>
              <p className="text-sm text-blue-800">
                Use the "Navigate" button on individual orders to open your preferred map application for turn-by-turn directions.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryDashboard;
