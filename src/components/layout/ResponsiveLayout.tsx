/**
 * Unified Responsive Layout System for YalaOffice
 * Provides a single layout that adapts to all screen sizes without conditional rendering
 */

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useMobileUtils } from '../../hooks/use-mobile';
import { useResponsiveSpacing } from '../../hooks/useResponsive';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  navigation: React.ReactNode;
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  className?: string;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  navigation,
  header,
  sidebar,
  className
}) => {
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  const spacing = useResponsiveSpacing();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && sidebarOpen) {
        const target = event.target as Element;
        if (!target.closest('.sidebar-container') && !target.closest('.sidebar-toggle')) {
          setSidebarOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, sidebarOpen]);

  // Close sidebar on route change (mobile)
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [children, isMobile]);

  return (
    <div className={cn('min-h-screen bg-gray-50 flex flex-col', className)}>
      {/* Header - Always visible on mobile, adaptive on desktop */}
      {header && (
        <header className={cn(
          'bg-white border-b border-gray-200 z-40',
          isMobile ? 'fixed top-0 left-0 right-0' : 'relative',
          shouldUseTouchUI ? 'h-16' : 'h-14'
        )}>
          {header}
        </header>
      )}

      <div className="flex flex-1 relative">
        {/* Sidebar/Navigation - Responsive behavior */}
        {sidebar && (
          <>
            {/* Mobile overlay */}
            {isMobile && sidebarOpen && (
              <div 
                className="fixed inset-0 bg-black bg-opacity-50 z-30"
                onClick={() => setSidebarOpen(false)}
              />
            )}
            
            {/* Sidebar container */}
            <aside className={cn(
              'sidebar-container bg-white border-r border-gray-200 z-40 transition-transform duration-300 ease-in-out',
              // Mobile: Fixed overlay sidebar
              isMobile ? [
                'fixed top-0 left-0 h-full',
                isSmallMobile ? 'w-72' : 'w-80',
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
              ] : [
                // Desktop: Static sidebar
                'relative flex-shrink-0 w-64'
              ]
            )}>
              <div className={cn(
                'h-full overflow-y-auto',
                isMobile && header ? 'pt-16' : '',
                shouldUseTouchUI ? 'pb-safe' : ''
              )}>
                {sidebar}
              </div>
            </aside>
          </>
        )}

        {/* Main content area */}
        <main className={cn(
          'flex-1 flex flex-col min-w-0', // min-w-0 prevents flex overflow
          shouldUseTouchUI ? 'pb-safe' : ''
        )}>
          {/* Navigation - Let the navigation component handle its own positioning */}
          <div className="relative">
            {navigation}
          </div>

          {/* Content area */}
          <div className={cn(
            'flex-1 overflow-auto',
            // Add bottom padding for mobile navigation - increased to account for safe areas
            isMobile ? (shouldUseTouchUI ? 'pb-24' : 'pb-20') : '',
            // Responsive padding
            isSmallMobile ? 'p-3' : isMobile ? 'p-4' : 'p-6'
          )}>
            {children}
          </div>
        </main>
      </div>

      {/* Sidebar toggle for mobile - can be positioned anywhere */}
      {isMobile && sidebar && (
        <button
          className="sidebar-toggle fixed top-4 left-4 z-50 bg-white rounded-lg shadow-lg p-2 border border-gray-200"
          onClick={() => setSidebarOpen(!sidebarOpen)}
          aria-label={sidebarOpen ? 'Close sidebar' : 'Open sidebar'}
        >
          <svg
            className="w-6 h-6 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {sidebarOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      )}
    </div>
  );
};

// Responsive container component for content areas
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  maxWidth = 'full',
  padding = true
}) => {
  const { isSmallMobile, isMobile } = useMobileUtils();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  };

  const paddingClasses = padding ? (
    isSmallMobile ? 'p-3' : isMobile ? 'p-4' : 'p-6'
  ) : '';

  return (
    <div className={cn(
      'w-full mx-auto',
      maxWidthClasses[maxWidth],
      paddingClasses,
      className
    )}>
      {children}
    </div>
  );
};

// Responsive grid component
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}) => {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8'
  };

  const gridClasses = cn(
    'grid',
    `grid-cols-${cols.mobile || 1}`,
    `md:grid-cols-${cols.tablet || 2}`,
    `lg:grid-cols-${cols.desktop || 3}`,
    gapClasses[gap]
  );

  return (
    <div className={cn(gridClasses, className)}>
      {children}
    </div>
  );
};

// Responsive card component
interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className,
  padding = 'md',
  hover = false
}) => {
  const { isSmallMobile } = useMobileUtils();

  const paddingClasses = {
    sm: isSmallMobile ? 'p-3' : 'p-4',
    md: isSmallMobile ? 'p-4' : 'p-6',
    lg: isSmallMobile ? 'p-6' : 'p-8'
  };

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 shadow-sm',
      paddingClasses[padding],
      hover && 'hover:shadow-md transition-shadow duration-200',
      className
    )}>
      {children}
    </div>
  );
};

export default ResponsiveLayout;
