/**
 * Performance Monitor Component
 * Displays real-time performance metrics and cache statistics
 */

import React, { useState, useEffect } from 'react';
import { Activity, Database, Zap, Clock, TrendingUp, RefreshCw } from 'lucide-react';
import { useCacheStats } from '../../hooks/useOptimizedData';
import { optimizedRealTimeService } from '../../services/optimizedRealTimeService';

interface PerformanceMetrics {
  cacheHitRate: number;
  cacheSize: number;
  activeBatches: number;
  activeTimers: number;
  avgResponseTime: number;
  totalRequests: number;
}

const PerformanceMonitor: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    cacheHitRate: 0,
    cacheSize: 0,
    activeBatches: 0,
    activeTimers: 0,
    avgResponseTime: 0,
    totalRequests: 0
  });

  const cacheStats = useCacheStats();

  // Update metrics every 5 seconds
  useEffect(() => {
    const updateMetrics = () => {
      const realTimeMetrics = optimizedRealTimeService.getMetrics();
      
      setMetrics({
        cacheHitRate: cacheStats.hitRate,
        cacheSize: cacheStats.size,
        activeBatches: realTimeMetrics.activeBatches,
        activeTimers: realTimeMetrics.activeTimers,
        avgResponseTime: Math.random() * 200 + 50, // Mock data - would be real in production
        totalRequests: cacheStats.hits + cacheStats.misses
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, [cacheStats]);

  // Only show in development or for admin users
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-teal-600 text-white p-3 rounded-full shadow-lg hover:bg-teal-700 transition-colors"
        title="Performance Monitor"
      >
        <Activity className="h-5 w-5" />
      </button>

      {/* Performance Panel */}
      {isVisible && (
        <div className="fixed bottom-20 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Activity className="h-5 w-5 mr-2 text-teal-600" />
              Performance Monitor
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <div className="space-y-4">
            {/* Cache Performance */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Database className="h-4 w-4 mr-2 text-blue-600" />
                <span className="font-medium text-gray-900">Cache Performance</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-600">Hit Rate:</span>
                  <span className={`ml-1 font-medium ${
                    metrics.cacheHitRate > 80 ? 'text-green-600' :
                    metrics.cacheHitRate > 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {metrics.cacheHitRate.toFixed(1)}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Size:</span>
                  <span className="ml-1 font-medium text-gray-900">{metrics.cacheSize}</span>
                </div>
                <div>
                  <span className="text-gray-600">Hits:</span>
                  <span className="ml-1 font-medium text-green-600">{cacheStats.hits}</span>
                </div>
                <div>
                  <span className="text-gray-600">Misses:</span>
                  <span className="ml-1 font-medium text-red-600">{cacheStats.misses}</span>
                </div>
              </div>
            </div>

            {/* Real-time Performance */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Zap className="h-4 w-4 mr-2 text-yellow-600" />
                <span className="font-medium text-gray-900">Real-time Updates</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-600">Batches:</span>
                  <span className="ml-1 font-medium text-gray-900">{metrics.activeBatches}</span>
                </div>
                <div>
                  <span className="text-gray-600">Timers:</span>
                  <span className="ml-1 font-medium text-gray-900">{metrics.activeTimers}</span>
                </div>
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Clock className="h-4 w-4 mr-2 text-purple-600" />
                <span className="font-medium text-gray-900">Response Time</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">Average:</span>
                <span className={`ml-1 font-medium ${
                  metrics.avgResponseTime < 100 ? 'text-green-600' :
                  metrics.avgResponseTime < 300 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {metrics.avgResponseTime.toFixed(0)}ms
                </span>
              </div>
            </div>

            {/* Request Stats */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <TrendingUp className="h-4 w-4 mr-2 text-green-600" />
                <span className="font-medium text-gray-900">Request Stats</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">Total:</span>
                <span className="ml-1 font-medium text-gray-900">{metrics.totalRequests}</span>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm">
                <div className="font-medium text-blue-900 mb-1">Performance Tips:</div>
                <ul className="text-blue-800 space-y-1">
                  {metrics.cacheHitRate < 70 && (
                    <li>• Low cache hit rate - consider increasing TTL</li>
                  )}
                  {metrics.avgResponseTime > 300 && (
                    <li>• High response time - check network/database</li>
                  )}
                  {metrics.activeBatches > 10 && (
                    <li>• Many active batches - consider debouncing</li>
                  )}
                  {metrics.cacheHitRate > 90 && metrics.avgResponseTime < 100 && (
                    <li className="text-green-700">✓ Excellent performance!</li>
                  )}
                </ul>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={() => window.location.reload()}
                className="flex-1 bg-teal-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-teal-700 transition-colors flex items-center justify-center"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PerformanceMonitor;
