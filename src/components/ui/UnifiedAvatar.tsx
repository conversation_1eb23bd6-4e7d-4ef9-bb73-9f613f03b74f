/**
 * Unified Avatar Component for YalaOffice
 * Displays user profile pictures with real-time synchronization and fallback support
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { useProfilePicture } from '../../hooks/useProfilePicture';
import { User } from 'lucide-react';

interface UnifiedAvatarProps {
  userId: string;
  fullName: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showOnlineStatus?: boolean;
  isOnline?: boolean;
  onClick?: () => void;
  loading?: boolean;
}

const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-base',
  xl: 'w-16 h-16 text-lg',
  '2xl': 'w-20 h-20 text-xl'
};

const onlineStatusSizes = {
  xs: 'w-2 h-2',
  sm: 'w-2.5 h-2.5',
  md: 'w-3 h-3',
  lg: 'w-3.5 h-3.5',
  xl: 'w-4 h-4',
  '2xl': 'w-5 h-5'
};

export const UnifiedAvatar: React.FC<UnifiedAvatarProps> = ({
  userId,
  fullName,
  size = 'md',
  className,
  showOnlineStatus = false,
  isOnline = false,
  onClick,
  loading = false
}) => {
  const { displayAvatar, isLoading } = useProfilePicture(userId, fullName);
  const [imageError, setImageError] = useState(false);

  // Generate initials fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2) || 'U';
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageError(false);
  };

  const isShowingImage = displayAvatar && !imageError && !isLoading && !loading;

  return (
    <div className={cn('relative inline-block', className)}>
      <div
        className={cn(
          'rounded-full overflow-hidden bg-gradient-to-r from-teal-600 to-amber-500 flex items-center justify-center font-medium text-white transition-all duration-200',
          sizeClasses[size],
          onClick && 'cursor-pointer hover:shadow-lg hover:scale-105',
          (isLoading || loading) && 'animate-pulse bg-gray-300'
        )}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={onClick ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        } : undefined}
      >
        {isShowingImage ? (
          <img
            src={displayAvatar}
            alt={`${fullName}'s profile picture`}
            className="w-full h-full object-cover"
            onError={handleImageError}
            onLoad={handleImageLoad}
          />
        ) : (isLoading || loading) ? (
          <div className="w-full h-full bg-gray-300 animate-pulse" />
        ) : (
          <span className="select-none">
            {getInitials(fullName)}
          </span>
        )}
      </div>

      {/* Online Status Indicator */}
      {showOnlineStatus && (
        <div
          className={cn(
            'absolute bottom-0 right-0 rounded-full border-2 border-white',
            onlineStatusSizes[size],
            isOnline ? 'bg-green-500' : 'bg-gray-400'
          )}
          title={isOnline ? 'Online' : 'Offline'}
        />
      )}
    </div>
  );
};

/**
 * Avatar component specifically for navigation headers
 */
interface NavigationAvatarProps {
  userId: string;
  fullName: string;
  userType?: string;
  onClick?: () => void;
  showDropdownIndicator?: boolean;
  className?: string;
}

export const NavigationAvatar: React.FC<NavigationAvatarProps> = ({
  userId,
  fullName,
  userType,
  onClick,
  showDropdownIndicator = false,
  className
}) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <UnifiedAvatar
        userId={userId}
        fullName={fullName}
        size="sm"
        onClick={onClick}
      />
      
      {/* User Info (Desktop) */}
      <div className="hidden md:block text-left">
        <p className="text-sm font-medium text-gray-700 truncate max-w-32">
          {fullName}
        </p>
        {userType && (
          <p className="text-xs text-gray-500 capitalize">
            {userType.replace('_', ' ')}
          </p>
        )}
      </div>

      {/* Dropdown Indicator */}
      {showDropdownIndicator && (
        <svg
          className="w-4 h-4 text-gray-400 transition-transform"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      )}
    </div>
  );
};

/**
 * Avatar component for user lists and tables
 */
interface ListAvatarProps {
  userId: string;
  fullName: string;
  email?: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ListAvatar: React.FC<ListAvatarProps> = ({
  userId,
  fullName,
  email,
  subtitle,
  size = 'md',
  className
}) => {
  return (
    <div className={cn('flex items-center space-x-3', className)}>
      <UnifiedAvatar
        userId={userId}
        fullName={fullName}
        size={size}
      />
      
      <div className="min-w-0 flex-1">
        <div className="text-sm font-medium text-gray-900 truncate">
          {fullName}
        </div>
        {(email || subtitle) && (
          <div className="text-sm text-gray-500 truncate">
            {subtitle || email}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Avatar component for profile sections
 */
interface ProfileAvatarProps {
  userId: string;
  fullName: string;
  size?: 'lg' | 'xl' | '2xl';
  editable?: boolean;
  onEdit?: () => void;
  className?: string;
}

export const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  userId,
  fullName,
  size = 'xl',
  editable = false,
  onEdit,
  className
}) => {
  return (
    <div className={cn('relative', className)}>
      <UnifiedAvatar
        userId={userId}
        fullName={fullName}
        size={size}
        onClick={editable ? onEdit : undefined}
      />
      
      {/* Edit Overlay */}
      {editable && (
        <div
          className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
          onClick={onEdit}
        >
          <User className="w-6 h-6 text-white" />
        </div>
      )}
    </div>
  );
};

export default UnifiedAvatar;
