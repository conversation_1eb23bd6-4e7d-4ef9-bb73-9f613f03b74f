/**
 * User Profile Dropdown Component for YalaOffice
 * Displays user info, profile options, and logout functionality with responsive design
 */

import React, { useState, useEffect, useRef } from 'react';
import { User, LogOut, ChevronDown, Shield, UserCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMobileUtils } from '../../hooks/use-mobile';
import { useAuth } from '../../contexts/AuthContext';
import { YalaLogo } from './YalaLogo';
import { NavigationAvatar, UnifiedAvatar } from './UnifiedAvatar';

interface UserProfileDropdownProps {
  onNavigateToProfile?: () => void;
  onLogout: () => void;
  className?: string;
}

export const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({
  onNavigateToProfile,
  onLogout,
  className
}) => {
  const { user } = useAuth();
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle profile navigation
  const handleProfileClick = () => {
    setIsOpen(false);
    if (onNavigateToProfile) {
      onNavigateToProfile();
    } else {
      // Default navigation to profile page
      window.location.href = '/profile';
    }
  };

  // Handle logout
  const handleLogout = () => {
    setIsOpen(false);
    onLogout();
  };

  // Get user initials for avatar
  const getUserInitials = (name?: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Get user type display name
  const getUserTypeDisplay = (userType?: string) => {
    switch (userType) {
      case 'admin':
        return 'Administrator';
      case 'manager':
        return 'Manager';
      case 'client':
        return 'Client';
      case 'reseller':
        return 'Reseller';
      case 'delivery':
      case 'delivery_person':
        return 'Delivery Person';
      default:
        return 'User';
    }
  };

  // Get user type color
  const getUserTypeColor = (userType?: string) => {
    switch (userType) {
      case 'admin':
        return 'text-red-600 bg-red-50';
      case 'manager':
        return 'text-purple-600 bg-purple-50';
      case 'client':
        return 'text-blue-600 bg-blue-50';
      case 'reseller':
        return 'text-green-600 bg-green-50';
      case 'delivery':
      case 'delivery_person':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      {/* Profile Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center space-x-2 rounded-lg hover:bg-gray-100 transition-colors',
          shouldUseTouchUI ? 'p-3 min-h-[44px]' : 'p-2',
          isOpen && 'bg-gray-100'
        )}
        aria-label="User profile menu"
      >
        <NavigationAvatar
          userId={user?.id || ''}
          fullName={user?.full_name || user?.fullName || 'User'}
          userType={getUserTypeDisplay(user?.userType || user?.user_type)}
          showDropdownIndicator={!isMobile}
        />
      </button>

      {/* Dropdown/Modal */}
      {isOpen && (
        <>
          {/* Mobile overlay */}
          {isMobile && (
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsOpen(false)}
            />
          )}
          
          {/* Dropdown content */}
          <div className={cn(
            'bg-white rounded-lg shadow-xl border border-gray-200 z-50',
            // Mobile: Bottom sheet modal
            isMobile ? [
              'fixed inset-x-4 bottom-20 max-h-80',
              'transform transition-transform duration-300'
            ] : [
              // Desktop: Positioned dropdown
              'absolute right-0 top-full mt-2 w-64'
            ]
          )}>
            {/* User Info Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <UnifiedAvatar
                  userId={user?.id || ''}
                  fullName={user?.full_name || user?.fullName || 'User'}
                  size="lg"
                />
                
                {/* User Details */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 truncate">
                    {user?.full_name || user?.fullName || 'User'}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {user?.email}
                  </p>
                  
                  {/* User Type Badge */}
                  <div className={cn(
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1',
                    getUserTypeColor(user?.userType || user?.user_type)
                  )}>
                    <Shield className="w-3 h-3 mr-1" />
                    {getUserTypeDisplay(user?.userType || user?.user_type)}
                  </div>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              {/* Profile/Settings */}
              <button
                onClick={handleProfileClick}
                className={cn(
                  'w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors',
                  shouldUseTouchUI && 'min-h-[48px]'
                )}
              >
                <UserCircle className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Profile</p>
                  <p className="text-xs text-gray-500">Manage your account settings</p>
                </div>
              </button>



              {/* Divider */}
              <div className="border-t border-gray-100 my-2"></div>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className={cn(
                  'w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors text-red-600',
                  shouldUseTouchUI && 'min-h-[48px]'
                )}
              >
                <LogOut className="h-5 w-5" />
                <div>
                  <p className="text-sm font-medium">Sign Out</p>
                  <p className="text-xs text-red-500">Log out of your account</p>
                </div>
              </button>
            </div>

            {/* Footer (Mobile only) */}
            {isMobile && (
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <p className="text-xs text-gray-500 text-center">
                  YalaOffice v2.1.0
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default UserProfileDropdown;
