import { useState, useEffect } from 'react';
import { X, Star, ShoppingCart, Package, Check, Minus, Plus, Scale } from 'lucide-react';
import { Product } from '../../types/inventory';
import { getProductById } from '../../services/inventoryService';
import ImageService from '../../services/imageService';

interface ProductComparisonProps {
  productIds: string[];
  isOpen: boolean;
  onClose: () => void;
  userType: 'client' | 'reseller';
  onAddToCart?: (product: any) => void;
  onRemoveFromComparison?: (productId: string) => void;
}

const ProductComparison = ({ 
  productIds, 
  isOpen, 
  onClose, 
  userType, 
  onAddToCart,
  onRemoveFromComparison 
}: ProductComparisonProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && productIds.length > 0) {
      loadProducts();
    }
  }, [isOpen, productIds]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const productPromises = productIds.map(id => getProductById(id));
      const productsData = await Promise.all(productPromises);
      
      // Filter out null results
      const validProducts = productsData.filter((product): product is Product => product !== null);
      setProducts(validProducts);
    } catch (err) {
      setError('Failed to load products for comparison');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product: Product) => {
    if (onAddToCart) {
      const cartProduct = {
        id: parseInt(product.id.replace('PRD-', '')),
        title: product.title,
        category: product.category,
        price: userType === 'reseller' ? product.resellerPrice : product.price,
        resellerPrice: product.resellerPrice,
        image: product.featuredImage,
        rating: product.rating,
        inStock: product.stock > 0,
        stock: product.stock
      };
      onAddToCart(cartProduct);
    }
  };

  const handleRemoveProduct = (productId: string) => {
    if (onRemoveFromComparison) {
      onRemoveFromComparison(productId);
    }
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} Dh`;
  };

  const getDisplayPrice = (product: Product) => {
    return userType === 'reseller' ? product.resellerPrice : product.price;
  };

  const getSavings = (product: Product) => {
    return userType === 'reseller' ? product.price - product.resellerPrice : 0;
  };

  const renderComparisonRow = (label: string, getValue: (product: Product) => any, type: 'text' | 'price' | 'rating' | 'boolean' | 'stock' = 'text') => {
    return (
      <tr className="border-b border-gray-200">
        <td className="py-3 px-4 font-medium text-gray-900 bg-gray-50 sticky left-0 z-10">
          {label}
        </td>
        {products.map((product) => {
          const value = getValue(product);
          let displayValue;

          switch (type) {
            case 'price':
              displayValue = formatPrice(value);
              break;
            case 'rating':
              displayValue = (
                <div className="flex items-center">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(value)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="ml-1 text-sm text-gray-600">({value})</span>
                </div>
              );
              break;
            case 'boolean':
              displayValue = value ? (
                <Check className="h-5 w-5 text-green-500" />
              ) : (
                <Minus className="h-5 w-5 text-gray-400" />
              );
              break;
            case 'stock':
              displayValue = (
                <span className={`font-medium ${value > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {value > 0 ? `${value} in stock` : 'Out of stock'}
                </span>
              );
              break;
            default:
              displayValue = value || 'N/A';
          }

          return (
            <td key={product.id} className="py-3 px-4 text-center">
              {displayValue}
            </td>
          );
        })}
      </tr>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Scale className="h-6 w-6 text-orange-500" />
            <h2 className="text-2xl font-bold text-gray-900">Product Comparison</h2>
            <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-sm font-medium">
              {products.length} products
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {loading && (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products for comparison...</p>
          </div>
        )}

        {error && (
          <div className="p-8 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadProducts}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && products.length > 0 && (
          <div className="flex-1 overflow-auto">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 sticky top-0 z-20">
                  <tr>
                    <th className="py-4 px-4 text-left font-semibold text-gray-900 sticky left-0 z-30 bg-gray-50">
                      Features
                    </th>
                    {products.map((product) => (
                      <th key={product.id} className="py-4 px-4 text-center min-w-64">
                        <div className="space-y-3">
                          {/* Product Image */}
                          <div className="relative">
                            <img
                              src={ImageService.getBestImageUrl(product, 96)}
                              alt={product.title}
                              className="w-24 h-24 object-cover rounded-lg mx-auto border border-gray-200"
                              onError={(e) => {
                                e.currentTarget.src = ImageService.generateFallbackImage(product.title, 96);
                              }}
                            />
                            <button
                              onClick={() => handleRemoveProduct(product.id)}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                          
                          {/* Product Title */}
                          <h3 className="font-semibold text-gray-900 text-sm line-clamp-2">
                            {product.title}
                          </h3>
                          
                          {/* Price */}
                          <div className="space-y-1">
                            <div className="text-xl font-bold text-gray-900">
                              {formatPrice(getDisplayPrice(product))}
                            </div>
                            {userType === 'reseller' && getSavings(product) > 0 && (
                              <div className="text-sm">
                                <span className="text-gray-500 line-through">
                                  {formatPrice(product.price)}
                                </span>
                                <span className="text-green-600 font-medium ml-2">
                                  Save {formatPrice(getSavings(product))}
                                </span>
                              </div>
                            )}
                          </div>
                          
                          {/* Add to Cart Button */}
                          <button
                            onClick={() => handleAddToCart(product)}
                            disabled={product.stock === 0}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-all ${
                              product.stock > 0
                                ? 'bg-orange-500 text-white hover:bg-orange-600'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                          >
                            {product.stock > 0 ? (
                              <>
                                <ShoppingCart className="h-4 w-4 inline mr-2" />
                                Add to Cart
                              </>
                            ) : (
                              'Out of Stock'
                            )}
                          </button>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                
                <tbody>
                  {renderComparisonRow('Brand', (p) => p.brand)}
                  {renderComparisonRow('Category', (p) => p.category)}
                  {renderComparisonRow('Rating', (p) => p.rating, 'rating')}
                  {renderComparisonRow('Stock Status', (p) => p.stock, 'stock')}
                  {renderComparisonRow('SKU', (p) => p.sku)}
                  {renderComparisonRow('Weight', (p) => p.weight ? `${p.weight} kg` : 'N/A')}
                  {renderComparisonRow('New Product', (p) => p.isNew, 'boolean')}
                  {renderComparisonRow('Active', (p) => p.isActive, 'boolean')}
                  
                  {/* Dimensions */}
                  {products.some(p => p.dimensions) && (
                    <>
                      {renderComparisonRow('Length', (p) => p.dimensions ? `${p.dimensions.length} cm` : 'N/A')}
                      {renderComparisonRow('Width', (p) => p.dimensions ? `${p.dimensions.width} cm` : 'N/A')}
                      {renderComparisonRow('Height', (p) => p.dimensions ? `${p.dimensions.height} cm` : 'N/A')}
                    </>
                  )}
                  
                  {/* Tags */}
                  {renderComparisonRow('Tags', (p) => p.tags.join(', '))}
                  
                  {/* Dates */}
                  {renderComparisonRow('Created', (p) => new Date(p.createdAt).toLocaleDateString())}
                  {renderComparisonRow('Updated', (p) => new Date(p.updatedAt).toLocaleDateString())}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {!loading && !error && products.length === 0 && (
          <div className="p-8 text-center">
            <Scale className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Products to Compare</h3>
            <p className="text-gray-600">Add products to your comparison list to see them here.</p>
          </div>
        )}

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Comparing {products.length} product{products.length !== 1 ? 's' : ''}
              {userType === 'reseller' && (
                <span className="ml-2 bg-teal-100 text-teal-800 px-2 py-1 rounded-full text-xs font-medium">
                  Reseller Pricing
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close Comparison
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductComparison;
