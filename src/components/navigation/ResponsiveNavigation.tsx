/**
 * Unified Responsive Navigation for YalaOffice
 * Combines desktop and mobile navigation into a single responsive component
 */

import React, { useState, useEffect } from 'react';
import {
  Home,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Menu,
  X,
  Bell,
  Search,
  User,
  Building2,
  UserCheck,
  FolderPlus,
  MapPin,
  Tag,
  Shield,
  Activity,
  LogOut,
  MessageSquare,
  Star,
  Truck,
  History,
  Route
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '../../contexts/AuthContext';
import { useMobileUtils } from '../../hooks/use-mobile';
import { useResponsiveSpacing } from '../../hooks/useResponsive';
import NotificationDropdown from '../ui/NotificationDropdown';
import UserProfileDropdown from '../ui/UserProfileDropdown';
import { YalaLogo, MobileLogo, HeaderLogo } from '../ui/YalaLogo';

interface ResponsiveNavigationProps {
  currentPage: string;
  activeTab?: string;
  onNavigate: (page: string, tab?: string) => void;
  userType?: 'admin' | 'manager' | 'client' | 'reseller' | 'delivery';
  onNavigateToProfile?: () => void;
  onLogout?: () => void;
  className?: string;
  loading?: boolean;
}

export const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({
  currentPage,
  activeTab,
  onNavigate,
  userType = 'admin',
  onNavigateToProfile,
  onLogout,
  className,
  loading = false
}) => {
  const { user } = useAuth();
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  const spacing = useResponsiveSpacing();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [notifications, setNotifications] = useState(3);

  // Get navigation items based on user type
  const getNavigationItems = () => {
    const adminItems = [
      { id: 'dashboard', label: 'Dashboard', icon: Home, color: 'text-blue-600' },
      { id: 'user-management', label: 'User Management', icon: Users, color: 'text-purple-600' },
      { id: 'product-management', label: 'Product Management', icon: Package, color: 'text-green-600' },
      { id: 'orders', label: 'Order Management', icon: ShoppingCart, color: 'text-orange-600' },
      { id: 'settings', label: 'System Settings', icon: Settings, color: 'text-gray-600' }
    ];

    const managerItems = [
      { id: 'dashboard', label: 'Dashboard', icon: Home, color: 'text-blue-600' },
      { id: 'product-management', label: 'Product Management', icon: Package, color: 'text-green-600' },
      { id: 'orders', label: 'Order Management', icon: ShoppingCart, color: 'text-orange-600' },
      { id: 'reviews', label: 'Reviews & Comments', icon: MessageSquare, color: 'text-amber-600' }
    ];

    const clientItems = [
      { id: 'dashboard', label: 'Shop', icon: Package, color: 'text-green-600' },
      { id: 'orders', label: 'My Orders', icon: ShoppingCart, color: 'text-orange-600' },
      { id: 'profile', label: 'Profile', icon: User, color: 'text-gray-600' }
    ];

    const resellerItems = [
      { id: 'dashboard', label: 'Shop', icon: Package, color: 'text-green-600' },
      { id: 'orders', label: 'My Orders', icon: ShoppingCart, color: 'text-orange-600' },
      { id: 'profile', label: 'Profile', icon: User, color: 'text-gray-600' }
    ];

    const deliveryItems = [
      { id: 'dashboard', label: 'Orders', icon: Truck, color: 'text-orange-600' },
      { id: 'history', label: 'History', icon: History, color: 'text-blue-600' },
      { id: 'profile', label: 'Profile', icon: User, color: 'text-gray-600' }
    ];

    switch (userType) {
      case 'admin':
        return adminItems;
      case 'manager':
        return managerItems;
      case 'delivery':
      case 'delivery_person':
        return deliveryItems;
      case 'client':
        return clientItems;
      case 'reseller':
        return resellerItems;
      default:
        return clientItems;
    }
  };

  // Role-based access control for navigation items
  const hasAccessToPage = (pageId: string) => {
    const rolePermissions = {
      admin: ['dashboard', 'user-management', 'product-management', 'orders', 'settings', 'analytics', 'profile'],
      manager: ['dashboard', 'product-management', 'orders', 'reviews', 'analytics', 'profile'],
      client: ['dashboard', 'orders', 'profile'],
      reseller: ['dashboard', 'orders', 'profile'],
      delivery: ['dashboard', 'history', 'profile'],
      delivery_person: ['dashboard', 'history', 'profile']
    };

    const userPermissions = rolePermissions[userType as keyof typeof rolePermissions] || rolePermissions.client;
    return userPermissions.includes(pageId);
  };

  const navigationItems = getNavigationItems().filter(item => hasAccessToPage(item.id));

  // Get navigation items for mobile bottom navigation (exclude System Settings)
  const getMobileBottomNavigationItems = () => {
    return navigationItems.filter(item => item.id !== 'system-settings');
  };

  const mobileBottomNavigationItems = getMobileBottomNavigationItems();

  // Get tabs for dashboard page
  const getDashboardTabs = () => {
    const currentItem = navigationItems.find(item => item.id === 'dashboard');
    return currentItem?.tabs || [];
  };

  const dashboardTabs = getDashboardTabs();

  // Handle navigation with special handling for client/reseller navigation
  const handleNavigation = (pageId: string, tabId?: string) => {
    // Special handling for client/reseller "My Orders" navigation
    if ((userType === 'client' || userType === 'reseller') && pageId === 'orders') {
      onNavigate('orders', 'orders');
    }
    // Special handling for client/reseller "Shop" navigation
    else if ((userType === 'client' || userType === 'reseller') && pageId === 'dashboard') {
      onNavigate('dashboard', 'products');
    } else {
      onNavigate(pageId, tabId);
    }

    if (isMobile) {
      setIsMenuOpen(false);
    }
  };

  // Close menu on outside click (mobile)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && isMenuOpen) {
        const target = event.target as Element;
        if (!target.closest('.mobile-menu') && !target.closest('.menu-toggle')) {
          setIsMenuOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, isMenuOpen]);

  return (
    <div className={cn('bg-white', className)}>
      {/* Mobile Header */}
      {isMobile && (
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {/* Menu Toggle */}
          <button
            className={cn(
              'menu-toggle rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center',
              shouldUseTouchUI ? 'p-3 min-w-[44px] min-h-[44px]' : 'p-2'
            )}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? (
              <X className={cn('text-gray-600', isSmallMobile ? 'h-5 w-5' : 'h-6 w-6')} />
            ) : (
              <Menu className={cn('text-gray-600', isSmallMobile ? 'h-5 w-5' : 'h-6 w-6')} />
            )}
          </button>

          {/* Logo */}
          <MobileLogo
            showText={true}
            textSize={isSmallMobile ? 'sm' : 'base'}
            size={isSmallMobile ? 'sm' : 'md'}
          />

          {/* Mobile Actions */}
          <div className="flex items-center space-x-2">
            {/* Notifications */}
            <NotificationDropdown userId={user?.id || 'demo-user'} />

            {/* Logout Button */}
            <button
              onClick={() => onLogout && onLogout()}
              className={cn(
                'rounded-lg hover:bg-red-50 transition-colors flex items-center justify-center',
                shouldUseTouchUI ? 'p-3 min-w-[44px] min-h-[44px]' : 'p-2'
              )}
              aria-label="Logout"
              title="Logout"
            >
              <LogOut className={cn('text-red-600', isSmallMobile ? 'h-5 w-5' : 'h-6 w-6')} />
            </button>
          </div>
        </div>
      )}

      {/* Mobile Sidebar Menu */}
      {isMobile && (
        <>
          {/* Overlay */}
          {isMenuOpen && (
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsMenuOpen(false)}
            />
          )}
          
          {/* Sidebar */}
          <div className={cn(
            'mobile-menu fixed left-0 top-0 h-full bg-white shadow-xl transform transition-transform duration-300 z-50',
            isMenuOpen ? 'translate-x-0' : '-translate-x-full',
            isSmallMobile ? 'w-72' : 'w-80'
          )}>
            {/* Menu Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <YalaLogo size="xl" />
                <div>
                  <h2 className="font-bold text-gray-900">YalaOffice</h2>
                  <p className="text-sm text-gray-600 capitalize">{user?.role || userType} Dashboard</p>
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 overflow-y-auto py-4">
              <nav className={cn('space-y-2', isSmallMobile ? 'px-3' : 'px-4')}>
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentPage === item.id;
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleNavigation(item.id)}
                      disabled={loading}
                      className={cn(
                        'w-full flex items-center rounded-xl transition-all duration-200',
                        shouldUseTouchUI ? 'min-h-[48px] px-4 py-3' : 'px-4 py-3',
                        isSmallMobile ? 'space-x-2' : 'space-x-3',
                        isActive
                          ? 'bg-teal-50 border-l-4 border-teal-600'
                          : 'hover:bg-gray-50 active:bg-gray-100',
                        loading && 'opacity-50 cursor-not-allowed'
                      )}
                    >
                      {loading && isActive ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
                      ) : (
                        <Icon className={cn(
                          isSmallMobile ? 'h-4 w-4' : 'h-5 w-5',
                          isActive ? 'text-teal-600' : item.color
                        )} />
                      )}
                      <span className={cn(
                        'font-medium',
                        isSmallMobile ? 'text-sm' : 'text-base',
                        isActive ? 'text-teal-600' : 'text-gray-700'
                      )}>
                        {item.label}
                      </span>
                    </button>
                  );
                })}

                {/* Divider */}
                <div className="border-t border-gray-200 my-4"></div>

                {/* Logout Option */}
                <button
                  onClick={() => {
                    setIsMenuOpen(false);
                    onLogout && onLogout();
                  }}
                  className={cn(
                    'w-full flex items-center rounded-xl transition-all duration-200',
                    shouldUseTouchUI ? 'min-h-[48px] px-4 py-3' : 'px-4 py-3',
                    isSmallMobile ? 'space-x-2' : 'space-x-3',
                    'hover:bg-red-50 active:bg-red-100'
                  )}
                >
                  <LogOut className={cn(
                    isSmallMobile ? 'h-4 w-4' : 'h-5 w-5',
                    'text-red-600'
                  )} />
                  <span className={cn(
                    'font-medium',
                    isSmallMobile ? 'text-sm' : 'text-base',
                    'text-red-600'
                  )}>
                    Logout
                  </span>
                </button>
              </nav>
            </div>
          </div>
        </>
      )}

      {/* Desktop Navigation */}
      {!isMobile && (
        <div className="border-b border-gray-200">
          {/* Main Navigation */}
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-6">
              {/* Logo */}
              <HeaderLogo />

              {/* Navigation Items */}
              <nav className="flex items-center space-x-1">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentPage === item.id;
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleNavigation(item.id)}
                      className={cn(
                        'flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors font-medium',
                        isActive
                          ? 'bg-teal-50 text-teal-600 border border-teal-200'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      )}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <NotificationDropdown userId={user?.id || 'demo-user'} />

              {/* User Profile */}
              <UserProfileDropdown
                onNavigateToProfile={onNavigateToProfile}
                onLogout={onLogout || (() => console.log('Logout not implemented'))}
              />
            </div>
          </div>

          {/* Dashboard Tabs (when on dashboard page) - Responsive */}
          {currentPage === 'dashboard' && dashboardTabs.length > 0 && (
            <div className={cn(
              'bg-gray-50 border-b border-gray-200',
              isMobile ? 'px-4 py-2' : 'px-6 py-3'
            )}>
              <div className={cn(
                'flex overflow-x-auto scrollbar-hide',
                isMobile ? 'space-x-1 pb-1' : 'space-x-1'
              )}>
                {dashboardTabs.map((tab) => (
                  <button
                    key={tab}
                    onClick={() => handleNavigation('dashboard', tab)}
                    className={cn(
                      'rounded-lg transition-colors whitespace-nowrap font-medium flex-shrink-0',
                      // Mobile-specific styling
                      isMobile ? [
                        shouldUseTouchUI ? 'px-3 py-2 min-h-[44px] text-xs' : 'px-3 py-2 text-xs',
                        'min-w-[80px] flex items-center justify-center'
                      ] : [
                        // Desktop styling
                        'px-4 py-2 text-sm'
                      ],
                      // Active/inactive states
                      activeTab === tab
                        ? 'bg-white text-teal-600 shadow-sm border border-teal-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    )}
                  >
                    {isMobile && tab.length > 8 ?
                      tab.charAt(0).toUpperCase() + tab.slice(1, 6) + '...' :
                      tab.charAt(0).toUpperCase() + tab.slice(1)
                    }
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Mobile Bottom Navigation - Enhanced for Client/Reseller */}
      {isMobile && (
        <div className={cn(
          'fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30 shadow-lg',
          shouldUseTouchUI ? 'pb-safe' : ''
        )}>
          <div className={cn(
            'grid gap-1',
            mobileBottomNavigationItems.length <= 3 ? 'grid-cols-3' : 'grid-cols-4'
          )}>
            {mobileBottomNavigationItems.map((item) => {
              const Icon = item.icon;
              // For client/reseller users, handle active state based on current page and active tab
              const isActive = (userType === 'client' || userType === 'reseller')
                ? (item.id === 'dashboard' && currentPage === 'dashboard') ||
                  (item.id === 'orders' && currentPage === 'orders') ||
                  (item.id === 'profile' && currentPage === 'profile')
                : currentPage === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    console.log('Bottom nav clicked:', item.id, item.label);
                    handleNavigation(item.id);
                  }}
                  className={cn(
                    'flex flex-col items-center justify-center transition-all duration-200 relative group',
                    shouldUseTouchUI ? 'py-3 px-2 min-h-[56px]' : 'py-3 px-2 min-h-[48px]',
                    'hover:bg-gray-50 active:bg-gray-100',
                    isActive ? 'text-teal-600 bg-teal-50' : 'text-gray-600'
                  )}
                  aria-label={item.label}
                  role="tab"
                  aria-selected={isActive}
                >
                  {/* Icon with enhanced sizing */}
                  <Icon className={cn(
                    'mb-1 transition-transform group-active:scale-95',
                    isSmallMobile ? 'h-5 w-5' : 'h-6 w-6'
                  )} />

                  {/* Label with responsive text sizing */}
                  <span className={cn(
                    'font-medium transition-colors',
                    isSmallMobile ? 'text-xs' : 'text-xs',
                    isActive ? 'text-teal-600' : 'text-gray-600'
                  )}>
                    {/* Smart label truncation for different screen sizes */}
                    {isSmallMobile && item.label.length > 8 ?
                      item.label.substring(0, 6) + '...' :
                      item.label
                    }
                  </span>

                  {/* Active indicator with enhanced styling */}
                  {isActive && (
                    <>
                      {/* Top border indicator */}
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-teal-600 rounded-b-full"></div>
                      {/* Bottom dot indicator */}
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-teal-600 rounded-full"></div>
                    </>
                  )}

                  {/* Ripple effect for touch feedback */}
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 bg-teal-600 opacity-0 group-active:opacity-10 transition-opacity duration-150"></div>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Safe area padding for devices with home indicators */}
          {shouldUseTouchUI && (
            <div className="h-2 bg-white"></div>
          )}
        </div>
      )}
    </div>
  );
};

export default ResponsiveNavigation;
