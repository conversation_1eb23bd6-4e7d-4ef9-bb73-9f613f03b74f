
import { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { LogOut, Bell, Menu, User } from 'lucide-react';
import AdminDashboard from './dashboards/AdminDashboard';
import StoreManagerDashboard from './dashboards/StoreManagerDashboard';
import DeliveryDashboard from './dashboards/DeliveryDashboard';
import ClientDashboard from './dashboards/ClientDashboard';
import ResellerDashboard from './dashboards/ResellerDashboard';
import NotificationSystem from './notifications/NotificationSystem';
import NotificationBell from './ui/NotificationBell';
import ResponsiveLayout from './layout/ResponsiveLayout';
import ResponsiveNavigation from './navigation/ResponsiveNavigation';
import { useMobileUtils } from '../hooks/use-mobile';

// Import management components
import UserManagement from './admin/UserManagement';
import ProductManagement from './inventory/ProductManagement';
import OrderManagement from './orders/OrderManagement';
import AdvancedAnalyticsDashboard from './analytics/AdvancedAnalyticsDashboard';
import SystemAdministration from './system/SystemAdministration';
import ProfileManagement from './profile/ProfileManagement';
import ReviewsManagementPage from './pages/ReviewsManagementPage';
import { liveDataService } from '../services/liveDataService';
import { realTimeService } from '../services/realTimeService';

interface DashboardProps {
  user: any;
  onLogout: () => void;
  onNavigateToProfile?: () => void;
  dashboardNavigateRef?: React.MutableRefObject<((page: string) => void) | null>;
}

const Dashboard = ({ user, onLogout, onNavigateToProfile, dashboardNavigateRef }: DashboardProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);

  // Initialize page and tab from URL parameters
  const [currentPage, setCurrentPage] = useState(() => {
    return searchParams.get('page') || 'dashboard';
  });
  const [activeTab, setActiveTab] = useState(() => {
    return searchParams.get('tab') || 'overview';
  });
  const [dashboardNavigate, setDashboardNavigate] = useState<((page: string) => void) | null>(null);
  const [realTimeData, setRealTimeData] = useState({
    orders: [],
    products: [],
    users: [],
    notifications: []
  });
  const [dataLoading, setDataLoading] = useState(false);
  const [navigationLoading, setNavigationLoading] = useState(false);
  const [navigationError, setNavigationError] = useState<string | null>(null);

  // Enhanced responsive utilities
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();

  // Handle navigation between pages and tabs with URL updates and loading states
  const handleNavigate = async (page: string, tab?: string) => {
    try {
      setNavigationLoading(true);
      setNavigationError(null);
      console.log('Dashboard: Navigation to', { page, tab });

      // Validate page access based on user role
      const rolePermissions = {
        admin: ['dashboard', 'user-management', 'product-management', 'order-management', 'system-settings', 'analytics', 'profile'],
        manager: ['dashboard', 'product-management', 'order-management', 'reviews', 'analytics', 'profile'],
        client: ['dashboard', 'orders', 'profile'],
        reseller: ['dashboard', 'orders', 'profile'],
        delivery: ['dashboard', 'history', 'profile'],
        delivery_person: ['dashboard', 'history', 'profile']
      };

      const userPermissions = rolePermissions[user.userType as keyof typeof rolePermissions] || rolePermissions.client;
      if (!userPermissions.includes(page)) {
        throw new Error(`Access denied: You don't have permission to access ${page}`);
      }

      setCurrentPage(page);

      // Update URL parameters
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('page', page);

      if (page === 'dashboard' && tab) {
        setActiveTab(tab);
        newSearchParams.set('tab', tab);
      } else if (page !== 'dashboard') {
        newSearchParams.delete('tab');
        setActiveTab('');
      }

      // Update URL without page reload
      setSearchParams(newSearchParams);

      // For client/reseller users, pass navigation to appropriate dashboard
      if ((user.userType === 'client' || user.userType === 'reseller') && dashboardNavigate) {
        dashboardNavigate(page);
      }

      // Update the navigation function for child components
      const navFunction = (childPage: string) => handleNavigate(childPage);
      setDashboardNavigate(() => navFunction);

    } catch (error) {
      console.error('Dashboard: Navigation error:', error);
      setNavigationError(error instanceof Error ? error.message : 'Navigation failed');
    } finally {
      setNavigationLoading(false);
    }
  };

  // Handle profile navigation
  const handleNavigateToProfile = () => {
    console.log('Dashboard: Navigating to profile');
    handleNavigate('profile');
  };

  // Sync URL changes with component state
  useEffect(() => {
    const page = searchParams.get('page') || 'dashboard';
    const tab = searchParams.get('tab') || '';

    if (page !== currentPage) {
      setCurrentPage(page);
    }
    if (tab !== activeTab) {
      setActiveTab(tab);
    }
  }, [searchParams, currentPage, activeTab]);

  // Initialize real-time data synchronization
  useEffect(() => {
    const initializeRealTimeData = async () => {
      try {
        setDataLoading(true);
        console.log('Dashboard: Initializing real-time data synchronization');

        // Load initial data
        const [orders, products, users] = await Promise.all([
          liveDataService.getAllOrders(),
          liveDataService.getAllProducts(),
          liveDataService.getAllUsers()
        ]);

        setRealTimeData({
          orders: orders || [],
          products: products || [],
          users: users || [],
          notifications: []
        });

        // Set up real-time subscriptions
        const subscriptions = [
          realTimeService.subscribeToOrders((updatedOrders) => {
            setRealTimeData(prev => ({ ...prev, orders: updatedOrders }));
          }),
          realTimeService.subscribeToProducts((updatedProducts) => {
            setRealTimeData(prev => ({ ...prev, products: updatedProducts }));
          }),
          realTimeService.subscribeToUsers((updatedUsers) => {
            setRealTimeData(prev => ({ ...prev, users: updatedUsers }));
          })
        ];

        console.log('Dashboard: Real-time subscriptions established');

        // Cleanup subscriptions on unmount
        return () => {
          subscriptions.forEach(unsubscribe => {
            if (typeof unsubscribe === 'function') {
              unsubscribe();
            }
          });
        };

      } catch (error) {
        console.error('Dashboard: Error initializing real-time data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    initializeRealTimeData();
  }, []);

  // Update the ref so Index.tsx can access the navigation function
  useEffect(() => {
    if (dashboardNavigateRef) {
      dashboardNavigateRef.current = (page: string) => handleNavigate(page);
    }
  }, [dashboardNavigateRef]);



  const renderPageContent = () => {
    const commonProps = {
      user,
      onLogout,
      onNavigateToProfile,
      dashboardNavigateRef: { current: (page: string) => handleNavigate(page) }
    };

    // Special props for ClientDashboard to handle bottom navigation
    const clientProps = {
      ...commonProps,
      dashboardNavigateRef: { current: dashboardNavigate }
    };

    // Route to different pages based on currentPage
    switch (currentPage) {
      case 'dashboard':
        // Render the appropriate dashboard based on user type
        switch (user.userType) {
          case 'admin':
            return <AdminDashboard {...commonProps} />;
          case 'manager':
            return <StoreManagerDashboard {...commonProps} />;
          case 'delivery':
          case 'delivery_person':
            return <DeliveryDashboard {...commonProps} />;
          case 'client':
            return <ClientDashboard {...clientProps} />;
          case 'reseller':
            return <ResellerDashboard {...clientProps} />;
          default:
            return <ClientDashboard {...commonProps} />;
        }

      case 'user-management':
        return <UserManagement currentUserId={user.id} />;

      case 'product-management':
        return <ProductManagement />;

      case 'order-management':
        return <OrderManagement />;

      case 'analytics':
        return <AdvancedAnalyticsDashboard />;

      case 'system-settings':
        return <SystemAdministration />;

      case 'reviews':
        return <ReviewsManagementPage />;

      case 'history':
        // For delivery users, show delivery history
        if (user.userType === 'delivery' || user.userType === 'delivery_person') {
          return <DeliveryDashboard {...commonProps} />;
        }
        return <div>Page not found</div>;

      case 'orders':
        // For client/reseller users, show their respective dashboard with orders tab
        if (user.userType === 'client') {
          return <ClientDashboard {...clientProps} />;
        } else if (user.userType === 'reseller') {
          return <ResellerDashboard {...clientProps} />;
        }
        return <div>Page not found</div>;

      case 'profile':
        return <ProfileManagement user={user} />;

      default:
        // Default to dashboard for the user's type
        switch (user.userType) {
          case 'admin':
            return <AdminDashboard {...commonProps} />;
          case 'manager':
            return <StoreManagerDashboard {...commonProps} />;
          case 'delivery':
          case 'delivery_person':
            return <DeliveryDashboard {...commonProps} />;
          case 'client':
            return <ClientDashboard {...clientProps} />;
          case 'reseller':
            return <ResellerDashboard {...clientProps} />;
          default:
            return <ClientDashboard {...commonProps} />;
        }
    }
  };

  // Close dropdowns when clicking outside
  const handleOutsideClick = () => {
    setShowNotifications(false);
    setShowProfile(false);
  };

  return (
    <div onClick={handleOutsideClick}>
      <ResponsiveLayout
        navigation={
          <ResponsiveNavigation
            currentPage={currentPage}
            activeTab={activeTab}
            onNavigate={handleNavigate}
            userType={user.userType}
            onNavigateToProfile={handleNavigateToProfile}
            onLogout={onLogout}
            loading={navigationLoading}
          />
        }
        className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-orange-50"
      >
        {/* Navigation Error Display */}
        {navigationError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md m-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{navigationError}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setNavigationError(null)}
                  className="inline-flex text-red-400 hover:text-red-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Loading Indicator */}
        {(navigationLoading || dataLoading) && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            <span className="ml-3 text-gray-600">Loading...</span>
          </div>
        )}

        {/* Page Content */}
        {!navigationLoading && !dataLoading && renderPageContent()}
      </ResponsiveLayout>

      {/* Notification System */}
      <NotificationSystem
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
    </div>
  );
};

export default Dashboard;
