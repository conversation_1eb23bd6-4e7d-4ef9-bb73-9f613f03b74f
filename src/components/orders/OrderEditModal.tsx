import React, { useState, useEffect } from 'react';
import { X, Plus, Minus, Trash2, Search, Save, AlertCircle, Building2 } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { formatCurrency } from '../../utils/currency';
import { syncOrderUpdated } from '../../services/realTimeService';
import { getBranches } from '../../services/branchService';
import { Branch } from '../../types/branch';

interface Customer {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
}

interface Product {
  id: string;
  title: string;
  price: number;
  stock: number;
  sku?: string;
  featured_image?: string;
}

interface OrderItem {
  id?: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product?: Product;
}

interface OrderEditData {
  id: string;
  order_number: string;
  customer_id: string;
  branch_id?: string;
  status: string;
  payment_status?: string;
  payment_method?: string;
  subtotal: number;
  delivery_fee?: number;
  discount_amount?: number;
  tax_amount?: number;
  total: number;
  delivery_address?: any;
  billing_address?: any;
  notes?: string;
  branches?: {
    id: string;
    name: string;
    address: any;
    coordinates?: any;
  };
  order_items: OrderItem[];
  customer?: Customer;
}

interface OrderEditModalProps {
  orderId: string;
  onClose: () => void;
  onOrderUpdated: () => void;
}

const OrderEditModal: React.FC<OrderEditModalProps> = ({ orderId, onClose, onOrderUpdated }) => {
  const [order, setOrder] = useState<OrderEditData | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [showProductDropdown, setShowProductDropdown] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(true);

  useEffect(() => {
    loadOrderData();
    loadCustomers();
    loadProducts();
    loadBranches();
  }, [orderId]);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  };

  const loadBranches = async () => {
    try {
      setLoadingBranches(true);
      const activeBranches = await getBranches(true);
      setBranches(activeBranches);
    } catch (error) {
      console.error('Error loading branches:', error);
    } finally {
      setLoadingBranches(false);
    }
  };

  // Get branch display text with distance if available
  const getBranchDisplayText = (branch: Branch): string => {
    let displayText = `${branch.name} - ${branch.address.city}`;

    // Add distance if both order delivery coordinates and branch coordinates are available
    if (order?.delivery_address?.latitude && order?.delivery_address?.longitude && branch.coordinates) {
      const distance = calculateDistance(
        order.delivery_address.latitude,
        order.delivery_address.longitude,
        branch.coordinates.latitude,
        branch.coordinates.longitude
      );
      displayText += ` (Distance: ${distance} km)`;
    }

    return displayText;
  };

  const loadOrderData = async () => {
    try {
      setLoading(true);
      const { data, error: fetchError } = await supabase
        .from('orders')
        .select(`
          *,
          branches!orders_branch_id_fkey (
            id,
            name,
            address,
            coordinates
          ),
          users!orders_customer_id_fkey (
            id,
            full_name,
            email,
            phone
          ),
          order_items (
            id,
            product_id,
            quantity,
            unit_price,
            total_price,
            products (
              id,
              title,
              price,
              stock,
              sku,
              featured_image
            )
          )
        `)
        .eq('id', orderId)
        .single();

      if (fetchError) throw fetchError;

      console.log('Loaded order data:', data); // Debug log
      console.log('Branch info:', {
        branch_id: data.branch_id,
        branches: data.branches
      }); // Debug log

      setOrder({
        ...data,
        customer: data.users,
        order_items: data.order_items.map((item: any) => ({
          ...item,
          product: item.products
        }))
      });
    } catch (err) {
      console.error('Error loading order:', err);
      setError(err instanceof Error ? err.message : 'Failed to load order');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const customerData = await liveDataService.getAllCustomersLegacy();
      setCustomers(customerData);
    } catch (err) {
      console.error('Error loading customers:', err);
    }
  };

  const loadProducts = async () => {
    try {
      const productData = await liveDataService.getAllProducts();
      const activeProducts = productData.filter(p => p.isActive && p.stock > 0);
      setProducts(activeProducts);
    } catch (err) {
      console.error('Error loading products:', err);
    }
  };

  const calculateTotals = (items: OrderItem[]) => {
    const subtotal = items.reduce((sum, item) => sum + item.total_price, 0);
    const deliveryFee = order?.delivery_fee || 0;
    const discountAmount = order?.discount_amount || 0;
    const taxAmount = order?.tax_amount || 0;
    const total = subtotal + deliveryFee - discountAmount + taxAmount;

    return { subtotal, total };
  };

  const updateOrderItem = (productId: string, quantity: number) => {
    if (!order) return;

    const updatedItems = order.order_items.map(item => {
      if (item.product_id === productId) {
        const newTotalPrice = item.unit_price * quantity;
        return { ...item, quantity, total_price: newTotalPrice };
      }
      return item;
    });

    const { subtotal, total } = calculateTotals(updatedItems);
    setOrder({ ...order, order_items: updatedItems, subtotal, total });
  };

  const removeOrderItem = (productId: string) => {
    if (!order) return;

    const updatedItems = order.order_items.filter(item => item.product_id !== productId);
    const { subtotal, total } = calculateTotals(updatedItems);
    setOrder({ ...order, order_items: updatedItems, subtotal, total });
  };

  const addProduct = (product: Product) => {
    if (!order) return;

    // Check if product already exists in order
    const existingItem = order.order_items.find(item => item.product_id === product.id);
    if (existingItem) {
      updateOrderItem(product.id, existingItem.quantity + 1);
    } else {
      const newItem: OrderItem = {
        product_id: product.id,
        quantity: 1,
        unit_price: product.price,
        total_price: product.price,
        product: product
      };

      const updatedItems = [...order.order_items, newItem];
      const { subtotal, total } = calculateTotals(updatedItems);
      setOrder({ ...order, order_items: updatedItems, subtotal, total });
    }

    setProductSearch('');
    setShowProductDropdown(false);
  };

  const changeCustomer = (customer: Customer) => {
    if (!order) return;

    setOrder({ ...order, customer_id: customer.id, customer });
    setCustomerSearch('');
    setShowCustomerDropdown(false);
  };

  const saveOrder = async () => {
    if (!order) return;

    try {
      setSaving(true);
      setError(null);

      // Update order
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          customer_id: order.customer_id,
          branch_id: order.branch_id,
          status: order.status,
          payment_status: order.payment_status,
          payment_method: order.payment_method,
          subtotal: order.subtotal,
          delivery_fee: order.delivery_fee,
          discount_amount: order.discount_amount,
          tax_amount: order.tax_amount,
          total: order.total,
          delivery_address: order.delivery_address,
          billing_address: order.billing_address,
          notes: order.notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', order.id);

      if (orderError) throw orderError;

      // Delete existing order items
      const { error: deleteError } = await supabase
        .from('order_items')
        .delete()
        .eq('order_id', order.id);

      if (deleteError) throw deleteError;

      // Insert updated order items
      const orderItemsToInsert = order.order_items.map(item => ({
        order_id: order.id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItemsToInsert);

      if (itemsError) throw itemsError;

      // Emit real-time events
      syncOrderUpdated(order.id, order);

      onOrderUpdated();
      onClose();
    } catch (err) {
      console.error('Error saving order:', err);
      setError(err instanceof Error ? err.message : 'Failed to save order');
    } finally {
      setSaving(false);
    }
  };

  const filteredCustomers = customers.filter(customer =>
    customer.full_name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  const filteredProducts = products.filter(product =>
    product.title.toLowerCase().includes(productSearch.toLowerCase()) ||
    (product.sku && product.sku.toLowerCase().includes(productSearch.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
            <span className="text-gray-600">Loading order...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return null;
  }

  const canEdit = !['delivered', 'cancelled', 'returned'].includes(order.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-teal-600 to-amber-500 text-white rounded-t-xl">
          <div>
            <h2 className="text-2xl font-bold">Edit Order</h2>
            <p className="text-teal-100">Order #{order.order_number}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {!canEdit && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 m-6">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  This order cannot be edited because it has been {order.status}.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 space-y-6">
          {/* Customer Selection */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customer
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={customerSearch || order.customer?.full_name || ''}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value);
                    setShowCustomerDropdown(true);
                  }}
                  onFocus={() => setShowCustomerDropdown(true)}
                  placeholder="Search customers..."
                  disabled={!canEdit}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
                />
                <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>

              {showCustomerDropdown && canEdit && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredCustomers.map(customer => (
                    <div
                      key={customer.id}
                      onClick={() => changeCustomer(customer)}
                      className="px-4 py-2 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="font-medium text-gray-900">{customer.full_name}</div>
                      <div className="text-sm text-gray-500">{customer.email}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Order Details */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={order.status}
                  onChange={(e) => setOrder({ ...order, status: e.target.value })}
                  disabled={!canEdit}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
                >
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="preparing">Preparing</option>
                  <option value="ready">Ready</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="returned">Returned</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Status
                </label>
                <select
                  value={order.payment_status || 'pending'}
                  onChange={(e) => setOrder({ ...order, payment_status: e.target.value })}
                  disabled={!canEdit}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
                >
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select
                  value={order.payment_method || 'cash'}
                  onChange={(e) => setOrder({ ...order, payment_method: e.target.value })}
                  disabled={!canEdit}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
                >
                  <option value="cash">Cash</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="check">Check</option>
                </select>
              </div>
            </div>
          </div>

          {/* Delivery Branch Selection */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Building2 className="h-5 w-5 text-teal-600 mr-2" />
              Delivery Branch Information
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Delivery Branch
                </label>
                <select
                  value={order.branch_id || ''}
                  onChange={(e) => {
                    setOrder({
                      ...order,
                      branch_id: e.target.value
                    });
                  }}
                  disabled={!canEdit || loadingBranches}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
                >
                  <option value="">
                    {loadingBranches ? 'Loading branches...' : 'Choose preferred branch...'}
                  </option>
                  {branches.map(branch => (
                    <option key={branch.id} value={branch.id}>
                      {getBranchDisplayText(branch)}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  This helps optimize delivery routing
                </p>
              </div>

              {order.branch_id && (
                <div className="bg-teal-50 border border-teal-200 rounded-lg p-3">
                  {(() => {
                    const selectedBranch = branches.find(b => b.id === order.branch_id) || order.branches;
                    if (selectedBranch) {
                      return (
                        <div>
                          <p className="text-sm text-teal-800">
                            <strong>Selected Branch:</strong> {selectedBranch.name}
                            {selectedBranch.address?.city && ` - ${selectedBranch.address.city}`}
                          </p>
                          {selectedBranch.address && (
                            <p className="text-xs text-teal-600 mt-1">
                              {selectedBranch.address.street && `${selectedBranch.address.street}, `}
                              {selectedBranch.address.city}
                            </p>
                          )}
                        </div>
                      );
                    }
                    return <p className="text-sm text-teal-800">Branch selected</p>;
                  })()}
                </div>
              )}
            </div>
          </div>

          {/* Add Product */}
          {canEdit && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Products</h3>
              <div className="relative">
                <input
                  type="text"
                  value={productSearch}
                  onChange={(e) => {
                    setProductSearch(e.target.value);
                    setShowProductDropdown(true);
                  }}
                  onFocus={() => setShowProductDropdown(true)}
                  placeholder="Search products to add..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
                <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />

                {showProductDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {filteredProducts.map(product => (
                      <div
                        key={product.id}
                        onClick={() => addProduct(product)}
                        className="px-4 py-2 hover:bg-gray-50 cursor-pointer"
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium text-gray-900">{product.title}</div>
                            <div className="text-sm text-gray-500">
                              {formatCurrency(product.price)} • Stock: {product.stock}
                            </div>
                          </div>
                          <Plus className="h-4 w-4 text-teal-600" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Order Items */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Order Items</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    {canEdit && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {order.order_items.map((item) => (
                    <tr key={item.product_id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {item.product?.featured_image && (
                            <img
                              className="h-10 w-10 rounded-lg object-cover mr-3"
                              src={item.product.featured_image}
                              alt={item.product.title}
                            />
                          )}
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.product?.title || 'Unknown Product'}
                            </div>
                            {item.product?.sku && (
                              <div className="text-sm text-gray-500">SKU: {item.product.sku}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.unit_price)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {canEdit ? (
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => updateOrderItem(item.product_id, Math.max(1, item.quantity - 1))}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <span className="text-sm font-medium text-gray-900 min-w-[2rem] text-center">
                              {item.quantity}
                            </span>
                            <button
                              onClick={() => updateOrderItem(item.product_id, item.quantity + 1)}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-900">{item.quantity}</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatCurrency(item.total_price)}
                      </td>
                      {canEdit && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => removeOrderItem(item.product_id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">{formatCurrency(order.subtotal)}</span>
              </div>
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total:</span>
                  <span className="text-lg font-bold text-teal-600">{formatCurrency(order.total)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-gray-50 rounded-lg p-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={order.notes || ''}
              onChange={(e) => setOrder({ ...order, notes: e.target.value })}
              disabled={!canEdit}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100"
              placeholder="Add any notes about this order..."
            />
          </div>

          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          {canEdit && (
            <button
              onClick={saveOrder}
              disabled={saving}
              className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Save Changes</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderEditModal;
