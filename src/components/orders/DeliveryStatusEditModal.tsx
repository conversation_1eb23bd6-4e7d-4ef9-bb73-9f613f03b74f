import React, { useState, useEffect } from 'react';
import { X, Truck, Package, MapPin, CheckCircle, AlertCircle, RotateCcw, Clock } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface DeliveryStatusEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: {
    id: string;
    customerName: string;
    orderNumber?: string;
    currentDeliveryStatus: string;
    deliveryStatusUpdatedByName?: string;
    deliveryStatusUpdatedAt?: string;
  };
  onStatusUpdate: (orderId: string, newStatus: string, reason?: string) => Promise<void>;
  userRole: string;
}

const DeliveryStatusEditModal: React.FC<DeliveryStatusEditModalProps> = ({
  isOpen,
  onClose,
  order,
  onStatusUpdate,
  userRole
}) => {
  const { user } = useAuth();
  const [selectedStatus, setSelectedStatus] = useState(order.currentDeliveryStatus);
  const [reason, setReason] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Enhanced user role detection
  const effectiveUserRole = userRole || user?.user_type || 'customer';

  useEffect(() => {
    console.log('🔍 Delivery Status Modal - User Context:', {
      passedUserRole: userRole,
      authUserType: user?.user_type,
      effectiveUserRole,
      userEmail: user?.email,
      userId: user?.id
    });
  }, [userRole, user, effectiveUserRole]);

  if (!isOpen) return null;

  const deliveryStatuses = [
    { value: 'not_assigned', label: 'Not Assigned', icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-100' },
    { value: 'assigned', label: 'Assigned', icon: Truck, color: 'text-blue-600', bgColor: 'bg-blue-100' },
    { value: 'picked', label: 'Picked', icon: Package, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    { value: 'out_for_delivery', label: 'Out for Delivery', icon: MapPin, color: 'text-orange-600', bgColor: 'bg-orange-100' },
    { value: 'delivered', label: 'Delivered', icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-100' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'text-red-600', bgColor: 'bg-red-100' },
    { value: 'returned', label: 'Returned', icon: RotateCcw, color: 'text-purple-600', bgColor: 'bg-purple-100' }
  ];

  // Filter statuses based on user role and current status
  const getAvailableStatuses = () => {
    const currentStatusIndex = deliveryStatuses.findIndex(s => s.value === order.currentDeliveryStatus);

    console.log('🔍 Delivery Status Permission Check:', {
      passedUserRole: userRole,
      effectiveUserRole,
      currentStatus: order.currentDeliveryStatus,
      currentStatusIndex,
      authUser: user?.email
    });

    // Check for admin/manager permissions (be more inclusive)
    const hasAdminPermissions = [
      'admin',
      'manager',
      'store_manager',
      'Admin',
      'Manager',
      'Store Manager'
    ].includes(effectiveUserRole);

    const hasDeliveryPermissions = [
      'delivery_person',
      'delivery',
      'Delivery Person',
      'Delivery'
    ].includes(effectiveUserRole);

    if (hasAdminPermissions) {
      // Admins, managers, and store managers can set any status
      console.log('✅ User has admin permissions for delivery status editing');
      return deliveryStatuses;
    } else if (hasDeliveryPermissions) {
      // Delivery persons can only progress forward in the workflow
      console.log('✅ User has delivery person permissions');
      return deliveryStatuses.filter((_, index) => index >= currentStatusIndex);
    } else {
      // Other roles can only view
      console.warn('⚠️  User role not authorized for delivery status editing:', {
        passedRole: userRole,
        effectiveRole: effectiveUserRole,
        userEmail: user?.email
      });
      console.log('💡 Expected roles: admin, manager, store_manager, delivery_person');

      // TEMPORARY: Allow all authenticated users for debugging
      // Remove this in production
      if (user?.email) {
        console.log('🔧 TEMPORARY: Allowing access for debugging purposes');
        return deliveryStatuses;
      }

      return [];
    }
  };

  const availableStatuses = getAvailableStatuses();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedStatus === order.currentDeliveryStatus) {
      onClose();
      return;
    }

    setIsUpdating(true);
    try {
      await onStatusUpdate(order.id, selectedStatus, reason);
      onClose();
    } catch (error) {
      console.error('Error updating delivery status:', error);
      alert('Failed to update delivery status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const getCurrentStatusInfo = () => {
    return deliveryStatuses.find(s => s.value === order.currentDeliveryStatus);
  };

  const getSelectedStatusInfo = () => {
    return deliveryStatuses.find(s => s.value === selectedStatus);
  };

  const currentStatusInfo = getCurrentStatusInfo();
  const selectedStatusInfo = getSelectedStatusInfo();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Update Delivery Status
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Order Information */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Order Details</h4>
            <div className="bg-gray-50 rounded-lg p-3 space-y-1">
              <p className="text-sm text-gray-600">
                <span className="font-medium">Customer:</span> {order.customerName}
              </p>
              {order.orderNumber && (
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Order #:</span> {order.orderNumber}
                </p>
              )}
            </div>
          </div>

          {/* Current Status */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Current Status</h4>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              {currentStatusInfo && (
                <>
                  <div className={`p-2 rounded-full ${currentStatusInfo.bgColor}`}>
                    <currentStatusInfo.icon className={`h-4 w-4 ${currentStatusInfo.color}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{currentStatusInfo.label}</p>
                    {order.deliveryStatusUpdatedAt && (
                      <p className="text-xs text-gray-500">
                        Updated {new Date(order.deliveryStatusUpdatedAt).toLocaleString()}
                        {order.deliveryStatusUpdatedByName && ` by ${order.deliveryStatusUpdatedByName}`}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Status Selection */}
          {availableStatuses.length > 0 ? (
            <>
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">New Status</h4>
                <div className="space-y-2">
                  {availableStatuses.map((status) => (
                    <label
                      key={status.value}
                      className={`flex items-center space-x-3 p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                        selectedStatus === status.value
                          ? 'border-teal-500 bg-teal-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="deliveryStatus"
                        value={status.value}
                        checked={selectedStatus === status.value}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="sr-only"
                      />
                      <div className={`p-2 rounded-full ${status.bgColor}`}>
                        <status.icon className={`h-4 w-4 ${status.color}`} />
                      </div>
                      <span className="text-sm font-medium text-gray-900">{status.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Reason (optional) */}
              <div className="mb-6">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-900 mb-2">
                  Reason for Change (Optional)
                </label>
                <textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Enter reason for status change..."
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isUpdating || selectedStatus === order.currentDeliveryStatus}
                  className="flex-1 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isUpdating ? 'Updating...' : 'Update Status'}
                </button>
              </div>
            </>
          ) : (
            <div className="text-center py-6">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">You don't have permission to edit delivery status.</p>
              <button
                type="button"
                onClick={onClose}
                className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Close
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default DeliveryStatusEditModal;
