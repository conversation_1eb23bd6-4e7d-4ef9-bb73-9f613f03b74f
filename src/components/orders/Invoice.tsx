
import { useState, useEffect } from 'react';
import { X, Download, Building2, FileText, Loader } from 'lucide-react';
import { Order } from '../../types/order';
import { generateInvoiceNumber, formatOrderDate } from '../../utils/orderUtils';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { supabase } from '../../integrations/supabase/client';
import { realTimeService } from '../../services/realTimeService';

interface InvoiceProps {
  order: Order;
  onClose: () => void;
  paymentMethod?: 'bank_transfer' | 'check' | 'cash';
}

const Invoice = ({ order, onClose, paymentMethod = 'cash' }: InvoiceProps) => {
  // Use order's payment method as primary source, fallback to prop
  const actualPaymentMethod = (order.paymentMethod as 'bank_transfer' | 'check' | 'cash') || paymentMethod;
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [companyInfo, setCompanyInfo] = useState({
    name: 'YalaOffice Morocco',
    address: 'Casablanca, Morocco',
    phone: '+212 5XX-XXX-XXX',
    email: '<EMAIL>',
    taxId: 'TAX-********',
    iceNumber: 'ICE-********'
  });
  const [paymentMethods, setPaymentMethods] = useState({
    bankTransfer: {
      enabled: true,
      bankName: 'Banque Populaire',
      accountNumber: '**********',
      iban: 'MA64 0123 4567 8901 2345 6789 01',
      swiftCode: 'BMCEMAMC',
      accountHolder: 'YalaOffice SARL'
    },
    check: {
      enabled: true,
      payableTo: 'YalaOffice SARL',
      mailingAddress: 'Casablanca, Morocco',
      processingTime: '3-5 business days'
    }
  });
  const invoiceNumber = generateInvoiceNumber(order.id);

  // Debug logging to understand data structure
  console.log('Invoice order data:', order);
  console.log('Invoice order items:', order.items);
  console.log('Invoice delivery address:', order.deliveryAddress);
  console.log('Invoice payment method - order.paymentMethod:', order.paymentMethod);
  console.log('Invoice payment method - prop paymentMethod:', paymentMethod);
  console.log('Invoice payment method - actualPaymentMethod:', actualPaymentMethod);

  // Load saved company information and payment methods
  useEffect(() => {
    loadInvoiceData();

    // Listen for real-time updates
    const companyInfoSubscription = realTimeService.subscribe('company-info-updated', (data) => {
      console.log('Invoice: Company info updated', data);
      setCompanyInfo(data.companyInfo);
    });

    const paymentMethodsSubscription = realTimeService.subscribe('payment-methods-updated', (data) => {
      console.log('Invoice: Payment methods updated', data);
      setPaymentMethods(data.paymentMethods);
    });

    return () => {
      companyInfoSubscription();
      paymentMethodsSubscription();
    };
  }, []);

  const loadInvoiceData = async () => {
    try {
      // Load company information using liveDataService
      try {
        const { liveDataService } = await import('../../services/liveDataService');
        const companyData = await liveDataService.getCompanyInfo();
        if (companyData) {
          setCompanyInfo({
            name: companyData.name || 'YalaOffice Morocco',
            address: companyData.address || 'Casablanca, Morocco',
            phone: companyData.phone || '+212 5XX-XXX-XXX',
            email: companyData.email || '<EMAIL>',
            taxId: companyData.tax_id || 'TAX-********',
            iceNumber: companyData.ice_number || 'ICE-********'
          });
        }
      } catch (error) {
        console.error('Error loading company info:', error);
        // Fallback to localStorage
        const savedCompanyInfo = localStorage.getItem('yala_company_info');
        if (savedCompanyInfo) {
          const parsed = JSON.parse(savedCompanyInfo);
          setCompanyInfo(prev => ({ ...prev, ...parsed }));
        }
      }

      // Load payment methods using liveDataService
      try {
        const { liveDataService } = await import('../../services/liveDataService');
        const paymentData = await liveDataService.getPaymentMethods();
        if (paymentData) {
          setPaymentMethods(paymentData);
        }
      } catch (error) {
        console.error('Error loading payment methods:', error);
        // Fallback to localStorage
        const savedPaymentMethods = localStorage.getItem('yala_payment_methods');
        if (savedPaymentMethods) {
          const parsed = JSON.parse(savedPaymentMethods);
          setPaymentMethods(prev => ({ ...prev, ...parsed }));
        }
      }
    } catch (error) {
      console.error('Error loading invoice data:', error);
    }
  };

  // Format delivery address from JSON to readable string
  const formatDeliveryAddress = (address: any): string => {
    if (!address) return 'No address provided';

    // If it's already a string, return it
    if (typeof address === 'string') {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(address);
        return formatAddressObject(parsed);
      } catch {
        // If parsing fails, return the string as is
        return address;
      }
    }

    // If it's an object, format it
    if (typeof address === 'object') {
      return formatAddressObject(address);
    }

    return 'Invalid address format';
  };

  const formatAddressObject = (addressObj: any): string => {
    const parts = [];

    if (addressObj.address) parts.push(addressObj.address);
    if (addressObj.city) parts.push(addressObj.city);
    if (addressObj.postalCode) parts.push(addressObj.postalCode);

    return parts.filter(Boolean).join(', ') || 'Address not available';
  };

  // Use order's customer data for Bill To section instead of authenticated user
  const customerData = {
    type: order.customer?.userType || 'client',
    fullName: order.customer?.fullName || order.customerName || 'Unknown Customer',
    email: order.customer?.email || order.customerEmail || 'No email provided',
    phone: order.customer?.phone || order.customer?.companyPhone || 'No phone provided',
    company: order.customer?.isCompany ? order.customer?.companyName : null,
    companyAddress: order.customer?.companyAddress || null,
    taxId: order.customer?.taxId || null,
    iceNumber: order.customer?.iceNumber || null,
    city: order.customer?.city || order.customer?.companyCity || null,
    address: order.customer?.companyAddress || formatDeliveryAddress(order.deliveryAddress)
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true);
    try {
      // Get the invoice content element
      const invoiceElement = document.getElementById('invoice-content');
      if (!invoiceElement) {
        throw new Error('Invoice content not found');
      }

      // Create canvas from the invoice content
      const canvas = await html2canvas(invoiceElement, {
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: invoiceElement.scrollWidth,
        height: invoiceElement.scrollHeight
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Calculate dimensions to fit A4
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      // Add the image to PDF
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      pdf.save(`YalaOffice-Invoice-${invoiceNumber}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 print:hidden">
          <h2 className="text-2xl font-bold text-gray-900">Invoice</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
              className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGeneratingPDF ? (
                <>
                  <Loader className="h-4 w-4 animate-spin" />
                  <span>Generating PDF...</span>
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4" />
                  <span>Download PDF</span>
                </>
              )}
            </button>
            <button
              onClick={handlePrint}
              className="flex items-center space-x-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Print</span>
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div id="invoice-content" className="p-8 print:p-0">
          {/* Invoice Header */}
          <div className="flex justify-between items-start mb-8">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-3 rounded-lg">
                <Building2 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{companyInfo.name}</h1>
                <p className="text-gray-600">Office & School Supplies</p>
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-2xl font-bold text-gray-900">INVOICE</h2>
              <p className="text-gray-600">{invoiceNumber}</p>
              <p className="text-sm text-gray-500 mt-2">Order: {order.id}</p>
            </div>
          </div>

          {/* Company & Customer Info */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">From:</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p className="font-medium">{companyInfo.name}</p>
                <p>{companyInfo.address}</p>
                <p>Phone: {companyInfo.phone}</p>
                <p>Email: {companyInfo.email}</p>
                <p>Tax ID: {companyInfo.taxId}</p>
                <p>ICE: {companyInfo.iceNumber}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Bill To:</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="flex items-center space-x-2">
                  <p className="font-medium">{customerData.fullName}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    customerData.type === 'reseller' ? 'bg-purple-100 text-purple-800' :
                    customerData.type === 'admin' ? 'bg-red-100 text-red-800' :
                    customerData.type === 'manager' ? 'bg-green-100 text-green-800' :
                    customerData.type === 'delivery_person' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {customerData.type.charAt(0).toUpperCase() + customerData.type.slice(1).replace('_', ' ')}
                  </span>
                </div>
                <p>{customerData.email}</p>
                {customerData.company && (
                  <div className="mt-2">
                    <p className="font-medium text-gray-700">Company:</p>
                    <p className="text-gray-600">{customerData.company}</p>
                  </div>
                )}
                {customerData.address && (
                  <div className="mt-2">
                    <p className="font-medium text-gray-700">Address:</p>
                    <p className="text-gray-600">{customerData.address}</p>
                  </div>
                )}
                {customerData.city && <p>City: {customerData.city}</p>}
                <p>Phone: {customerData.phone}</p>
                {customerData.taxId && <p>Tax ID: {customerData.taxId}</p>}
                {customerData.iceNumber && <p>ICE Number: {customerData.iceNumber}</p>}
                <p className="mt-2">
                  <span className="font-medium">Branch:</span> {order.selectedBranch}
                </p>
              </div>
            </div>
          </div>

          {/* Invoice Details */}
          <div className="grid md:grid-cols-3 gap-4 mb-8 text-sm">
            <div>
              <p className="font-medium text-gray-900">Invoice Date:</p>
              <p className="text-gray-600">{formatOrderDate(order.createdAt)}</p>
            </div>
            <div>
              <p className="font-medium text-gray-900">Payment Method:</p>
              <p className="text-gray-600">{order.paymentMethod}</p>
            </div>
            <div>
              <p className="font-medium text-gray-900">Payment Status:</p>
              <p className={`font-medium ${
                order.paymentStatus === 'paid' ? 'text-green-600' : 
                order.paymentStatus === 'pending' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
              </p>
            </div>
          </div>

          {/* Customer Type Benefits */}
          {customerData.type === 'reseller' && (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
              <h4 className="font-medium text-purple-900 mb-2">Reseller Benefits Applied</h4>
              <ul className="text-sm text-purple-800 space-y-1">
                <li>• Special wholesale pricing</li>
                <li>• Extended payment terms available</li>
                <li>• Priority customer support</li>
              </ul>
            </div>
          )}

          {/* Items Table */}
          <div className="mb-8">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-900">Item</th>
                  <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-900">Category</th>
                  <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-900">Qty</th>
                  <th className="border border-gray-300 px-4 py-3 text-right text-sm font-medium text-gray-900">Unit Price</th>
                  <th className="border border-gray-300 px-4 py-3 text-right text-sm font-medium text-gray-900">Total</th>
                </tr>
              </thead>
              <tbody>
                {order.items && order.items.length > 0 ? order.items.map((item, index) => {
                  // Handle different possible data structures
                  const itemTitle = item.title || item.name || item.product_name || 'Unknown Item';
                  const itemCategory = item.category || item.category_name || 'Uncategorized';
                  const itemQuantity = item.quantity || 1;
                  const itemPrice = item.price || item.unit_price || 0;
                  const itemTotal = itemQuantity * itemPrice;

                  return (
                    <tr key={item.id || index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                        {itemTitle}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm text-gray-600">
                        {itemCategory}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm text-center text-gray-900">
                        {itemQuantity}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm text-right text-gray-900">
                        {Number(itemPrice).toFixed(2)} Dh
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-sm text-right text-gray-900">
                        {itemTotal.toFixed(2)} Dh
                      </td>
                    </tr>
                  );
                }) : (
                  <tr>
                    <td colSpan={5} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                      No items found in this order
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-80">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="text-gray-900">{order.subtotal.toFixed(2)} Dh</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee:</span>
                  <span className="text-gray-900">{order.deliveryFee === 0 ? 'Free' : `${order.deliveryFee.toFixed(2)} Dh`}</span>
                </div>
                {order.promoCode && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount ({order.promoCode}):</span>
                    <span>-{((order.subtotal + (order.deliveryFee || 0)) - order.total).toFixed(2)} Dh</span>
                  </div>
                )}
                <div className="border-t border-gray-300 pt-2">
                  <div className="flex justify-between text-lg font-bold text-gray-900">
                    <span>Total Amount:</span>
                    <span>{order.total.toFixed(2)} Dh</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="border-t border-gray-200 pt-6 mb-6">
            <h3 className="font-semibold text-gray-900 mb-4">Payment Information</h3>
            <div className="text-sm">
              {/* Bank Transfer Information */}
              {actualPaymentMethod === 'bank_transfer' && paymentMethods.bankTransfer.enabled && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Bank Transfer</h4>
                  <div className="text-blue-800 space-y-1">
                    <p><strong>Bank:</strong> {paymentMethods.bankTransfer.bankName}</p>
                    <p><strong>Account:</strong> {paymentMethods.bankTransfer.accountNumber}</p>
                    <p><strong>IBAN:</strong> {paymentMethods.bankTransfer.iban}</p>
                    <p><strong>SWIFT:</strong> {paymentMethods.bankTransfer.swiftCode}</p>
                    <p><strong>Account Holder:</strong> {paymentMethods.bankTransfer.accountHolder}</p>
                  </div>
                </div>
              )}

              {/* Check Payment Information */}
              {actualPaymentMethod === 'check' && paymentMethods.check.enabled && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-medium text-green-900 mb-2">Check Payment</h4>
                  <div className="text-green-800 space-y-1">
                    <p><strong>Payable to:</strong> {paymentMethods.check.payableTo}</p>
                    <p><strong>Mail to:</strong> {paymentMethods.check.mailingAddress}</p>
                    <p><strong>Processing:</strong> {paymentMethods.check.processingTime}</p>
                  </div>
                </div>
              )}

              {/* Cash Payment Information */}
              {actualPaymentMethod === 'cash' && (
                <div className="bg-amber-50 p-4 rounded-lg">
                  <h4 className="font-medium text-amber-900 mb-2">Cash on Delivery</h4>
                  <div className="text-amber-800 space-y-1">
                    <p><strong>Payment Method:</strong> Cash on Delivery (COD)</p>
                    <p><strong>Instructions:</strong> Please have the exact amount ready upon delivery.</p>
                    <p><strong>Note:</strong> Payment is due upon receipt of goods.</p>
                    <p><strong>Total Amount Due:</strong> {order.total.toFixed(2)} Dh</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 pt-6 text-center text-sm text-gray-500">
            <p>Thank you for your business!</p>
            <p className="mt-2">
              For questions about this invoice, contact us at {companyInfo.email} or {companyInfo.phone}
            </p>
            <p className="mt-2 text-xs">
              This invoice is generated electronically and is valid without signature.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Invoice;
