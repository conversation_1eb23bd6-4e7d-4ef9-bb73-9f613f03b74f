
import { useState } from 'react';
import { Plus, Edit, Trash2, Building2, Banknote, FileText } from 'lucide-react';
import { PaymentMethod } from '../../types/userDefinitions';
import { addPaymentMethod, updatePaymentMethod, deletePaymentMethod } from '../../services/userService';
import { useToast } from '../../hooks/use-toast';

interface PaymentMethodManagementProps {
  userId: string;
  paymentMethods: PaymentMethod[];
  onPaymentMethodsChange: (methods: PaymentMethod[]) => void;
}

const PaymentMethodManagement = ({ userId, paymentMethods, onPaymentMethodsChange }: PaymentMethodManagementProps) => {
  const [showForm, setShowForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);
  const [formData, setFormData] = useState({
    type: 'bank_transfer' as 'bank_transfer' | 'check' | 'cash_on_delivery',
    label: '',
    isDefault: false,
    bankName: '',
    accountNumber: '',
    checkDetails: ''
  });
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      type: 'bank_transfer',
      label: '',
      isDefault: false,
      bankName: '',
      accountNumber: '',
      checkDetails: ''
    });
    setEditingMethod(null);
    setShowForm(false);
  };

  const handleEdit = (method: PaymentMethod) => {
    setFormData({
      type: method.type as any,
      label: method.label,
      isDefault: method.isDefault,
      bankName: method.details.bankName || '',
      accountNumber: method.details.accountNumber || '',
      checkDetails: method.details.checkDetails || ''
    });
    setEditingMethod(method);
    setShowForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const methodData = {
        type: formData.type,
        label: formData.label,
        isDefault: formData.isDefault,
        details: formData.type === 'bank_transfer' ? {
          bankName: formData.bankName,
          accountNumber: formData.accountNumber.slice(-4) // Store only last 4 digits
        } : formData.type === 'check' ? {
          checkDetails: formData.checkDetails
        } : {}
      };

      if (editingMethod) {
        // Update existing method
        const updatedMethod = await updatePaymentMethod(editingMethod.id, methodData);
        if (updatedMethod) {
          const updatedMethods = paymentMethods.map(method => 
            method.id === editingMethod.id ? updatedMethod : method
          );
          onPaymentMethodsChange(updatedMethods);
          toast({
            title: "Payment Method Updated",
            description: "Your payment method has been successfully updated.",
          });
        }
      } else {
        // Add new method
        const newMethod = await addPaymentMethod(userId, methodData);
        onPaymentMethodsChange([...paymentMethods, newMethod]);
        toast({
          title: "Payment Method Added",
          description: "Your new payment method has been successfully added.",
        });
      }
      resetForm();
    } catch (error) {
      console.error('Error saving payment method:', error);
      toast({
        title: "Error",
        description: "Failed to save payment method. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (methodId: string) => {
    try {
      const success = await deletePaymentMethod(methodId);
      if (success) {
        const updatedMethods = paymentMethods.filter(method => method.id !== methodId);
        onPaymentMethodsChange(updatedMethods);
        toast({
          title: "Payment Method Deleted",
          description: "The payment method has been successfully deleted.",
        });
      }
    } catch (error) {
      console.error('Error deleting payment method:', error);
      toast({
        title: "Error",
        description: "Failed to delete payment method. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getPaymentIcon = (type: string) => {
    switch (type) {
      case 'bank_transfer': return Building2;
      case 'check': return FileText;
      case 'cash_on_delivery': return Banknote;
      default: return Building2;
    }
  };

  const formatPaymentDetails = (method: PaymentMethod) => {
    switch (method.type) {
      case 'bank_transfer':
        return `${method.details.bankName} - ****${method.details.accountNumber}`;
      case 'check':
        return method.details.checkDetails || 'Check payment';
      case 'cash_on_delivery':
        return 'Pay upon delivery';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-bold text-gray-900">Payment Methods</h3>
        <button
          onClick={() => setShowForm(true)}
          className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Payment Method</span>
        </button>
      </div>

      {/* Payment Methods List */}
      <div className="grid gap-4">
        {paymentMethods.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No payment methods added yet</p>
            <p className="text-sm">Add your first payment method to get started</p>
          </div>
        ) : (
          paymentMethods.map((method) => {
            const Icon = getPaymentIcon(method.type);
            return (
              <div key={method.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-3">
                    <div className="bg-gray-100 p-2 rounded-lg">
                      <Icon className="h-6 w-6 text-gray-600" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-semibold text-gray-900">{method.label}</h4>
                        {method.isDefault && (
                          <span className="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded">Default</span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 capitalize">
                        {method.type.replace('_', ' ')}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatPaymentDetails(method)}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(method)}
                      className="p-2 text-gray-600 hover:text-teal-600 hover:bg-teal-50 rounded"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(method.id)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Payment Method Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {editingMethod ? 'Edit Payment Method' : 'Add Payment Method'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Payment Type</label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                    <option value="cash_on_delivery">Cash on Delivery</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Label</label>
                  <input
                    type="text"
                    value={formData.label}
                    onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
                    placeholder="e.g., My Bank Account, Company Check"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    required
                  />
                </div>

                {formData.type === 'bank_transfer' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Bank Name</label>
                      <input
                        type="text"
                        value={formData.bankName}
                        onChange={(e) => setFormData(prev => ({ ...prev, bankName: e.target.value }))}
                        placeholder="e.g., Attijariwafa Bank"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
                      <input
                        type="text"
                        value={formData.accountNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, accountNumber: e.target.value }))}
                        placeholder="Your account number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        required
                      />
                    </div>
                  </>
                )}

                {formData.type === 'check' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Check Details</label>
                    <input
                      type="text"
                      value={formData.checkDetails}
                      onChange={(e) => setFormData(prev => ({ ...prev, checkDetails: e.target.value }))}
                      placeholder="Additional check payment details"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                  </div>
                )}

                {formData.type !== 'cash_on_delivery' && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isDefaultPayment"
                      checked={formData.isDefault}
                      onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                      className="rounded"
                    />
                    <label htmlFor="isDefaultPayment" className="text-sm text-gray-700">
                      Set as default payment method
                    </label>
                  </div>
                )}

                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-6 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
                  >
                    {editingMethod ? 'Update Method' : 'Add Method'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentMethodManagement;
