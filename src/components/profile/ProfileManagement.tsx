
import { useState, useEffect, useRef } from 'react';
import { User, Mail, Phone, MapPin, Building, Save, FileText, CreditCard, Shield, Camera, Upload, Link } from 'lucide-react';
import { updateUser } from '../../services/userManagementService';
import { MOROCCAN_CITIES } from '../../constants/cities';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';
import { useProfilePicture } from '../../hooks/useProfilePicture';
import { ProfileAvatar } from '../ui/UnifiedAvatar';

interface ProfileManagementProps {
  user: any;
  onUserUpdate?: (updatedUser: any) => void;
}

const ProfileManagement = ({ user, onUserUpdate }: ProfileManagementProps) => {
  console.log('ProfileManagement: Component initialized with:', { user: user?.id, onUserUpdate: typeof onUserUpdate });
  const [loading, setLoading] = useState(false);
  const [justSaved, setJustSaved] = useState(false);
  const [showAvatarUrlInput, setShowAvatarUrlInput] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Use unified profile picture hook
  const {
    displayAvatar,
    isLoading: avatarLoading,
    error: avatarError,
    updateProfilePicture,
    uploadProfilePicture
  } = useProfilePicture(user.id, user.full_name || user.fullName || 'User');

  const [formData, setFormData] = useState({
    // Map from user object (snake_case) to form fields (camelCase)
    fullName: user.full_name || user.fullName || '',
    email: user.email || '',
    phone: user.phone || '',
    city: user.city || '',
    company: user.company || '',
    jobTitle: user.job_title || user.jobTitle || '',
    bio: user.bio || '',
    // Company information
    isCompany: user.is_company || user.isCompany || false,
    companyName: user.company_name || user.companyName || '',
    iceNumber: user.ice_number || user.iceNumber || '',
    companyAddress: user.company_address || user.companyAddress || '',
    companyPhone: user.company_phone || user.companyPhone || '',
    companyCity: user.company_city || user.companyCity || '',
    companyEmail: user.company_email || user.companyEmail || '',
    taxId: user.tax_id || user.taxId || '',
    legalForm: user.legal_form || user.legalForm || ''
  });



  // Role-based visibility check
  const isAdminOrManager = user.userType === 'admin' || user.userType === 'manager';
  const shouldHideAccountType = isAdminOrManager;

  // Load fresh user data from database on mount and when user ID changes
  const loadUserData = async () => {
    try {
      console.log('ProfileManagement: Loading fresh user data from database for user:', user.id);
      setLoading(true);

      // Get user data with profile information - include ALL fields that can be updated
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          *,
          user_profiles (
            bio,
            job_title,
            avatar_url
          )
        `)
        .eq('id', user.id)
        .single();

      if (userError) {
        console.error('ProfileManagement: Error loading user data:', userError);
        setLoading(false);
        return;
      }

      console.log('ProfileManagement: Fresh user data loaded:', userData);

      // Update form data with fresh database data
      // Handle both array and object formats for user_profiles
      const profileData = Array.isArray(userData.user_profiles)
        ? userData.user_profiles?.[0] || {}
        : userData.user_profiles || {};

      console.log('ProfileManagement: Profile data extracted:', profileData);

      setFormData({
        // Map from database (snake_case) to form fields (camelCase)
        fullName: userData.full_name || '',
        email: userData.email || '',
        phone: userData.phone || '',
        city: userData.city || '',
        company: userData.company || '',
        jobTitle: profileData.job_title || '',
        bio: profileData.bio || '',
        avatarUrl: profileData.avatar_url || '',
        // Company information - all fields from users table
        isCompany: userData.is_company || false,
        companyName: userData.company_name || '',
        iceNumber: userData.ice_number || '',
        companyAddress: userData.company_address || '',
        companyPhone: userData.company_phone || '',
        companyCity: userData.company_city || '',
        companyEmail: userData.company_email || '',
        taxId: userData.tax_id || '',
        legalForm: userData.legal_form || ''
      });

      setLoading(false);
      console.log('ProfileManagement: Form data updated with fresh database data');

    } catch (error) {
      console.error('ProfileManagement: Error in loadUserData:', error);
      setLoading(false);
    }
  };

  // Load data on mount and when user ID changes
  useEffect(() => {
    if (user.id) {
      loadUserData();
    }
  }, [user.id]);

  // Update form data when user prop changes (fallback for real-time updates)
  useEffect(() => {
    // Don't update form data if we just saved to prevent overwrites
    if (justSaved) {
      console.log('🔍 DEBUG: Skipping user prop update because we just saved');
      return;
    }

    console.log('🔍 DEBUG: User prop changed, updating form data:', user);
    console.log('🔍 DEBUG: User prop job_title:', user.job_title);
    console.log('🔍 DEBUG: User prop jobTitle:', user.jobTitle);
    console.log('🔍 DEBUG: User prop bio:', user.bio);

    setFormData(prevFormData => ({
      // Map from user object (snake_case) to form fields (camelCase)
      fullName: user.full_name || user.fullName || '',
      email: user.email || '',
      phone: user.phone || '',
      city: user.city || '',
      company: user.company || '',
      // Only update jobTitle and bio if they exist in the user prop, otherwise preserve current values
      jobTitle: (user.job_title || user.jobTitle) ? (user.job_title || user.jobTitle) : prevFormData.jobTitle,
      bio: user.bio ? user.bio : prevFormData.bio,
      // Company information
      isCompany: user.is_company || user.isCompany || false,
      companyName: user.company_name || user.companyName || '',
      iceNumber: user.ice_number || user.iceNumber || '',
      companyAddress: user.company_address || user.companyAddress || '',
      companyPhone: user.company_phone || user.companyPhone || '',
      companyCity: user.company_city || user.companyCity || '',
      companyEmail: user.company_email || user.companyEmail || '',
      taxId: user.tax_id || user.taxId || '',
      legalForm: user.legal_form || user.legalForm || ''
    }));

    console.log('🔍 DEBUG: Form data updated from user prop');
  }, [user, justSaved]);

  // Debug: Monitor form data changes
  useEffect(() => {
    console.log('🔍 DEBUG: Form data changed:', {
      jobTitle: formData.jobTitle,
      bio: formData.bio
    });
  }, [formData.jobTitle, formData.bio]);

  // Set up real-time synchronization for profile updates
  useEffect(() => {
    console.log('ProfileManagement: Setting up real-time subscriptions');

    // Subscribe to user updates from liveDataService
    const userSubscription = liveDataService.subscribeToUsers((payload) => {
      console.log('ProfileManagement: Real-time user update:', payload);
      // If this is an update for the current user, refresh the form
      if (payload.new && payload.new.id === user.id) {
        console.log('ProfileManagement: Updating form with real-time data');
        const updatedUser = payload.new;

        // Don't overwrite job_title and bio from real-time updates since they come from user_profiles table
        // Only update the current form data with fields from the users table
        setFormData(prevFormData => ({
          ...prevFormData, // Preserve existing form data including jobTitle and bio
          fullName: updatedUser.full_name || '',
          email: updatedUser.email || '',
          phone: updatedUser.phone || '',
          city: updatedUser.city || '',
          company: updatedUser.company || '',
          // Keep existing jobTitle and bio values
          isCompany: updatedUser.is_company || false,
          companyName: updatedUser.company_name || '',
          iceNumber: updatedUser.ice_number || '',
          companyAddress: updatedUser.company_address || '',
          companyPhone: updatedUser.company_phone || '',
          companyCity: updatedUser.company_city || '',
          companyEmail: updatedUser.company_email || '',
          taxId: updatedUser.tax_id || '',
          legalForm: updatedUser.legal_form || ''
        }));
        // Notify parent component if callback is provided
        if (onUserUpdate) {
          onUserUpdate(updatedUser);
        }
      }
    });

    // Subscribe to profile-specific events
    const profileUpdateSubscription = realTimeService.subscribe('profile-updated', (payload) => {
      console.log('ProfileManagement: Profile update event:', payload);
      if (payload.userId === user.id) {
        // Reload user data if needed
        console.log('ProfileManagement: Profile updated for current user');
      }
    });

    return () => {
      console.log('ProfileManagement: Cleaning up real-time subscriptions');
      userSubscription.unsubscribe();
      profileUpdateSubscription();
    };
  }, [user.id, onUserUpdate]);

  // Avatar upload functions using unified service
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const success = await uploadProfilePicture(file);
      if (success) {
        alert('Avatar updated successfully!');
      } else {
        alert(avatarError || 'Error updating avatar. Please try again.');
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      alert('Error uploading avatar. Please try again.');
    }
  };



  const handleAvatarUrlSubmit = async () => {
    if (!avatarUrl.trim()) {
      alert('Please enter a valid URL');
      return;
    }

    // Basic URL validation
    try {
      new URL(avatarUrl);
    } catch {
      alert('Please enter a valid URL');
      return;
    }

    try {
      // Test if the image URL is accessible
      const img = new Image();
      img.onload = async () => {
        try {
          const success = await updateProfilePicture(avatarUrl);
          if (success) {
            setShowAvatarUrlInput(false);
            setAvatarUrl('');
            alert('Avatar updated successfully!');
          } else {
            alert(avatarError || 'Error updating avatar. Please try again.');
          }
        } catch (error) {
          console.error('Error updating avatar URL:', error);
          alert('Error updating avatar. Please try again.');
        }
      };

      img.onerror = () => {
        alert('Unable to load image from the provided URL. Please check the URL and try again.');
      };

      img.src = avatarUrl;

    } catch (error) {
      console.error('Error updating avatar URL:', error);
      alert('Error updating avatar. Please try again.');
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('ProfileManagement: Updating profile with form data:', formData);

      // Map form data to database field names (camelCase to snake_case)
      const mappedUserData = {
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        city: formData.city,
        company: formData.company,
        job_title: formData.jobTitle,
        bio: formData.bio,
        is_company: formData.isCompany,
        company_name: formData.companyName,
        ice_number: formData.iceNumber,
        company_address: formData.companyAddress,
        company_phone: formData.companyPhone,
        company_city: formData.companyCity,
        company_email: formData.companyEmail,
        tax_id: formData.taxId,
        legal_form: formData.legalForm
      };

      console.log('ProfileManagement: Mapped data for database update:', mappedUserData);

      // Update user through the userManagementService to ensure real-time sync
      const updatedUser = await updateUser(user.id, mappedUserData, user.id);

      console.log('ProfileManagement: Update result:', updatedUser);

      if (updatedUser) {
        console.log('🔍 DEBUG: updatedUser received:', updatedUser);
        console.log('🔍 DEBUG: updatedUser.job_title:', updatedUser.job_title);
        console.log('🔍 DEBUG: updatedUser.bio:', updatedUser.bio);
        console.log('🔍 DEBUG: updatedUser.jobTitle:', updatedUser.jobTitle);

        // Update the local form data to reflect the saved changes
        const newFormData = {
          fullName: updatedUser.full_name || '',
          email: updatedUser.email || '',
          phone: updatedUser.phone || '',
          city: updatedUser.city || '',
          company: updatedUser.company || '',
          jobTitle: updatedUser.job_title || updatedUser.jobTitle || '',
          bio: updatedUser.bio || '',
          isCompany: updatedUser.is_company || false,
          companyName: updatedUser.company_name || '',
          iceNumber: updatedUser.ice_number || '',
          companyAddress: updatedUser.company_address || '',
          companyPhone: updatedUser.company_phone || '',
          companyCity: updatedUser.company_city || '',
          companyEmail: updatedUser.company_email || '',
          taxId: updatedUser.tax_id || '',
          legalForm: updatedUser.legal_form || ''
        };

        console.log('🔍 DEBUG: Setting form data with:', {
          jobTitle: newFormData.jobTitle,
          bio: newFormData.bio
        });

        setFormData(newFormData);

        // Set flag to prevent overwrites and clear it after a delay
        setJustSaved(true);
        setTimeout(() => setJustSaved(false), 2000); // Clear flag after 2 seconds

        // Notify parent component of the update if callback is provided
        console.log('ProfileManagement: About to call onUserUpdate:', {
          onUserUpdate: typeof onUserUpdate,
          updatedUser: updatedUser?.id
        });

        if (onUserUpdate && typeof onUserUpdate === 'function') {
          onUserUpdate(updatedUser);
        } else {
          console.warn('ProfileManagement: onUserUpdate is not a function:', typeof onUserUpdate);
        }

        // Broadcast real-time events for system-wide synchronization
        realTimeService.emit('profile-updated', {
          userId: user.id,
          updatedData: updatedUser,
          timestamp: new Date().toISOString()
        });

        realTimeService.emit('user-profile-changed', {
          userId: user.id,
          fullName: updatedUser.full_name,
          email: updatedUser.email,
          phone: updatedUser.phone,
          city: updatedUser.city,
          company: updatedUser.company_name || updatedUser.company,
          jobTitle: updatedUser.job_title,
          bio: updatedUser.bio,
          isCompany: updatedUser.is_company,
          companyName: updatedUser.company_name,
          timestamp: new Date().toISOString()
        });

        console.log('ProfileManagement: Profile updated successfully with real-time sync');

        alert('Profile updated successfully! Changes have been saved to the database.');
      } else {
        throw new Error('Update operation returned null or undefined');
      }
    } catch (error) {
      console.error('ProfileManagement: Error updating profile:', error);
      console.error('ProfileManagement: Form data that caused error:', formData);
      console.error('ProfileManagement: Mapped data that caused error:', mappedUserData);

      let errorMessage = 'Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
        if (error.message.includes('column') && error.message.includes('does not exist')) {
          errorMessage = 'Some profile fields are not supported yet. Basic information has been saved.';
        }
      }

      alert(`Error updating profile: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  const moroccanCities = MOROCCAN_CITIES;

  const legalForms = [
    'SARL', 'SA', 'SAS', 'SNC', 'SCS', 'Auto-entrepreneur', 'Entreprise individuelle'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h3>
        <p className="text-gray-600 dark:text-gray-300">Manage your personal and company information</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Account Type Selection - Hidden for Admin/Manager roles */}
        {!shouldHideAccountType && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Account Type
            </h4>
            <div className="flex space-x-6">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={!formData.isCompany}
                  onChange={() => handleInputChange('isCompany', false)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Individual</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={formData.isCompany}
                  onChange={() => handleInputChange('isCompany', true)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Company</span>
              </label>
            </div>
          </div>
        )}

        {/* Security Notice for Admin/Manager */}
        {shouldHideAccountType && (
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-amber-600 mr-2" />
              <div>
                <h4 className="text-sm font-semibold text-amber-800">Account Type Restriction</h4>
                <p className="text-sm text-amber-700">
                  As an {user.userType}, you cannot modify your account type for security reasons.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Personal Information - Only shown for Individual accounts */}
        {!formData.isCompany && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <User className="h-5 w-5 text-teal-600 mr-2" />
              Personal Information
            </h4>

            {/* Avatar Section */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Profile Picture
              </label>

              <div className="flex items-center space-x-4">
                {/* Avatar Display using Unified Component */}
                <ProfileAvatar
                  userId={user.id}
                  fullName={formData.fullName}
                  size="2xl"
                  editable={false}
                  className="relative"
                >
                  {(avatarLoading || avatarError) && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                      {avatarLoading ? (
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                      ) : (
                        <div className="text-white text-xs">Error</div>
                      )}
                    </div>
                  )}
                </ProfileAvatar>

                {/* Upload Options */}
                <div className="flex flex-col space-y-2">
                  {/* URL Input Button - Primary option */}
                  <button
                    type="button"
                    onClick={() => setShowAvatarUrlInput(!showAvatarUrlInput)}
                    disabled={avatarLoading}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                  >
                    <Link className="h-4 w-4 mr-2" />
                    Use Image URL
                  </button>

                  {/* File Upload Button - Secondary option */}
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={avatarLoading}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {avatarLoading ? 'Uploading...' : 'Upload File'}
                  </button>
                </div>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />

              {/* URL Input Field */}
              {showAvatarUrlInput && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Image URL
                  </label>
                  <div className="space-y-3">
                    <div className="flex space-x-2">
                      <input
                        type="url"
                        value={avatarUrl}
                        onChange={(e) => setAvatarUrl(e.target.value)}
                        placeholder="https://example.com/image.jpg"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                      />
                      <button
                        type="button"
                        onClick={handleAvatarUrlSubmit}
                        disabled={avatarLoading || !avatarUrl.trim()}
                        className="px-4 py-2 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-md hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {avatarLoading ? 'Saving...' : 'Save'}
                      </button>
                    </div>

                    {/* URL Preview */}
                    {avatarUrl && avatarUrl !== formData.avatarUrl && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                          <img
                            src={avatarUrl}
                            alt="Preview"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling!.style.display = 'flex';
                            }}
                          />
                          <div className="w-full h-full bg-red-100 text-red-600 text-xs flex items-center justify-center hidden">
                            ✗
                          </div>
                        </div>
                        <span>Preview</span>
                      </div>
                    )}

                    <p className="text-xs text-gray-500">
                      Enter a direct link to an image (JPG, PNG, GIF, WebP)
                    </p>
                  </div>
                </div>
              )}
            </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Full Name
              </label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Job Title
              </label>
              <input
                type="text"
                value={formData.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Bio
            </label>
            <textarea
              rows={3}
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Brief description about yourself..."
            />
          </div>
        </div>
        )}

        {/* Company Information (only shown if isCompany is true) */}
        {formData.isCompany && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Company Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required={formData.isCompany}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ICE Number
                </label>
                <input
                  type="text"
                  value={formData.iceNumber}
                  onChange={(e) => handleInputChange('iceNumber', e.target.value)}
                  placeholder="000000000000000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tax ID
                </label>
                <input
                  type="text"
                  value={formData.taxId}
                  onChange={(e) => handleInputChange('taxId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Legal Form
                </label>
                <select
                  value={formData.legalForm}
                  onChange={(e) => handleInputChange('legalForm', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select legal form</option>
                  {legalForms.map(form => (
                    <option key={form} value={form}>{form}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Address
                </label>
                <textarea
                  rows={2}
                  value={formData.companyAddress}
                  onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Company address..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 text-teal-600 mr-2" />
            Contact Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+212 6 XX XX XX XX"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City
              </label>
              <select
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select city</option>
                {moroccanCities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
            {!formData.isCompany && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company (Optional)
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            )}
            {formData.isCompany && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.companyPhone}
                    onChange={(e) => handleInputChange('companyPhone', e.target.value)}
                    placeholder="+212 5 XX XX XX XX"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Email
                  </label>
                  <input
                    type="email"
                    value={formData.companyEmail}
                    onChange={(e) => handleInputChange('companyEmail', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company City
                  </label>
                  <select
                    value={formData.companyCity}
                    onChange={(e) => handleInputChange('companyCity', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select company city</option>
                    {moroccanCities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="bg-teal-600 text-white py-2 px-6 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{loading ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </form>




    </div>
  );
};

export default ProfileManagement;
