/**
 * Security Middleware for YalaOffice
 * Provides comprehensive security validation for critical operations
 */

import { AuthorizationService, Permission } from '../services/authorizationService';
import { InputSanitizer } from '../services/secureQueryService';

/**
 * Security validation result
 */
export interface SecurityValidationResult {
  success: boolean;
  error?: string;
  user?: any;
}

/**
 * Security middleware for protecting critical operations
 */
export class SecurityMiddleware {
  /**
   * Validate user authentication and authorization
   */
  static async validateAccess(
    requiredPermission: Permission,
    resourceId?: string,
    resourceType?: string
  ): Promise<SecurityValidationResult> {
    try {
      // Check basic permission
      const authResult = await AuthorizationService.hasPermission(requiredPermission);
      
      if (!authResult.authorized) {
        return {
          success: false,
          error: authResult.reason || 'Access denied',
        };
      }
      
      // Check resource-specific access if provided
      if (resourceId && resourceType) {
        const resourceResult = await AuthorizationService.canAccessResource(
          resourceType,
          resourceId,
          this.getActionFromPermission(requiredPermission)
        );
        
        if (!resourceResult.authorized) {
          return {
            success: false,
            error: resourceResult.reason || 'Resource access denied',
          };
        }
      }
      
      return {
        success: true,
        user: authResult.user,
      };
    } catch (error) {
      console.error('Security validation error:', error);
      return {
        success: false,
        error: 'Security validation failed',
      };
    }
  }
  
  /**
   * Validate and sanitize input data
   */
  static validateInput(data: Record<string, any>, rules: Record<string, any>): {
    valid: boolean;
    sanitized: Record<string, any>;
    errors: string[];
  } {
    const errors: string[] = [];
    const sanitized: Record<string, any> = {};
    
    Object.entries(rules).forEach(([field, rule]) => {
      const value = data[field];
      
      // Check required fields
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field} is required`);
        return;
      }
      
      // Skip validation for optional empty fields
      if (!rule.required && (value === undefined || value === null || value === '')) {
        return;
      }
      
      // Type-specific validation and sanitization
      switch (rule.type) {
        case 'string':
          sanitized[field] = InputSanitizer.sanitizeSearchInput(String(value));
          if (rule.minLength && sanitized[field].length < rule.minLength) {
            errors.push(`${field} must be at least ${rule.minLength} characters`);
          }
          if (rule.maxLength && sanitized[field].length > rule.maxLength) {
            errors.push(`${field} must be no more than ${rule.maxLength} characters`);
          }
          break;
          
        case 'email':
          sanitized[field] = InputSanitizer.sanitizeEmail(String(value));
          if (sanitized[field] && !this.isValidEmail(sanitized[field])) {
            errors.push(`${field} must be a valid email address`);
          }
          break;
          
        case 'number':
          sanitized[field] = InputSanitizer.sanitizeNumber(value);
          if (rule.min !== undefined && sanitized[field] < rule.min) {
            errors.push(`${field} must be at least ${rule.min}`);
          }
          if (rule.max !== undefined && sanitized[field] > rule.max) {
            errors.push(`${field} must be no more than ${rule.max}`);
          }
          break;
          
        case 'boolean':
          sanitized[field] = InputSanitizer.sanitizeBoolean(value);
          break;
          
        case 'uuid':
          sanitized[field] = InputSanitizer.sanitizeUUID(String(value));
          if (sanitized[field] === '' && rule.required) {
            errors.push(`${field} must be a valid UUID`);
          }
          break;
          
        case 'array':
          sanitized[field] = InputSanitizer.sanitizeArray(value);
          if (rule.minItems && sanitized[field].length < rule.minItems) {
            errors.push(`${field} must have at least ${rule.minItems} items`);
          }
          if (rule.maxItems && sanitized[field].length > rule.maxItems) {
            errors.push(`${field} must have no more than ${rule.maxItems} items`);
          }
          break;
          
        default:
          sanitized[field] = value;
      }
      
      // Custom validation function
      if (rule.validate && typeof rule.validate === 'function') {
        const customResult = rule.validate(sanitized[field]);
        if (customResult !== true) {
          errors.push(customResult || `${field} is invalid`);
        }
      }
    });
    
    return {
      valid: errors.length === 0,
      sanitized,
      errors,
    };
  }
  
  /**
   * Rate limiting check
   */
  static async checkRateLimit(
    userId: string,
    action: string,
    maxAttempts: number = 10,
    windowMinutes: number = 15
  ): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    try {
      // This would typically use Redis or a similar cache
      // For now, we'll use a simple in-memory implementation
      const key = `rate_limit:${userId}:${action}`;
      const now = new Date();
      const windowStart = new Date(now.getTime() - windowMinutes * 60 * 1000);
      
      // In a real implementation, you would:
      // 1. Get attempts from cache/database
      // 2. Filter attempts within the time window
      // 3. Check if under the limit
      // 4. Update the cache
      
      // For now, return allowed (implement proper rate limiting in production)
      return {
        allowed: true,
        remaining: maxAttempts - 1,
        resetTime: new Date(now.getTime() + windowMinutes * 60 * 1000),
      };
    } catch (error) {
      console.error('Rate limit check error:', error);
      // Fail open for now (allow the request)
      return {
        allowed: true,
        remaining: 0,
        resetTime: new Date(),
      };
    }
  }
  
  /**
   * Log security event
   */
  static async logSecurityEvent(
    eventType: string,
    userId: string,
    details: Record<string, any>,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    try {
      const { AdvancedSecurityService } = await import('../services/securityService');
      
      await AdvancedSecurityService.logSecurityEvent({
        eventType: eventType as any,
        userId,
        userEmail: details.userEmail || 'unknown',
        ipAddress: details.ipAddress || 'unknown',
        userAgent: details.userAgent || 'unknown',
        details,
        severity,
        resolved: false,
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }
  
  /**
   * Comprehensive security check for critical operations
   */
  static async performSecurityCheck(options: {
    permission: Permission;
    userId?: string;
    resourceId?: string;
    resourceType?: string;
    inputData?: Record<string, any>;
    inputRules?: Record<string, any>;
    rateLimit?: { maxAttempts: number; windowMinutes: number };
    logEvent?: { type: string; details: Record<string, any> };
  }): Promise<{
    success: boolean;
    error?: string;
    sanitizedData?: Record<string, any>;
    user?: any;
  }> {
    try {
      // 1. Validate access
      const accessResult = await this.validateAccess(
        options.permission,
        options.resourceId,
        options.resourceType
      );
      
      if (!accessResult.success) {
        // Log failed access attempt
        if (options.logEvent && options.userId) {
          await this.logSecurityEvent(
            'access_denied',
            options.userId,
            {
              ...options.logEvent.details,
              permission: options.permission,
              reason: accessResult.error,
            },
            'high'
          );
        }
        
        return {
          success: false,
          error: accessResult.error,
        };
      }
      
      // 2. Rate limiting check
      if (options.rateLimit && options.userId) {
        const rateLimitResult = await this.checkRateLimit(
          options.userId,
          options.permission,
          options.rateLimit.maxAttempts,
          options.rateLimit.windowMinutes
        );
        
        if (!rateLimitResult.allowed) {
          await this.logSecurityEvent(
            'rate_limit_exceeded',
            options.userId,
            {
              permission: options.permission,
              remaining: rateLimitResult.remaining,
              resetTime: rateLimitResult.resetTime,
            },
            'high'
          );
          
          return {
            success: false,
            error: 'Rate limit exceeded. Please try again later.',
          };
        }
      }
      
      // 3. Input validation
      let sanitizedData: Record<string, any> | undefined;
      if (options.inputData && options.inputRules) {
        const validationResult = this.validateInput(options.inputData, options.inputRules);
        
        if (!validationResult.valid) {
          return {
            success: false,
            error: `Input validation failed: ${validationResult.errors.join(', ')}`,
          };
        }
        
        sanitizedData = validationResult.sanitized;
      }
      
      // 4. Log successful security check
      if (options.logEvent && options.userId) {
        await this.logSecurityEvent(
          options.logEvent.type,
          options.userId,
          {
            ...options.logEvent.details,
            permission: options.permission,
            success: true,
          },
          'low'
        );
      }
      
      return {
        success: true,
        sanitizedData,
        user: accessResult.user,
      };
    } catch (error) {
      console.error('Security check error:', error);
      return {
        success: false,
        error: 'Security check failed',
      };
    }
  }
  
  /**
   * Helper methods
   */
  private static getActionFromPermission(permission: Permission): 'read' | 'write' | 'delete' {
    if (permission.includes('.delete')) return 'delete';
    if (permission.includes('.update') || permission.includes('.create')) return 'write';
    return 'read';
  }
  
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
