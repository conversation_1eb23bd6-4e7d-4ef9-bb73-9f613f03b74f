-- Fix user registration trigger to work with proper permissions
-- This migration fixes the handle_new_user_registration function to run with SECURITY DEFINER

-- Drop existing function and trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user_registration();

-- Create the function with <PERSON><PERSON>ITY DEFINER to bypass RLS
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS TRIGGER 
SECURITY DEFINER -- This allows the function to bypass RLS policies
SET search_path = public
AS $$
BEGIN
    -- This function will be called when a user confirms their email
    -- It ensures the user data is properly inserted into the users table
    
    -- Log the trigger execution for debugging
    RAISE LOG 'handle_new_user_registration triggered for user: %', NEW.id;
    
    -- Check if user already exists in users table
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = NEW.id) THEN
        -- Insert user data from auth metadata
        INSERT INTO users (
            id,
            email,
            full_name,
            user_type,
            phone,
            city,
            is_active
        ) VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
            COALESCE(NEW.raw_user_meta_data->>'user_type', 'client'),
            NEW.raw_user_meta_data->>'phone',
            COALESCE(NEW.raw_user_meta_data->>'city', 'Tetouan'),
            true
        );
        
        RAISE LOG 'User profile created for: %', NEW.email;
        
        -- Create customer profile for client/reseller users
        IF COALESCE(NEW.raw_user_meta_data->>'user_type', 'client') IN ('client', 'reseller') THEN
            INSERT INTO customer_profiles (
                user_id,
                discount_rate,
                credit_limit,
                status
            ) VALUES (
                NEW.id,
                CASE 
                    WHEN COALESCE(NEW.raw_user_meta_data->>'user_type', 'client') = 'reseller' THEN 5.0 
                    ELSE 0.0 
                END,
                0,
                'active'
            );
            
            RAISE LOG 'Customer profile created for: %', NEW.email;
        END IF;
    ELSE
        RAISE LOG 'User already exists in users table: %', NEW.id;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log any errors but don't fail the auth process
        RAISE LOG 'Error in handle_new_user_registration for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new user registration
-- This trigger fires when a user confirms their email
CREATE TRIGGER on_auth_user_created
    AFTER UPDATE OF email_confirmed_at ON auth.users
    FOR EACH ROW
    WHEN (OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION handle_new_user_registration();

-- Grant necessary permissions for the function to work
GRANT USAGE ON SCHEMA auth TO postgres;
GRANT SELECT ON auth.users TO postgres;

-- Add comment for documentation
COMMENT ON FUNCTION handle_new_user_registration() IS 'Automatically creates user profile and customer profile when user confirms email - runs with SECURITY DEFINER';

-- Verification query
SELECT 'User registration trigger fixed successfully!' as status;
