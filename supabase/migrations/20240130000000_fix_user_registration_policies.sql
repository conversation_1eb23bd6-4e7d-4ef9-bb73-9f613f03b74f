-- Fix user registration and authentication policies
-- This migration addresses critical issues with user registration and email confirmation

-- Enable RLS on users table if not already enabled
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them properly
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;
DROP POLICY IF EXISTS "Allow user registration" ON users;
DROP POLICY IF EXISTS "Only admins can delete users" ON users;

-- Policy 1: Users can view their own profile
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (id = auth.uid());

-- Policy 2: Admins and managers can view all users
CREATE POLICY "Ad<PERSON> can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager')
        )
    );

-- Policy 3: Allow new user registration (INSERT)
-- This is critical for user registration to work
CREATE POLICY "Allow user registration" ON users
    FOR INSERT WITH CHECK (
        -- Allow insert if the user ID matches the authenticated user
        -- This happens during registration when Supabase auth creates the user
        id = auth.uid()
    );

-- Policy 4: Users can update their own profile
CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid())
    WITH CHECK (id = auth.uid());

-- Policy 5: Only admins can delete users
CREATE POLICY "Only admins can delete users" ON users
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type = 'admin'
        )
    );

-- Enable RLS on user_profiles table if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view their own user profile" ON user_profiles;
        DROP POLICY IF EXISTS "Users can insert their own user profile" ON user_profiles;
        DROP POLICY IF EXISTS "Users can update their own user profile" ON user_profiles;
        
        -- Create policies for user_profiles
        CREATE POLICY "Users can view their own user profile" ON user_profiles
            FOR SELECT USING (user_id = auth.uid());
            
        CREATE POLICY "Users can insert their own user profile" ON user_profiles
            FOR INSERT WITH CHECK (user_id = auth.uid());
            
        CREATE POLICY "Users can update their own user profile" ON user_profiles
            FOR UPDATE USING (user_id = auth.uid())
            WITH CHECK (user_id = auth.uid());
    END IF;
END $$;

-- Enable RLS on customer_profiles table if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'customer_profiles') THEN
        ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view their own customer profile" ON customer_profiles;
        DROP POLICY IF EXISTS "Users can insert their own customer profile" ON customer_profiles;
        DROP POLICY IF EXISTS "Users can update their own customer profile" ON customer_profiles;
        DROP POLICY IF EXISTS "Admins can view all customer profiles" ON customer_profiles;
        
        -- Create policies for customer_profiles
        CREATE POLICY "Users can view their own customer profile" ON customer_profiles
            FOR SELECT USING (user_id = auth.uid());
            
        CREATE POLICY "Users can insert their own customer profile" ON customer_profiles
            FOR INSERT WITH CHECK (user_id = auth.uid());
            
        CREATE POLICY "Users can update their own customer profile" ON customer_profiles
            FOR UPDATE USING (user_id = auth.uid())
            WITH CHECK (user_id = auth.uid());
            
        CREATE POLICY "Admins can view all customer profiles" ON customer_profiles
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM users 
                    WHERE id = auth.uid() 
                    AND user_type IN ('admin', 'manager')
                )
            );
    END IF;
END $$;

-- Create a function to handle user registration after email confirmation
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS TRIGGER AS $$
BEGIN
    -- This function will be called when a user confirms their email
    -- It ensures the user data is properly inserted into the users table
    
    -- Check if user already exists in users table
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = NEW.id) THEN
        -- Insert user data from auth metadata
        INSERT INTO users (
            id,
            email,
            full_name,
            user_type,
            phone,
            city,
            is_active
        ) VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
            COALESCE(NEW.raw_user_meta_data->>'user_type', 'client'),
            NEW.raw_user_meta_data->>'phone',
            COALESCE(NEW.raw_user_meta_data->>'city', 'Tetouan'),
            true
        );
        
        -- Create customer profile for client and reseller users
        IF COALESCE(NEW.raw_user_meta_data->>'user_type', 'client') IN ('client', 'reseller') THEN
            INSERT INTO customer_profiles (
                user_id,
                discount_rate,
                credit_limit,
                status
            ) VALUES (
                NEW.id,
                CASE WHEN COALESCE(NEW.raw_user_meta_data->>'user_type', 'client') = 'reseller' THEN 5.0 ELSE 0.0 END,
                0,
                'active'
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
-- This trigger fires when a user confirms their email
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER UPDATE OF email_confirmed_at ON auth.users
    FOR EACH ROW
    WHEN (OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION handle_new_user_registration();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT SELECT ON auth.users TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Add comment for documentation
COMMENT ON FUNCTION handle_new_user_registration() IS 'Automatically creates user profile and customer profile when user confirms email';
