// Supabase Edge Function for sending emails via SMTP
// Deploy this to Supabase Edge Functions for email sending capability

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface EmailRequest {
  to: string;
  from: string;
  subject: string;
  html: string;
  text: string;
  smtp_config: {
    host: string;
    port: number;
    username: string;
    password: string;
    secure: boolean;
  };
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
      },
    })
  }

  try {
    const { to, from, subject, html, text, smtp_config }: EmailRequest = await req.json()

    console.log('📧 Sending email via Edge Function:', {
      to,
      from,
      subject,
      smtp_host: smtp_config.host
    })

    // Validate required fields
    if (!to || !from || !subject || !smtp_config) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // In a real implementation, you would use a library like nodemailer
    // For now, we'll simulate the email sending and log the attempt
    
    // Simulate email sending process
    const emailResult = await sendEmailViaSMTP({
      to,
      from,
      subject,
      html,
      text,
      smtp_config
    })

    if (emailResult.success) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Email sent successfully',
          messageId: emailResult.messageId 
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: emailResult.error 
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Error in send-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
})

// Simulated SMTP sending function
// In production, replace this with actual SMTP implementation
async function sendEmailViaSMTP(emailData: EmailRequest): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    console.log('🔄 Attempting to send email via SMTP...')
    
    // Simulate SMTP connection and sending
    // In a real implementation, you would:
    // 1. Create SMTP transport with nodemailer or similar
    // 2. Connect to SMTP server
    // 3. Send the email
    // 4. Return the result
    
    // For now, simulate a successful send
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    console.log('✅ Email sent successfully (simulated)', {
      messageId,
      to: emailData.to,
      subject: emailData.subject
    })
    
    return {
      success: true,
      messageId
    }
    
  } catch (error) {
    console.error('❌ SMTP sending failed:', error)
    
    return {
      success: false,
      error: error.message || 'SMTP sending failed'
    }
  }
}

/* 
DEPLOYMENT INSTRUCTIONS:

1. Install Supabase CLI:
   npm install -g supabase

2. Login to Supabase:
   supabase login

3. Link to your project:
   supabase link --project-ref YOUR_PROJECT_REF

4. Deploy the function:
   supabase functions deploy send-email

5. Set environment variables if needed:
   supabase secrets set SMTP_HOST=your-smtp-host
   supabase secrets set SMTP_USERNAME=your-username
   supabase secrets set SMTP_PASSWORD=your-password

6. Test the function:
   curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-email' \
     -H 'Authorization: Bearer YOUR_ANON_KEY' \
     -H 'Content-Type: application/json' \
     -d '{
       "to": "<EMAIL>",
       "from": "<EMAIL>",
       "subject": "Test Email",
       "html": "<h1>Test</h1>",
       "text": "Test",
       "smtp_config": {
         "host": "smtp.gmail.com",
         "port": 587,
         "username": "<EMAIL>",
         "password": "your-app-password",
         "secure": true
       }
     }'

PRODUCTION IMPLEMENTATION:

To make this work in production, you need to:

1. Add nodemailer or similar SMTP library:
   - Create a deno.json file with dependencies
   - Import nodemailer for Deno
   - Implement actual SMTP sending

2. Handle authentication and security:
   - Validate API keys
   - Rate limiting
   - Input sanitization

3. Add error handling and retries:
   - Retry failed sends
   - Queue management
   - Logging and monitoring

4. Configure SMTP providers:
   - Gmail (with app passwords)
   - SendGrid
   - Mailgun
   - Amazon SES
   - Or any other SMTP provider
*/
