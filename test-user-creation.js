// Test script to verify admin user creation with email confirmation bypass
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://umzikqwughlzkiarldoa.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testAdminUserCreation() {
  try {
    console.log('Testing admin user creation with email confirmation bypass...');
    
    // Test creating a user with email confirmation bypassed
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testadmin123',
      email_confirm: true, // Bypass email confirmation
      user_metadata: {
        full_name: 'Test Admin 2',
        user_type: 'admin',
        phone: '+212 6 12 34 56 79',
        city: 'Casablanca',
        created_by: 'system',
        admin_created: true
      }
    });

    if (authError) {
      console.error('Auth error:', authError);
      return;
    }

    console.log('✅ User created in auth with email confirmed:', authData.user.email_confirmed_at);
    console.log('User ID:', authData.user.id);
    
    // Now create the user record in our database
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: '<EMAIL>',
        full_name: 'Test Admin 2',
        user_type: 'admin',
        phone: '+212 6 12 34 56 79',
        city: 'Casablanca',
        is_active: true,
        is_company: false
      })
      .select()
      .single();

    if (userError) {
      console.error('Database error:', userError);
      return;
    }

    console.log('✅ User record created in database:', userRecord);
    
    // Test login with the new user
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testadmin123'
    });

    if (loginError) {
      console.error('Login error:', loginError);
      return;
    }

    console.log('✅ Login successful! Email confirmation was bypassed.');
    console.log('Logged in user:', loginData.user.email);
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAdminUserCreation();
