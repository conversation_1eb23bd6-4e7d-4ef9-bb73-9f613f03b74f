#!/usr/bin/env node

/**
 * Debug RPC function to identify exact mismatch
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function debugRPCFunction() {
  console.log('🔍 Debugging RPC Function...\n');
  
  try {
    // 1. Check if function exists
    console.log('1️⃣ Checking if function exists...');
    const { data: functionData, error: functionError } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_type')
      .eq('routine_name', 'get_all_users_admin')
      .eq('routine_schema', 'public');
    
    if (functionError) {
      console.log('⚠️  Could not check function existence:', functionError.message);
    } else if (functionData && functionData.length > 0) {
      console.log('✅ Function exists:', functionData[0]);
    } else {
      console.log('❌ Function does not exist!');
      return;
    }
    
    // 2. Get function definition
    console.log('\n2️⃣ Getting function definition...');
    const { data: defData, error: defError } = await supabase
      .from('information_schema.parameters')
      .select('parameter_name, data_type, parameter_mode')
      .eq('specific_name', 'get_all_users_admin')
      .order('ordinal_position');
    
    if (defError) {
      console.log('⚠️  Could not get function definition:', defError.message);
    } else {
      console.log('📋 Function parameters:');
      defData.forEach(param => {
        console.log(`   ${param.parameter_name}: ${param.data_type} (${param.parameter_mode})`);
      });
    }
    
    // 3. Try a simple direct query to compare
    console.log('\n3️⃣ Testing direct query with exact columns...');
    const { data: directData, error: directError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        user_type,
        phone,
        city,
        is_active,
        is_company,
        company_name,
        ice_number,
        company_address,
        company_phone,
        company_city,
        company_email,
        tax_id,
        legal_form,
        created_at,
        updated_at,
        last_login,
        profile_picture_url
      `)
      .limit(1);
    
    if (directError) {
      console.log('❌ Direct query failed:', directError.message);
    } else {
      console.log('✅ Direct query succeeded with', directData?.length || 0, 'users');
      if (directData && directData.length > 0) {
        console.log('📊 Sample data structure:');
        const sample = directData[0];
        Object.keys(sample).forEach(key => {
          const value = sample[key];
          console.log(`   ${key}: ${typeof value} = ${value === null ? 'NULL' : JSON.stringify(value).substring(0, 50)}`);
        });
      }
    }
    
    // 4. Try to call the RPC with detailed error info
    console.log('\n4️⃣ Attempting RPC call with detailed error...');
    try {
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_all_users_admin');
      
      if (rpcError) {
        console.log('❌ RPC Error Details:');
        console.log('   Message:', rpcError.message);
        console.log('   Code:', rpcError.code);
        console.log('   Details:', rpcError.details);
        console.log('   Hint:', rpcError.hint);
      } else {
        console.log('✅ RPC succeeded with', rpcData?.length || 0, 'users');
      }
    } catch (err) {
      console.log('❌ RPC Exception:', err.message);
    }
    
    // 5. Check if we can drop and recreate the function
    console.log('\n5️⃣ Suggested fix:');
    console.log('The function structure doesn\'t match the database. Try this SQL:');
    console.log('');
    console.log('-- Drop the function completely');
    console.log('DROP FUNCTION IF EXISTS get_all_users_admin() CASCADE;');
    console.log('');
    console.log('-- Create a simple version that works');
    console.log('CREATE OR REPLACE FUNCTION get_all_users_admin()');
    console.log('RETURNS SETOF users AS $$');
    console.log('BEGIN');
    console.log('    -- Simple approach: return the users table directly');
    console.log('    RETURN QUERY SELECT * FROM users ORDER BY created_at DESC;');
    console.log('END;');
    console.log('$$ LANGUAGE plpgsql SECURITY DEFINER;');
    console.log('');
    console.log('GRANT EXECUTE ON FUNCTION get_all_users_admin() TO authenticated;');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugRPCFunction();
