#!/usr/bin/env node

/**
 * Apply SQL migration to Supabase database
 * This script applies the missing RPC functions migration
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyMigration() {
  try {
    console.log('🚀 Starting migration application...');
    
    // Read the migration file
    const migrationPath = join(projectRoot, 'supabase/migrations/20240131000000_add_missing_rpc_functions.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded successfully');
    console.log('📝 Applying migration to database...');
    
    // Execute the migration SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      // If exec_sql doesn't exist, try direct execution
      console.log('⚠️  exec_sql RPC not available, trying direct execution...');
      
      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`Executing: ${statement.substring(0, 50)}...`);
          
          const { error: execError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0); // This will fail but we can use it to execute SQL
          
          // Alternative approach: use a simple query to test connection
          const { error: testError } = await supabase
            .from('users')
            .select('count')
            .limit(1);
          
          if (testError && !testError.message.includes('relation "_temp_migration" does not exist')) {
            console.error('❌ Database connection error:', testError);
            throw testError;
          }
        }
      }
      
      console.log('⚠️  Direct SQL execution not supported via client. Please apply migration manually.');
      console.log('📋 Migration SQL content:');
      console.log('=' .repeat(80));
      console.log(migrationSQL);
      console.log('=' .repeat(80));
      console.log('');
      console.log('🔧 To apply manually:');
      console.log('1. Go to your Supabase dashboard');
      console.log('2. Navigate to SQL Editor');
      console.log('3. Copy and paste the above SQL');
      console.log('4. Execute the query');
      
      return;
    }
    
    console.log('✅ Migration applied successfully!');
    console.log('📊 Result:', data);
    
    // Test the new RPC function
    console.log('🧪 Testing new RPC function...');
    const { data: testData, error: testError } = await supabase.rpc('get_all_users_admin');
    
    if (testError) {
      console.log('⚠️  RPC function test failed (this might be expected if not logged in as admin):', testError.message);
    } else {
      console.log('✅ RPC function test successful! Found', testData?.length || 0, 'users');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
applyMigration();
