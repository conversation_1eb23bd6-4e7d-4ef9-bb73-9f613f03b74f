-- Setup Supabase Storage Bucket for Profile Pictures
-- Creates the avatars bucket and sets up proper policies

-- Step 1: Create the avatars bucket (if it doesn't exist)
-- Note: This SQL creates the bucket record, but you may need to create it via Supabase Dashboard
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars', 
    true,
    2097152, -- 2MB limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
    public = true,
    file_size_limit = 2097152,
    allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- Step 2: Set up RLS policies for the avatars bucket
-- Policy 1: Allow authenticated users to upload their own avatars
CREATE POLICY "Users can upload their own avatars" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy 2: Allow public read access to all avatars
CREATE POLICY "Public read access for avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

-- Policy 3: Allow users to update their own avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy 4: Allow users to delete their own avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Step 3: Verification queries
SELECT 'STORAGE BUCKET SETUP VERIFICATION' as status;

-- Check if bucket exists
SELECT 
    'Bucket Check' as check_type,
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'avatars';

-- Check storage policies
SELECT 
    'Storage Policies Check' as check_type,
    policyname,
    cmd,
    SUBSTRING(qual, 1, 100) as policy_condition
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%avatar%'
ORDER BY policyname;

-- Instructions for manual setup if SQL doesn't work
SELECT 'MANUAL SETUP INSTRUCTIONS' as guide;
SELECT 'If the SQL above fails, follow these manual steps:' as step_0
UNION ALL
SELECT '1. Go to Supabase Dashboard → Storage' as step_1
UNION ALL
SELECT '2. Click "Create Bucket"' as step_2
UNION ALL
SELECT '3. Name: avatars' as step_3
UNION ALL
SELECT '4. Set as Public bucket' as step_4
UNION ALL
SELECT '5. File size limit: 2MB' as step_5
UNION ALL
SELECT '6. Allowed MIME types: image/jpeg, image/png, image/webp' as step_6;
