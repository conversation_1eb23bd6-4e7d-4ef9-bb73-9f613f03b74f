-- Create Email Queue Table for Manual Processing
-- Stores emails when automated sending fails

-- Create email queue table
CREATE TABLE IF NOT EXISTS email_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    to_email VARCHAR(255) NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    from_name VA<PERSON>HA<PERSON>(255),
    subject TEXT NOT NULL,
    html_content TEXT,
    text_content TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
    smtp_config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    last_error TEXT,
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_created_at ON email_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_email_queue_priority ON email_queue(priority);
CREATE INDEX IF NOT EXISTS idx_email_queue_to_email ON email_queue(to_email);

-- Add RLS policies
ALTER TABLE email_queue ENABLE ROW LEVEL SECURITY;

-- Policy: Only admins can view email queue
CREATE POLICY "Admins can view email queue" ON email_queue
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager')
        )
    );

-- Policy: System can insert emails
CREATE POLICY "System can insert emails" ON email_queue
    FOR INSERT WITH CHECK (true);

-- Policy: Admins can update email status
CREATE POLICY "Admins can update email queue" ON email_queue
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager')
        )
    );

-- Function to retry failed emails
CREATE OR REPLACE FUNCTION retry_failed_emails()
RETURNS void AS $$
BEGIN
    -- Reset failed emails that haven't been attempted in the last hour
    UPDATE email_queue 
    SET 
        status = 'pending',
        attempts = attempts + 1,
        last_error = NULL
    WHERE 
        status = 'failed' 
        AND attempts < 3 
        AND created_at > NOW() - INTERVAL '24 hours'
        AND (sent_at IS NULL OR sent_at < NOW() - INTERVAL '1 hour');
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old emails
CREATE OR REPLACE FUNCTION cleanup_old_emails()
RETURNS void AS $$
BEGIN
    -- Delete sent emails older than 30 days
    DELETE FROM email_queue 
    WHERE status = 'sent' 
    AND sent_at < NOW() - INTERVAL '30 days';
    
    -- Delete failed emails older than 7 days with 3+ attempts
    DELETE FROM email_queue 
    WHERE status = 'failed' 
    AND attempts >= 3 
    AND created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Create view for email queue management
CREATE OR REPLACE VIEW email_queue_summary AS
SELECT 
    id,
    to_email,
    from_email,
    subject,
    status,
    priority,
    attempts,
    created_at,
    sent_at,
    CASE 
        WHEN status = 'pending' AND created_at < NOW() - INTERVAL '1 hour' THEN 'overdue'
        WHEN status = 'failed' AND attempts >= 3 THEN 'max_attempts'
        WHEN status = 'pending' THEN 'ready'
        ELSE status
    END as queue_status,
    last_error
FROM email_queue
ORDER BY 
    priority ASC,
    created_at ASC;

-- Verification queries
SELECT 'Email Queue Table Created Successfully!' as status;

-- Show table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'email_queue'
ORDER BY ordinal_position;

-- Show current queue status
SELECT 
    status,
    COUNT(*) as count,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM email_queue 
GROUP BY status
ORDER BY status;

-- Instructions for email processing
SELECT 'EMAIL PROCESSING INSTRUCTIONS' as guide;
SELECT 'Check email_queue table for pending emails' as step_1
UNION ALL
SELECT 'Use email_queue_summary view for management' as step_2
UNION ALL
SELECT 'Run retry_failed_emails() to retry failed sends' as step_3
UNION ALL
SELECT 'Run cleanup_old_emails() to clean old records' as step_4;
