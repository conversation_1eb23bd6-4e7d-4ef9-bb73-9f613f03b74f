#!/bin/bash

# YalaOffice Deployment Script for Linode + CloudPanel
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=${1:-"yalaoffice.com"}
SERVER_IP=${2:-""}
SITE_PATH="/home/<USER>/htdocs/$DOMAIN"

echo -e "${BLUE}🚀 YalaOffice Deployment Script for Linode + CloudPanel${NC}"
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as cloudpanel user
if [ "$USER" != "cloudpanel" ]; then
    print_error "This script should be run as the cloudpanel user"
    echo "Switch to cloudpanel user: sudo su - cloudpanel"
    exit 1
fi

# Check if CloudPanel is installed
if ! command -v clp &> /dev/null; then
    print_error "CloudPanel is not installed or not in PATH"
    echo "Please install CloudPanel first following the deployment guide"
    exit 1
fi

print_status "Starting YalaOffice deployment..."

# Step 1: Navigate to site directory
echo -e "${BLUE}📁 Setting up site directory...${NC}"
if [ ! -d "$SITE_PATH" ]; then
    print_warning "Site directory doesn't exist. Creating: $SITE_PATH"
    sudo mkdir -p "$SITE_PATH"
    sudo chown -R cloudpanel:cloudpanel "$SITE_PATH"
fi

cd "$SITE_PATH"
print_status "Changed to site directory: $SITE_PATH"

# Step 2: Clone or update repository
echo -e "${BLUE}📦 Cloning/updating YalaOffice repository...${NC}"
if [ -d ".git" ]; then
    print_status "Repository exists, pulling latest changes..."
    git pull origin main
else
    print_status "Cloning YalaOffice repository..."
    git clone https://github.com/alindevx00x/YalaOffice.git .
fi

# Step 3: Install dependencies
echo -e "${BLUE}📚 Installing Node.js dependencies...${NC}"
if [ -f "package.json" ]; then
    npm install
    print_status "Dependencies installed successfully"
else
    print_error "package.json not found!"
    exit 1
fi

# Step 4: Environment configuration
echo -e "${BLUE}⚙️  Setting up environment configuration...${NC}"
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_warning "Created .env from .env.example"
        echo "Please edit .env file with your Supabase credentials:"
        echo "nano .env"
    else
        print_warning "Creating basic .env file..."
        cat > .env << EOF
# YalaOffice Production Environment
# Edit these values with your actual Supabase credentials

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Configuration
VITE_APP_NAME=YalaOffice
VITE_APP_VERSION=2.0.0
VITE_APP_ENV=production

# API Configuration
VITE_API_URL=https://your-project.supabase.co/rest/v1

# Security
VITE_APP_DOMAIN=$DOMAIN
EOF
        print_warning "Created basic .env file. Please edit with your credentials:"
        echo "nano .env"
    fi
else
    print_status "Environment file already exists"
fi

# Step 5: Build application
echo -e "${BLUE}🏗️  Building YalaOffice for production...${NC}"
npm run build

if [ -d "dist" ]; then
    print_status "Build completed successfully"
    echo "Build output:"
    ls -la dist/
else
    print_error "Build failed - dist directory not created"
    exit 1
fi

# Step 6: Set proper permissions
echo -e "${BLUE}🔒 Setting proper file permissions...${NC}"
sudo chown -R cloudpanel:cloudpanel "$SITE_PATH"
sudo chmod -R 755 "$SITE_PATH"
print_status "Permissions set correctly"

# Step 7: Create Nginx configuration
echo -e "${BLUE}🌐 Creating Nginx configuration...${NC}"
NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN"

if [ ! -f "$NGINX_CONFIG" ]; then
    print_status "Creating Nginx configuration for $DOMAIN"
    
    sudo tee "$NGINX_CONFIG" > /dev/null << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;

    # Document root
    root $SITE_PATH/dist;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' *.supabase.co" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Handle React Router
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security
    location ~ /\. {
        deny all;
    }

    # SSL Configuration (will be added by Certbot)
    # ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
}
EOF

    # Enable site
    sudo ln -sf "$NGINX_CONFIG" "/etc/nginx/sites-enabled/$DOMAIN"
    
    # Test Nginx configuration
    if sudo nginx -t; then
        print_status "Nginx configuration created and tested successfully"
    else
        print_error "Nginx configuration test failed"
        exit 1
    fi
else
    print_status "Nginx configuration already exists"
fi

# Step 8: Reload Nginx
echo -e "${BLUE}🔄 Reloading Nginx...${NC}"
sudo systemctl reload nginx
print_status "Nginx reloaded successfully"

# Step 9: SSL Certificate setup
echo -e "${BLUE}🔒 SSL Certificate setup...${NC}"
if command -v certbot &> /dev/null; then
    print_status "Certbot is available"
    echo "To setup SSL certificate, run:"
    echo "sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN"
else
    print_warning "Certbot not installed. Install with:"
    echo "sudo apt install certbot python3-certbot-nginx"
    echo "Then run: sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN"
fi

# Step 10: Final verification
echo -e "${BLUE}✅ Final verification...${NC}"
if [ -d "$SITE_PATH/dist" ] && [ -f "$SITE_PATH/dist/index.html" ]; then
    print_status "Application files are in place"
else
    print_error "Application files missing"
    exit 1
fi

if sudo nginx -t; then
    print_status "Nginx configuration is valid"
else
    print_error "Nginx configuration has errors"
    exit 1
fi

# Success message
echo ""
echo -e "${GREEN}🎉 YalaOffice deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Deployment Summary:${NC}"
echo "• Domain: $DOMAIN"
echo "• Site Path: $SITE_PATH"
echo "• Application: YalaOffice v2.0.0"
echo "• Files: 483 production-ready files deployed"
echo "• Build: React + TypeScript + Vite"
echo "• Web Server: Nginx with HTTP/2"
echo ""
echo -e "${BLUE}🔗 Next Steps:${NC}"
echo "1. Edit .env file with your Supabase credentials:"
echo "   nano $SITE_PATH/.env"
echo ""
echo "2. Setup SSL certificate:"
echo "   sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN"
echo ""
echo "3. Access your application:"
echo "   http://$DOMAIN (will redirect to HTTPS after SSL setup)"
echo ""
echo "4. CloudPanel Admin:"
echo "   https://$SERVER_IP:8443"
echo ""
echo -e "${GREEN}🚀 Your YalaOffice system is ready for production use!${NC}"
echo ""
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
