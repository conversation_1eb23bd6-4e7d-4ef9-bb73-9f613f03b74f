#!/usr/bin/env node

/**
 * Test User Management data fetching to identify synchronization issues
 * This script simulates the liveDataService.getAllUsers() call
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const anonKey = process.env.VITE_SUPABASE_ANON_KEY;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !anonKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create clients
const supabaseAnon = createClient(supabaseUrl, anonKey);
const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Transform user data (simplified version of liveDataService.transformUserData)
function transformUserData(users) {
  return users.map(user => ({
    id: user.id,
    email: user.email,
    fullName: user.full_name,
    userType: user.user_type,
    phone: user.phone,
    city: user.city,
    isActive: user.is_active,
    isCompany: user.is_company,
    companyName: user.company_name,
    createdAt: user.created_at,
    updatedAt: user.updated_at
  }));
}

async function testUserManagement() {
  console.log('🧪 Testing User Management Data Fetching...\n');
  
  // Test 1: Simulate liveDataService.getAllUsers() with anon client
  console.log('1️⃣ Testing getAllUsers with anonymous client (simulating frontend)...');
  try {
    console.log('   Attempting RPC call: get_all_users_admin...');
    const { data: rpcData, error: rpcError } = await supabaseAnon.rpc('get_all_users_admin');
    
    if (rpcError) {
      console.log('   ⚠️  RPC failed:', rpcError.message);
      console.log('   🔄 Falling back to direct query...');
      
      const { data: fallbackData, error: fallbackError } = await supabaseAnon
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (fallbackError) {
        console.log('   ❌ Fallback query failed:', fallbackError.message);
      } else {
        console.log('   ✅ Fallback query succeeded! Found', fallbackData?.length || 0, 'users');
        if (fallbackData && fallbackData.length > 0) {
          const transformed = transformUserData(fallbackData);
          console.log('   📊 User types:', transformed.map(u => u.userType).join(', '));
          console.log('   📊 Active users:', transformed.filter(u => u.isActive).length);
        }
      }
    } else {
      console.log('   ✅ RPC succeeded! Found', rpcData?.length || 0, 'users');
      if (rpcData && rpcData.length > 0) {
        const transformed = transformUserData(rpcData);
        console.log('   📊 User types:', transformed.map(u => u.userType).join(', '));
      }
    }
  } catch (err) {
    console.log('   ❌ Test failed:', err.message);
  }
  
  // Test 2: Test with service role (admin) client
  console.log('\n2️⃣ Testing getAllUsers with service role client (admin access)...');
  try {
    console.log('   Attempting RPC call: get_all_users_admin...');
    const { data: rpcData, error: rpcError } = await supabaseAdmin.rpc('get_all_users_admin');
    
    if (rpcError) {
      console.log('   ⚠️  RPC failed:', rpcError.message);
    } else {
      console.log('   ✅ RPC succeeded! Found', rpcData?.length || 0, 'users');
      if (rpcData && rpcData.length > 0) {
        const transformed = transformUserData(rpcData);
        console.log('   📊 User types:', transformed.map(u => u.userType).join(', '));
        console.log('   📊 Active users:', transformed.filter(u => u.isActive).length);
        console.log('   📊 Sample users:');
        transformed.slice(0, 3).forEach(user => {
          console.log(`      - ${user.fullName} (${user.userType}) - ${user.isActive ? 'Active' : 'Inactive'}`);
        });
      }
    }
  } catch (err) {
    console.log('   ❌ Test failed:', err.message);
  }
  
  // Test 3: Check RLS policies
  console.log('\n3️⃣ Testing RLS policies...');
  try {
    // Test as anonymous user
    const { data: anonData, error: anonError } = await supabaseAnon
      .from('users')
      .select('id, email, full_name, user_type')
      .limit(10);
    
    console.log('   Anonymous access:', anonError ? `❌ ${anonError.message}` : `✅ ${anonData?.length || 0} users`);
    
    // Test with service role
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('users')
      .select('id, email, full_name, user_type')
      .limit(10);
    
    console.log('   Admin access:', adminError ? `❌ ${adminError.message}` : `✅ ${adminData?.length || 0} users`);
    
  } catch (err) {
    console.log('   ❌ RLS test failed:', err.message);
  }
  
  // Test 4: Count total users
  console.log('\n4️⃣ Checking total user count...');
  try {
    const { count, error } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.log('   ❌ Count query failed:', error.message);
    } else {
      console.log('   ✅ Total users in database:', count);
    }
  } catch (err) {
    console.log('   ❌ Count test failed:', err.message);
  }
  
  console.log('\n📋 Analysis:');
  console.log('- If RPC works with service role but not anon, it\'s a permissions issue');
  console.log('- If fallback query works, the issue is in the RPC function logic');
  console.log('- If counts don\'t match, there might be filtering or RLS issues');
}

// Run the tests
testUserManagement();
