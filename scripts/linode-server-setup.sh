#!/bin/bash

# YalaOffice Linode Server Initial Setup Script
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice
# Purpose: Prepare Linode server for CloudPanel installation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 YalaOffice Linode Server Setup Script${NC}"
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run as root"
    echo "Run with: sudo bash linode-server-setup.sh"
    exit 1
fi

print_status "Starting Linode server setup for YalaOffice..."

# Step 1: Update system
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt update && apt upgrade -y
print_status "System packages updated"

# Step 2: Install essential packages
echo -e "${BLUE}🛠️  Installing essential packages...${NC}"
apt install -y curl wget git unzip software-properties-common ufw fail2ban htop nano
print_status "Essential packages installed"

# Step 3: Configure firewall
echo -e "${BLUE}🔥 Configuring UFW firewall...${NC}"
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow OpenSSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8443/tcp  # CloudPanel admin
ufw --force enable
print_status "Firewall configured and enabled"

# Step 4: Configure fail2ban
echo -e "${BLUE}🛡️  Configuring fail2ban...${NC}"
systemctl enable fail2ban
systemctl start fail2ban
print_status "Fail2ban configured and started"

# Step 5: Create cloudpanel user
echo -e "${BLUE}👤 Creating cloudpanel user...${NC}"
if id "cloudpanel" &>/dev/null; then
    print_warning "User cloudpanel already exists"
else
    adduser --disabled-password --gecos "" cloudpanel
    usermod -aG sudo cloudpanel
    print_status "User cloudpanel created and added to sudo group"
fi

# Step 6: Set up SSH key for cloudpanel user (if root has one)
echo -e "${BLUE}🔑 Setting up SSH keys...${NC}"
if [ -f "/root/.ssh/authorized_keys" ]; then
    mkdir -p /home/<USER>/.ssh
    cp /root/.ssh/authorized_keys /home/<USER>/.ssh/
    chown -R cloudpanel:cloudpanel /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    chmod 600 /home/<USER>/.ssh/authorized_keys
    print_status "SSH keys copied to cloudpanel user"
else
    print_warning "No SSH keys found for root user"
fi

# Step 7: Configure system limits
echo -e "${BLUE}⚙️  Configuring system limits...${NC}"
cat >> /etc/security/limits.conf << EOF
# YalaOffice system limits
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF
print_status "System limits configured"

# Step 8: Configure sysctl for performance
echo -e "${BLUE}🚀 Optimizing system performance...${NC}"
cat >> /etc/sysctl.conf << EOF
# YalaOffice performance optimizations
net.core.somaxconn = 65536
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65536
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 400000
vm.swappiness = 10
EOF
sysctl -p
print_status "System performance optimized"

# Step 9: Install Node.js (required for YalaOffice)
echo -e "${BLUE}📦 Installing Node.js...${NC}"
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs
print_status "Node.js $(node --version) installed"

# Step 10: Download CloudPanel installer
echo -e "${BLUE}📥 Downloading CloudPanel installer...${NC}"
cd /tmp
curl -sS https://installer.cloudpanel.io/ce/v2/install.sh -o install.sh
echo "a3ba69a8102345127b4ae0e28cfe89daca675cbc63cd39225133cdd2fa02ad36 install.sh" | sha256sum -c
if [ $? -eq 0 ]; then
    print_status "CloudPanel installer downloaded and verified"
else
    print_error "CloudPanel installer verification failed"
    exit 1
fi

# Step 11: System information
echo -e "${BLUE}📊 System Information:${NC}"
echo "• OS: $(lsb_release -d | cut -f2)"
echo "• Kernel: $(uname -r)"
echo "• CPU: $(nproc) cores"
echo "• RAM: $(free -h | awk '/^Mem:/ {print $2}')"
echo "• Disk: $(df -h / | awk 'NR==2 {print $2}')"
echo "• Node.js: $(node --version)"
echo "• NPM: $(npm --version)"

# Step 12: Final instructions
echo ""
echo -e "${GREEN}🎉 Linode server setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo ""
echo "1. Install CloudPanel:"
echo "   sudo bash /tmp/install.sh"
echo ""
echo "2. Access CloudPanel admin (after installation):"
echo "   https://$(curl -s ifconfig.me):8443"
echo ""
echo "3. Switch to cloudpanel user:"
echo "   sudo su - cloudpanel"
echo ""
echo "4. Deploy YalaOffice:"
echo "   # Download the deployment script"
echo "   wget https://raw.githubusercontent.com/alindevx00x/YalaOffice/main/scripts/deploy-to-linode.sh"
echo "   chmod +x deploy-to-linode.sh"
echo "   ./deploy-to-linode.sh your-domain.com"
echo ""
echo -e "${BLUE}🔒 Security Notes:${NC}"
echo "• UFW firewall is enabled with necessary ports"
echo "• Fail2ban is configured for SSH protection"
echo "• System limits optimized for web applications"
echo "• Performance tuning applied"
echo ""
echo -e "${BLUE}📊 Server Status:${NC}"
echo "• Firewall: $(ufw status | head -1)"
echo "• Fail2ban: $(systemctl is-active fail2ban)"
echo "• SSH: Port 22 (change if needed for security)"
echo ""
echo -e "${GREEN}🚀 Your Linode server is ready for CloudPanel installation!${NC}"
echo ""
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
