#!/usr/bin/env node

/**
 * Test RPC functions to verify they exist and work correctly
 * This script tests the missing RPC functions after migration
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testRPCFunctions() {
  console.log('🧪 Testing RPC Functions...\n');
  
  // Test 1: get_all_users_admin
  console.log('1️⃣ Testing get_all_users_admin...');
  try {
    const { data, error } = await supabase.rpc('get_all_users_admin');
    
    if (error) {
      if (error.message.includes('function get_all_users_admin() does not exist')) {
        console.log('❌ Function does not exist - migration needs to be applied');
      } else if (error.message.includes('Access denied')) {
        console.log('✅ Function exists but access denied (expected for non-admin)');
      } else {
        console.log('⚠️  Function exists but returned error:', error.message);
      }
    } else {
      console.log('✅ Function works! Returned', data?.length || 0, 'users');
    }
  } catch (err) {
    console.log('❌ Function test failed:', err.message);
  }
  
  // Test 2: check_email_exists
  console.log('\n2️⃣ Testing check_email_exists...');
  try {
    const { data, error } = await supabase.rpc('check_email_exists', {
      email_to_check: '<EMAIL>'
    });
    
    if (error) {
      if (error.message.includes('function check_email_exists') && error.message.includes('does not exist')) {
        console.log('❌ Function does not exist - migration needs to be applied');
      } else {
        console.log('⚠️  Function exists but returned error:', error.message);
      }
    } else {
      console.log('✅ Function works! Email exists:', data);
    }
  } catch (err) {
    console.log('❌ Function test failed:', err.message);
  }
  
  // Test 3: admin_update_user_password
  console.log('\n3️⃣ Testing admin_update_user_password...');
  try {
    const { data, error } = await supabase.rpc('admin_update_user_password', {
      target_user_email: '<EMAIL>',
      admin_user_id: '00000000-0000-0000-0000-000000000000'
    });
    
    if (error) {
      if (error.message.includes('function admin_update_user_password') && error.message.includes('does not exist')) {
        console.log('❌ Function does not exist - migration needs to be applied');
      } else {
        console.log('⚠️  Function exists but returned error:', error.message);
      }
    } else {
      console.log('✅ Function works! Result:', data);
    }
  } catch (err) {
    console.log('❌ Function test failed:', err.message);
  }
  
  // Test 4: Check if we can query users table directly
  console.log('\n4️⃣ Testing direct users table access...');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, email, full_name, user_type')
      .limit(5);
    
    if (error) {
      console.log('❌ Direct query failed:', error.message);
    } else {
      console.log('✅ Direct query works! Found', data?.length || 0, 'users');
      if (data && data.length > 0) {
        console.log('   Sample user types:', data.map(u => u.user_type).join(', '));
      }
    }
  } catch (err) {
    console.log('❌ Direct query failed:', err.message);
  }
  
  console.log('\n📋 Summary:');
  console.log('If any functions show "does not exist", the migration needs to be applied manually.');
  console.log('Please go to your Supabase dashboard > SQL Editor and run the migration SQL.');
}

// Run the tests
testRPCFunctions();
