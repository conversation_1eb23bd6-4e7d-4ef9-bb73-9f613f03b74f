#!/usr/bin/env node

/**
 * Apply the RPC function fix and test it
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyFixAndTest() {
  try {
    console.log('🔧 Applying RPC function fix...\n');
    
    // Read the fix migration file
    const fixPath = join(projectRoot, 'supabase/migrations/20240131000001_fix_get_all_users_admin.sql');
    const fixSQL = readFileSync(fixPath, 'utf8');
    
    console.log('📄 Fix migration loaded');
    console.log('📋 SQL to apply:');
    console.log('=' .repeat(80));
    console.log(fixSQL);
    console.log('=' .repeat(80));
    console.log('');
    console.log('⚠️  Please apply this SQL manually in your Supabase dashboard:');
    console.log('1. Go to https://supabase.com/dashboard');
    console.log('2. Select your project');
    console.log('3. Go to SQL Editor');
    console.log('4. Copy and paste the above SQL');
    console.log('5. Click "Run"');
    console.log('');
    
    // Test the current function first
    console.log('🧪 Testing current function before fix...');
    const { data: beforeData, error: beforeError } = await supabase.rpc('get_all_users_admin');
    console.log('Before fix:', beforeError ? `❌ ${beforeError.message}` : `✅ ${beforeData?.length || 0} users`);
    
    console.log('\n⏳ Waiting for you to apply the fix manually...');
    console.log('Press Ctrl+C when done, then run this script again to test.');
    
    // Keep the script running so user can see the instructions
    await new Promise(resolve => {
      process.stdin.resume();
      process.on('SIGINT', () => {
        console.log('\n👋 Exiting. Run this script again after applying the fix to test it.');
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run the fix
applyFixAndTest();
