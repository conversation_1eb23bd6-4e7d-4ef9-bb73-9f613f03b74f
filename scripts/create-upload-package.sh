#!/bin/bash

# YalaOffice Manual Upload Package Creator
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice
# Purpose: Create a clean package for manual upload to CloudPanel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📦 Creating YalaOffice Upload Package${NC}"
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo ""

# Create upload directory
UPLOAD_DIR="yalaoffice-upload-package"
echo -e "${YELLOW}Creating upload package directory...${NC}"
rm -rf "$UPLOAD_DIR"
mkdir -p "$UPLOAD_DIR"

# Copy essential files and directories
echo -e "${YELLOW}Copying source files...${NC}"
cp -r src/ "$UPLOAD_DIR/"
cp -r public/ "$UPLOAD_DIR/"
cp package.json "$UPLOAD_DIR/"
cp package-lock.json "$UPLOAD_DIR/" 2>/dev/null || echo "No package-lock.json found"
cp vite.config.ts "$UPLOAD_DIR/"
cp tsconfig.json "$UPLOAD_DIR/"
cp tsconfig.node.json "$UPLOAD_DIR/"
cp tailwind.config.ts "$UPLOAD_DIR/"
cp postcss.config.js "$UPLOAD_DIR/"
cp eslint.config.js "$UPLOAD_DIR/"
cp index.html "$UPLOAD_DIR/"
cp README.md "$UPLOAD_DIR/"

# Copy essential configuration files
echo -e "${YELLOW}Copying configuration files...${NC}"
cp .env.production.example "$UPLOAD_DIR/"
cp -r docs/ "$UPLOAD_DIR/" 2>/dev/null || echo "No docs directory found"

# Copy database schema
echo -e "${YELLOW}Copying database files...${NC}"
cp database-schema-final.sql "$UPLOAD_DIR/" 2>/dev/null || echo "No database schema found"

# Copy essential scripts (production only)
echo -e "${YELLOW}Copying production scripts...${NC}"
mkdir -p "$UPLOAD_DIR/scripts"
cp scripts/build-production.sh "$UPLOAD_DIR/scripts/" 2>/dev/null || echo "No build script found"
cp scripts/deploy-production.sh "$UPLOAD_DIR/scripts/" 2>/dev/null || echo "No deploy script found"
cp scripts/setup-database.sh "$UPLOAD_DIR/scripts/" 2>/dev/null || echo "No database setup script found"

# Create production .env file
echo -e "${YELLOW}Creating production .env file...${NC}"
cat > "$UPLOAD_DIR/.env" << 'EOF'
# YalaOffice Production Environment Configuration - yalaoffice.com
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice
# Domain: yalaoffice.com
# ⚠️  NEVER commit this file to version control

# =============================================
# SUPABASE CONFIGURATION (PRODUCTION)
# =============================================
VITE_SUPABASE_URL=https://umzikqwughlzkiarldoa.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY

# =============================================
# APPLICATION CONFIGURATION (PRODUCTION)
# =============================================
NODE_ENV=production
VITE_APP_VERSION=2.0.0
VITE_APP_NAME=YalaOffice
VITE_APP_ENV=production
VITE_APP_DOMAIN=yalaoffice.com
VITE_APP_TITLE=YalaOffice - Smart Supply Management
VITE_APP_DESCRIPTION=Professional supply management system for Moroccan businesses

# =============================================
# API CONFIGURATION (PRODUCTION)
# =============================================
VITE_API_URL=https://umzikqwughlzkiarldoa.supabase.co/rest/v1
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# =============================================
# SECURITY CONFIGURATION (PRODUCTION)
# =============================================
VITE_DEBUG_MODE=false
VITE_SESSION_TIMEOUT=480
VITE_MAX_LOGIN_ATTEMPTS=5
VITE_LOCKOUT_DURATION=15
VITE_ANALYTICS_ENABLED=true

# =============================================
# FEATURE FLAGS (PRODUCTION)
# =============================================
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_EXPORT=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_REPORTS=true
VITE_FEATURE_MOBILE_APP=true

# =============================================
# LOCALIZATION (MOROCCO)
# =============================================
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_LOCALE=en-US
VITE_DEFAULT_TIMEZONE=Africa/Casablanca
VITE_DEFAULT_CURRENCY=MAD
VITE_CURRENCY_SYMBOL=Dh

# =============================================
# PERFORMANCE CONFIGURATION (PRODUCTION)
# =============================================
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100
VITE_MAX_FILE_SIZE=10
VITE_MAX_IMAGE_SIZE=5
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# =============================================
# MONITORING & LOGGING (PRODUCTION)
# =============================================
VITE_ERROR_REPORTING=true
VITE_LOG_LEVEL=error
VITE_MONITORING_ENABLED=true

# =============================================
# LEGACY SETTINGS (COMPATIBILITY)
# =============================================
LIVE_DATA_MIGRATION=true
DEBUG=false
EOF

# Create upload instructions
echo -e "${YELLOW}Creating upload instructions...${NC}"
cat > "$UPLOAD_DIR/UPLOAD_INSTRUCTIONS.md" << 'EOF'
# YalaOffice Manual Upload Instructions

## 📁 Upload Process via CloudPanel File Manager

### Step 1: Upload Files
1. Login to CloudPanel Admin: https://YOUR_SERVER_IP:8443
2. Go to Sites → yalaoffice.com → File Manager
3. Navigate to: /home/<USER>/htdocs/yalaoffice.com/
4. Upload all files and folders from this package

### Step 2: Install Dependencies
Via CloudPanel Terminal or SSH:
```bash
cd /home/<USER>/htdocs/yalaoffice.com
npm install
```

### Step 3: Build for Production
```bash
npm run build
```

### Step 4: Set Permissions
```bash
sudo chown -R yalaoffice:yalaoffice .
chmod 600 .env
chmod -R 755 .
```

### Step 5: Configure Nginx
The nginx configuration should point to the `dist/` folder:
```
root /home/<USER>/htdocs/yalaoffice.com/dist;
```

## ✅ Verification
- Visit: https://yalaoffice.com
- Check all features work
- Verify Supabase connection

## 📞 Support
Developer: Alindevx00x (https://github.com/alindevx00x)
Repository: https://github.com/alindevx00x/YalaOffice
EOF

# Create .gitignore for the package
cat > "$UPLOAD_DIR/.gitignore" << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Cache
.cache/
.temp/
EOF

# Create package info
cat > "$UPLOAD_DIR/PACKAGE_INFO.txt" << EOF
YalaOffice Manual Upload Package
================================

Created: $(date)
Developer: Alindevx00x (https://github.com/alindevx00x)
Repository: https://github.com/alindevx00x/YalaOffice
Domain: yalaoffice.com

Package Contents:
- Source code (src/)
- Public assets (public/)
- Configuration files
- Production environment (.env)
- Upload instructions
- Database schema

Total Files: $(find "$UPLOAD_DIR" -type f | wc -l)
Package Size: $(du -sh "$UPLOAD_DIR" | cut -f1)

Instructions:
1. Upload all contents to CloudPanel File Manager
2. Run: npm install
3. Run: npm run build
4. Configure nginx to serve from dist/ folder
5. Visit: https://yalaoffice.com

Support: https://github.com/alindevx00x/YalaOffice
EOF

# Create ZIP package
echo -e "${YELLOW}Creating ZIP package...${NC}"
zip -r "yalaoffice-upload-package.zip" "$UPLOAD_DIR/" > /dev/null

# Summary
echo ""
echo -e "${GREEN}✅ Upload package created successfully!${NC}"
echo ""
echo -e "${BLUE}📦 Package Details:${NC}"
echo "• Directory: $UPLOAD_DIR/"
echo "• ZIP File: yalaoffice-upload-package.zip"
echo "• Total Files: $(find "$UPLOAD_DIR" -type f | wc -l)"
echo "• Package Size: $(du -sh "$UPLOAD_DIR" | cut -f1)"
echo "• ZIP Size: $(du -sh yalaoffice-upload-package.zip | cut -f1)"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Download: yalaoffice-upload-package.zip"
echo "2. Extract and upload contents via CloudPanel File Manager"
echo "3. Follow instructions in UPLOAD_INSTRUCTIONS.md"
echo ""
echo -e "${GREEN}🚀 Ready for manual upload to yalaoffice.com!${NC}"
echo ""
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
