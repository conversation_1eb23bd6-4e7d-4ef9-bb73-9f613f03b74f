#!/bin/bash

# YalaOffice Upload Package Fix Script
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Purpose: Fix missing files and configuration issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Fixing YalaOffice Upload Package Issues${NC}"
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo ""

# Create updated upload directory
UPLOAD_DIR="yalaoffice-upload-package-fixed"
echo -e "${YELLOW}Creating fixed upload package...${NC}"
rm -rf "$UPLOAD_DIR"
mkdir -p "$UPLOAD_DIR"

# Copy all essential files
echo -e "${YELLOW}Copying source files...${NC}"
cp -r src/ "$UPLOAD_DIR/"
cp -r public/ "$UPLOAD_DIR/"
cp package.json "$UPLOAD_DIR/"
cp package-lock.json "$UPLOAD_DIR/" 2>/dev/null || echo "No package-lock.json found"
cp index.html "$UPLOAD_DIR/"
cp README.md "$UPLOAD_DIR/"

# Create proper TypeScript configurations
echo -e "${YELLOW}Creating TypeScript configurations...${NC}"

# Main tsconfig.json
cat > "$UPLOAD_DIR/tsconfig.json" << 'EOF'
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
EOF

# App-specific tsconfig
cat > "$UPLOAD_DIR/tsconfig.app.json" << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
EOF

# Node-specific tsconfig
cat > "$UPLOAD_DIR/tsconfig.node.json" << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["vite.config.ts"]
}
EOF

# Create proper Vite configuration
echo -e "${YELLOW}Creating Vite configuration...${NC}"
cat > "$UPLOAD_DIR/vite.config.ts" << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production')
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu']
        }
      }
    }
  },
  server: {
    port: 5173,
    host: true
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@supabase/supabase-js']
  }
})
EOF

# Create Tailwind configuration
echo -e "${YELLOW}Creating Tailwind configuration...${NC}"
cat > "$UPLOAD_DIR/tailwind.config.ts" << 'EOF'
import type { Config } from 'tailwindcss'

export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './index.html',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        'teal': {
          '600': '#0d9488'
        },
        'amber': {
          '500': '#f29f06'
        }
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config
EOF

# Create other configuration files
echo -e "${YELLOW}Creating other configuration files...${NC}"

# PostCSS config
cat > "$UPLOAD_DIR/postcss.config.js" << 'EOF'
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
EOF

# ESLint config
cat > "$UPLOAD_DIR/eslint.config.js" << 'EOF'
import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
)
EOF

# Create fixed .env file (without NODE_ENV)
echo -e "${YELLOW}Creating fixed .env file...${NC}"
cat > "$UPLOAD_DIR/.env" << 'EOF'
# YalaOffice Production Environment Configuration - yalaoffice.com
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice
# Domain: yalaoffice.com
# ⚠️  NEVER commit this file to version control

# =============================================
# SUPABASE CONFIGURATION (PRODUCTION)
# =============================================
VITE_SUPABASE_URL=https://umzikqwughlzkiarldoa.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY

# =============================================
# APPLICATION CONFIGURATION (PRODUCTION)
# =============================================
VITE_APP_VERSION=2.0.0
VITE_APP_NAME=YalaOffice
VITE_APP_ENV=production
VITE_APP_DOMAIN=yalaoffice.com
VITE_APP_TITLE=YalaOffice - Smart Supply Management
VITE_APP_DESCRIPTION=Professional supply management system for Moroccan businesses

# =============================================
# API CONFIGURATION (PRODUCTION)
# =============================================
VITE_API_URL=https://umzikqwughlzkiarldoa.supabase.co/rest/v1
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# =============================================
# SECURITY CONFIGURATION (PRODUCTION)
# =============================================
VITE_DEBUG_MODE=false
VITE_SESSION_TIMEOUT=480
VITE_MAX_LOGIN_ATTEMPTS=5
VITE_LOCKOUT_DURATION=15
VITE_ANALYTICS_ENABLED=true

# =============================================
# FEATURE FLAGS (PRODUCTION)
# =============================================
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_EXPORT=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_REPORTS=true
VITE_FEATURE_MOBILE_APP=true

# =============================================
# LOCALIZATION (MOROCCO)
# =============================================
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_LOCALE=en-US
VITE_DEFAULT_TIMEZONE=Africa/Casablanca
VITE_DEFAULT_CURRENCY=MAD
VITE_CURRENCY_SYMBOL=Dh

# =============================================
# PERFORMANCE CONFIGURATION (PRODUCTION)
# =============================================
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100
VITE_MAX_FILE_SIZE=10
VITE_MAX_IMAGE_SIZE=5
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# =============================================
# MONITORING & LOGGING (PRODUCTION)
# =============================================
VITE_ERROR_REPORTING=true
VITE_LOG_LEVEL=error
VITE_MONITORING_ENABLED=true

# =============================================
# LEGACY SETTINGS (COMPATIBILITY)
# =============================================
LIVE_DATA_MIGRATION=true
DEBUG=false
EOF

# Create .gitignore
cat > "$UPLOAD_DIR/.gitignore" << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Cache
.cache/
.temp/
EOF

# Create updated instructions
cat > "$UPLOAD_DIR/FIXED_UPLOAD_INSTRUCTIONS.md" << 'EOF'
# Fixed YalaOffice Upload Instructions

## Issues Fixed:
1. ✅ Added missing tsconfig.app.json
2. ✅ Removed NODE_ENV from .env file
3. ✅ Updated Vite configuration for production
4. ✅ Fixed TypeScript configurations
5. ✅ Updated Tailwind and other configs

## Upload Process:
1. Upload all files to CloudPanel File Manager
2. Run: npm install
3. Run: npm run build
4. Configure nginx to serve from dist/ folder

## Build should now work without errors!
EOF

# Create ZIP package
echo -e "${YELLOW}Creating fixed ZIP package...${NC}"
zip -r "yalaoffice-upload-package-fixed.zip" "$UPLOAD_DIR/" > /dev/null

# Summary
echo ""
echo -e "${GREEN}✅ Fixed upload package created successfully!${NC}"
echo ""
echo -e "${BLUE}📦 Fixed Package Details:${NC}"
echo "• Directory: $UPLOAD_DIR/"
echo "• ZIP File: yalaoffice-upload-package-fixed.zip"
echo "• Total Files: $(find "$UPLOAD_DIR" -type f | wc -l)"
echo "• Package Size: $(du -sh "$UPLOAD_DIR" | cut -f1)"
echo "• ZIP Size: $(du -sh yalaoffice-upload-package-fixed.zip | cut -f1)"
echo ""
echo -e "${BLUE}🔧 Issues Fixed:${NC}"
echo "• Added missing tsconfig.app.json"
echo "• Removed NODE_ENV from .env"
echo "• Updated Vite configuration"
echo "• Fixed TypeScript configurations"
echo "• Updated all config files"
echo ""
echo -e "${GREEN}🚀 Ready for upload - build should work now!${NC}"
echo ""
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
