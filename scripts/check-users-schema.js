#!/usr/bin/env node

/**
 * Check the actual users table schema to identify column mismatches
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkUsersSchema() {
  console.log('🔍 Checking actual users table schema...\n');
  
  try {
    // Get table schema information
    const { data: schemaData, error: schemaError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'users')
      .eq('table_schema', 'public')
      .order('ordinal_position');
    
    if (schemaError) {
      console.log('⚠️  Could not get schema info, trying direct query...');
      
      // Try to get a sample row to see actual structure
      const { data: sampleData, error: sampleError } = await supabase
        .from('users')
        .select('*')
        .limit(1);
      
      if (sampleError) {
        console.log('❌ Sample query failed:', sampleError.message);
        return;
      }
      
      if (sampleData && sampleData.length > 0) {
        console.log('✅ Sample user data structure:');
        const sampleUser = sampleData[0];
        Object.keys(sampleUser).forEach(key => {
          const value = sampleUser[key];
          const type = typeof value;
          console.log(`   ${key}: ${type} = ${value === null ? 'NULL' : JSON.stringify(value)}`);
        });
      } else {
        console.log('⚠️  No users found in table');
      }
      
    } else {
      console.log('✅ Users table schema:');
      schemaData.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
    }
    
    // Test what columns we can actually select
    console.log('\n🧪 Testing column selection...');
    
    const testColumns = [
      'id', 'email', 'full_name', 'user_type', 'phone', 'city', 
      'is_active', 'status', 'is_company', 'company_name', 'ice_number',
      'company_address', 'company_phone', 'company_city', 'company_email',
      'tax_id', 'legal_form', 'created_at', 'updated_at'
    ];
    
    const workingColumns = [];
    const failingColumns = [];
    
    for (const column of testColumns) {
      try {
        const { data, error } = await supabase
          .from('users')
          .select(column)
          .limit(1);
        
        if (error) {
          failingColumns.push(column);
          console.log(`   ❌ ${column}: ${error.message}`);
        } else {
          workingColumns.push(column);
          console.log(`   ✅ ${column}: OK`);
        }
      } catch (err) {
        failingColumns.push(column);
        console.log(`   ❌ ${column}: ${err.message}`);
      }
    }
    
    console.log('\n📊 Summary:');
    console.log(`✅ Working columns (${workingColumns.length}):`, workingColumns.join(', '));
    console.log(`❌ Failing columns (${failingColumns.length}):`, failingColumns.join(', '));
    
    // Generate corrected function
    console.log('\n🔧 Suggested RPC function columns:');
    console.log('RETURNS TABLE (');
    workingColumns.forEach((col, index) => {
      const isLast = index === workingColumns.length - 1;
      let dataType = 'TEXT';
      
      // Map common column types
      if (col === 'id') dataType = 'UUID';
      else if (col.includes('is_') || col === 'is_active' || col === 'is_company') dataType = 'BOOLEAN';
      else if (col.includes('_at') || col === 'created_at' || col === 'updated_at') dataType = 'TIMESTAMPTZ';
      
      console.log(`    ${col} ${dataType}${isLast ? '' : ','}`);
    });
    console.log(')');
    
  } catch (error) {
    console.error('❌ Schema check failed:', error);
  }
}

// Run the schema check
checkUsersSchema();
