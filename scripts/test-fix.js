#!/usr/bin/env node

/**
 * Test the RPC function fix
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const anonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !serviceRoleKey || !anonKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create clients
const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const supabaseAnon = createClient(supabaseUrl, anonKey);

async function testFix() {
  console.log('🧪 Testing RPC Function Fix...\n');
  
  // Test 1: Service role access
  console.log('1️⃣ Testing with service role (should work)...');
  try {
    const { data, error } = await supabaseAdmin.rpc('get_all_users_admin');
    
    if (error) {
      console.log('❌ Service role test failed:', error.message);
    } else {
      console.log('✅ Service role test passed!');
      console.log(`   Found ${data?.length || 0} users`);
      
      if (data && data.length > 0) {
        const userTypes = data.reduce((acc, user) => {
          acc[user.user_type] = (acc[user.user_type] || 0) + 1;
          return acc;
        }, {});
        console.log('   User types:', JSON.stringify(userTypes, null, 2));
        
        console.log('   Sample users:');
        data.slice(0, 3).forEach(user => {
          console.log(`   - ${user.full_name} (${user.user_type}) - ${user.is_active ? 'Active' : 'Inactive'}`);
        });
      }
    }
  } catch (err) {
    console.log('❌ Service role test error:', err.message);
  }
  
  // Test 2: Anonymous access (should fail with proper error)
  console.log('\n2️⃣ Testing with anonymous access (should fail gracefully)...');
  try {
    const { data, error } = await supabaseAnon.rpc('get_all_users_admin');
    
    if (error) {
      if (error.message.includes('Access denied')) {
        console.log('✅ Anonymous access properly denied:', error.message);
      } else {
        console.log('⚠️  Anonymous access failed with unexpected error:', error.message);
      }
    } else {
      console.log('⚠️  Anonymous access unexpectedly succeeded with', data?.length || 0, 'users');
    }
  } catch (err) {
    console.log('❌ Anonymous test error:', err.message);
  }
  
  // Test 3: Compare with direct query
  console.log('\n3️⃣ Comparing with direct database query...');
  try {
    const { count, error } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.log('❌ Direct query failed:', error.message);
    } else {
      console.log('✅ Direct query found', count, 'total users');
    }
  } catch (err) {
    console.log('❌ Direct query error:', err.message);
  }
  
  console.log('\n📊 Summary:');
  console.log('✅ If service role returns users matching the total count, the fix worked!');
  console.log('✅ If anonymous access is properly denied, security is working!');
  console.log('🎯 The User Management component should now show all users correctly.');
}

// Run the test
testFix();
