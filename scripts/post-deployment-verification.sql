-- =============================================
-- POST-DEPLOYMENT VERIFICATION SCRIPT
-- =============================================
-- Developer: Alindevx00x (https://github.com/alindevx00x)
-- Purpose: Comprehensive verification of YalaOffice database deployment
-- Run this after deploying database-schema-final.sql

-- =============================================
-- SCHEMA INTEGRITY VERIFICATION
-- =============================================

SELECT '🔍 STARTING POST-DEPLOYMENT VERIFICATION' as status;
SELECT '============================================' as separator;

-- Check 1: Verify all required tables exist
SELECT '📋 TABLE VERIFICATION' as check_type;
WITH required_tables AS (
    SELECT unnest(ARRAY[
        'users', 'user_profiles', 'branches', 'categories', 'products',
        'orders', 'order_items', 'order_tracking', 'customer_profiles',
        'wishlists', 'product_reviews', 'stock_movements', 'branch_inventory',
        'promo_codes', 'company_settings', 'system_configs', 'notifications',
        'email_queue', 'audit_logs', 'security_events', 'password_reset_tokens',
        'analytics_events', 'customer_behavior'
    ]) as table_name
),
existing_tables AS (
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
)
SELECT 
    rt.table_name,
    CASE 
        WHEN et.table_name IS NOT NULL THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
FROM required_tables rt
LEFT JOIN existing_tables et ON rt.table_name = et.table_name
ORDER BY rt.table_name;

-- Check 2: Verify all required functions exist
SELECT '🔧 FUNCTION VERIFICATION' as check_type;
WITH required_functions AS (
    SELECT unnest(ARRAY[
        'get_user_role', 'is_admin', 'is_manager_or_admin', 'can_manage_clients',
        'get_orders_for_user', 'get_user_statistics', 'calculate_order_total',
        'apply_promo_code', 'generate_order_number', 'update_updated_at_column',
        'verify_schema_integrity'
    ]) as function_name
),
existing_functions AS (
    SELECT routine_name as function_name
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_type = 'FUNCTION'
)
SELECT 
    rf.function_name,
    CASE 
        WHEN ef.function_name IS NOT NULL THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
FROM required_functions rf
LEFT JOIN existing_functions ef ON rf.function_name = ef.function_name
ORDER BY rf.function_name;

-- Check 3: Verify RLS is enabled on sensitive tables
SELECT '🔐 RLS VERIFICATION' as check_type;
WITH sensitive_tables AS (
    SELECT unnest(ARRAY[
        'users', 'user_profiles', 'orders', 'order_items', 'customer_profiles',
        'wishlists', 'product_reviews', 'promo_codes', 'notifications',
        'audit_logs', 'security_events'
    ]) as table_name
)
SELECT 
    st.table_name,
    CASE 
        WHEN c.relrowsecurity THEN '✅ ENABLED'
        ELSE '❌ DISABLED'
    END as rls_status
FROM sensitive_tables st
LEFT JOIN pg_class c ON c.relname = st.table_name
LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = 'public'
ORDER BY st.table_name;

-- Check 4: Verify indexes are created
SELECT '📊 INDEX VERIFICATION' as check_type;
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Check 5: Verify triggers are active
SELECT '⚡ TRIGGER VERIFICATION' as check_type;
SELECT 
    trigger_schema,
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing,
    action_statement
FROM information_schema.triggers
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- =============================================
-- DATA INTEGRITY VERIFICATION
-- =============================================

SELECT '🔍 DATA INTEGRITY VERIFICATION' as check_type;

-- Check 6: Verify seed data was inserted
SELECT '🌱 SEED DATA VERIFICATION' as check_type;
SELECT 'Company Settings' as data_type, COUNT(*) as count FROM company_settings
UNION ALL
SELECT 'System Configs' as data_type, COUNT(*) as count FROM system_configs
UNION ALL
SELECT 'Branches' as data_type, COUNT(*) as count FROM branches
UNION ALL
SELECT 'Categories' as data_type, COUNT(*) as count FROM categories
UNION ALL
SELECT 'Promo Codes' as data_type, COUNT(*) as count FROM promo_codes;

-- Check 7: Verify foreign key constraints
SELECT '🔗 FOREIGN KEY VERIFICATION' as check_type;
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

-- =============================================
-- PERFORMANCE VERIFICATION
-- =============================================

SELECT '⚡ PERFORMANCE VERIFICATION' as check_type;

-- Check 8: Verify table sizes and row counts
SELECT '📈 TABLE STATISTICS' as check_type;
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_stat_get_tuples_returned(c.oid) as rows_read,
    pg_stat_get_tuples_inserted(c.oid) as rows_inserted
FROM pg_tables pt
JOIN pg_class c ON c.relname = pt.tablename
JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = pt.schemaname
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check 9: Test critical functions
SELECT '🧪 FUNCTION TESTING' as check_type;

-- Test get_user_role function (should return NULL when not authenticated)
SELECT 'get_user_role()' as function_name, 
       CASE 
           WHEN get_user_role() IS NULL THEN '✅ WORKING'
           ELSE '⚠️ UNEXPECTED RESULT'
       END as test_result;

-- Test is_admin function
SELECT 'is_admin()' as function_name,
       CASE 
           WHEN is_admin() = false THEN '✅ WORKING'
           ELSE '⚠️ UNEXPECTED RESULT'
       END as test_result;

-- Test order number generation
SELECT 'generate_order_number()' as function_name,
       CASE 
           WHEN generate_order_number() LIKE 'YO%' THEN '✅ WORKING'
           ELSE '❌ FAILED'
       END as test_result;

-- =============================================
-- SECURITY VERIFICATION
-- =============================================

SELECT '🔐 SECURITY VERIFICATION' as check_type;

-- Check 10: Verify RLS policies exist
SELECT '🛡️ RLS POLICY VERIFICATION' as check_type;
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    permissive,
    CASE 
        WHEN qual IS NOT NULL THEN '✅ HAS CONDITIONS'
        ELSE '⚠️ NO CONDITIONS'
    END as policy_status
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, cmd, policyname;

-- Check 11: Verify user permissions on functions
SELECT '👥 FUNCTION PERMISSIONS' as check_type;
SELECT 
    p.proname as function_name,
    array_to_string(p.proacl, ', ') as permissions
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proname IN ('get_user_role', 'is_admin', 'is_manager_or_admin', 'get_orders_for_user')
ORDER BY p.proname;

-- =============================================
-- FINAL VERIFICATION SUMMARY
-- =============================================

SELECT '📊 DEPLOYMENT VERIFICATION SUMMARY' as summary_type;

-- Run the built-in schema integrity check
SELECT * FROM verify_schema_integrity();

-- Final statistics
SELECT 
    'DEPLOYMENT STATISTICS' as info,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE') as total_tables,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public' AND routine_type = 'FUNCTION') as total_functions,
    (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public') as total_indexes,
    (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema = 'public') as total_triggers,
    (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public') as total_policies;

-- Success message
SELECT '🎉 POST-DEPLOYMENT VERIFICATION COMPLETED!' as status;
SELECT 'Database schema deployed successfully by Alindevx00x' as developer_info;
SELECT 'GitHub: https://github.com/alindevx00x' as github_profile;

-- =============================================
-- TROUBLESHOOTING QUERIES
-- =============================================

-- If any issues are found, use these queries for troubleshooting:

-- Check for missing tables
/*
SELECT 'Missing Tables' as issue_type;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
AND table_name NOT IN (
    'users', 'user_profiles', 'branches', 'categories', 'products',
    'orders', 'order_items', 'order_tracking', 'customer_profiles',
    'wishlists', 'product_reviews', 'stock_movements', 'branch_inventory',
    'promo_codes', 'company_settings', 'system_configs', 'notifications',
    'email_queue', 'audit_logs', 'security_events', 'password_reset_tokens',
    'analytics_events', 'customer_behavior'
);
*/

-- Check for failed constraints
/*
SELECT 'Constraint Violations' as issue_type;
SELECT conname, conrelid::regclass, pg_get_constraintdef(oid)
FROM pg_constraint
WHERE NOT convalidated;
*/

-- Check for missing indexes
/*
SELECT 'Missing Indexes' as issue_type;
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
AND n_distinct > 100
AND attname NOT IN (
    SELECT indexname FROM pg_indexes WHERE schemaname = 'public'
);
*/
