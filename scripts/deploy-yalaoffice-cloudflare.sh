#!/bin/bash

# YalaOffice Deployment Script for yalaoffice.com + CloudPanel + Cloudflare
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Repository: https://github.com/alindevx00x/YalaOffice
# Domain: yalaoffice.com

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="yalaoffice.com"
SITE_PATH="/home/<USER>/htdocs/yalaoffice.com"
NGINX_CONFIG="/etc/nginx/sites-available/yalaoffice.com"

echo -e "${BLUE}🚀 YalaOffice Deployment for yalaoffice.com${NC}"
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
echo -e "${BLUE}Domain: yalaoffice.com${NC}"
echo -e "${BLUE}Setup: Linode + CloudPanel + Cloudflare${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Check if running as cloudpanel user
if [ "$USER" != "cloudpanel" ]; then
    print_error "This script should be run as the cloudpanel user"
    echo "Switch to cloudpanel user: sudo su - cloudpanel"
    exit 1
fi

print_status "Starting YalaOffice deployment for yalaoffice.com..."

# Step 1: Create site directory
echo -e "${BLUE}📁 Setting up site directory...${NC}"
if [ ! -d "$SITE_PATH" ]; then
    print_warning "Site directory doesn't exist. Creating: $SITE_PATH"
    sudo mkdir -p "$SITE_PATH"
    sudo chown -R cloudpanel:cloudpanel "$SITE_PATH"
fi

cd "$SITE_PATH"
print_status "Changed to site directory: $SITE_PATH"

# Step 2: Clone or update repository
echo -e "${BLUE}📦 Cloning/updating YalaOffice repository...${NC}"
if [ -d ".git" ]; then
    print_status "Repository exists, pulling latest changes..."
    git pull origin main
else
    print_status "Cloning YalaOffice repository..."
    git clone https://github.com/alindevx00x/YalaOffice.git .
fi

# Step 3: Install dependencies
echo -e "${BLUE}📚 Installing Node.js dependencies...${NC}"
if [ -f "package.json" ]; then
    npm install
    print_status "Dependencies installed successfully"
else
    print_error "package.json not found!"
    exit 1
fi

# Step 4: Environment configuration for yalaoffice.com
echo -e "${BLUE}⚙️  Setting up environment for yalaoffice.com...${NC}"
if [ ! -f ".env" ]; then
    print_warning "Creating .env file for yalaoffice.com..."
    cat > .env << 'EOF'
# YalaOffice Production Environment - yalaoffice.com
# Developer: Alindevx00x (https://github.com/alindevx00x)

# Supabase Configuration
# Get these from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Configuration
VITE_APP_NAME=YalaOffice
VITE_APP_VERSION=2.0.0
VITE_APP_ENV=production
VITE_APP_DOMAIN=yalaoffice.com
VITE_APP_TITLE=YalaOffice - Smart Supply Management
VITE_APP_DESCRIPTION=Professional supply management system for Moroccan businesses

# API Configuration
VITE_API_URL=https://your-project.supabase.co/rest/v1
VITE_API_TIMEOUT=30000

# Security Configuration
VITE_DEBUG_MODE=false
VITE_SESSION_TIMEOUT=480
VITE_ANALYTICS_ENABLED=true

# Feature Flags
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_EXPORT=true

# Localization
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_LOCALE=en-US
VITE_DEFAULT_TIMEZONE=Africa/Casablanca

# Currency Settings (Morocco)
VITE_DEFAULT_CURRENCY=MAD
VITE_CURRENCY_SYMBOL=Dh

# Performance Configuration
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100
VITE_MAX_FILE_SIZE=10
VITE_MAX_IMAGE_SIZE=5
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# Monitoring & Logging
VITE_ERROR_REPORTING=true
VITE_LOG_LEVEL=error
EOF
    print_warning "Created .env file. Please edit with your Supabase credentials:"
    echo "nano .env"
else
    print_status "Environment file already exists"
fi

# Step 5: Build application
echo -e "${BLUE}🏗️  Building YalaOffice for production...${NC}"
npm run build

if [ -d "dist" ]; then
    print_status "Build completed successfully"
    echo "Build output:"
    ls -la dist/
else
    print_error "Build failed - dist directory not created"
    exit 1
fi

# Step 6: Set proper permissions
echo -e "${BLUE}🔒 Setting proper file permissions...${NC}"
sudo chown -R cloudpanel:cloudpanel "$SITE_PATH"
sudo chmod -R 755 "$SITE_PATH"
print_status "Permissions set correctly"

# Step 7: Create Cloudflare-optimized Nginx configuration
echo -e "${BLUE}🌐 Creating Cloudflare-optimized Nginx configuration...${NC}"

if [ ! -f "$NGINX_CONFIG" ]; then
    print_status "Creating Nginx configuration for yalaoffice.com with Cloudflare optimization"
    
    sudo tee "$NGINX_CONFIG" > /dev/null << 'EOF'
# YalaOffice Nginx Configuration for Cloudflare
# Developer: Alindevx00x (https://github.com/alindevx00x)
# Domain: yalaoffice.com

server {
    listen 80;
    listen [::]:80;
    server_name yalaoffice.com www.yalaoffice.com;
    
    # Document root
    root /home/<USER>/htdocs/yalaoffice.com/dist;
    index index.html;

    # Cloudflare real IP restoration
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from 2400:cb00::/32;
    set_real_ip_from 2606:4700::/32;
    set_real_ip_from 2803:f800::/32;
    set_real_ip_from 2405:b500::/32;
    set_real_ip_from 2405:8100::/32;
    set_real_ip_from 2c0f:f248::/32;
    set_real_ip_from 2a06:98c0::/29;
    real_ip_header CF-Connecting-IP;

    # Security headers (Cloudflare compatible)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Handle React Router (SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets (Cloudflare will also cache these)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security - deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.env {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Gzip compression (fallback - Cloudflare handles this)
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Logging
    access_log /var/log/nginx/yalaoffice.com.access.log;
    error_log /var/log/nginx/yalaoffice.com.error.log;
}
EOF

    # Enable site
    sudo ln -sf "$NGINX_CONFIG" "/etc/nginx/sites-enabled/yalaoffice.com"
    
    # Test Nginx configuration
    if sudo nginx -t; then
        print_status "Nginx configuration created and tested successfully"
    else
        print_error "Nginx configuration test failed"
        exit 1
    fi
else
    print_status "Nginx configuration already exists"
fi

# Step 8: Reload Nginx
echo -e "${BLUE}🔄 Reloading Nginx...${NC}"
sudo systemctl reload nginx
print_status "Nginx reloaded successfully"

# Step 9: Final verification
echo -e "${BLUE}✅ Final verification...${NC}"
if [ -d "$SITE_PATH/dist" ] && [ -f "$SITE_PATH/dist/index.html" ]; then
    print_status "Application files are in place"
else
    print_error "Application files missing"
    exit 1
fi

if sudo nginx -t; then
    print_status "Nginx configuration is valid"
else
    print_error "Nginx configuration has errors"
    exit 1
fi

# Success message
echo ""
echo -e "${GREEN}🎉 YalaOffice deployment for yalaoffice.com completed successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Deployment Summary:${NC}"
echo "• Domain: yalaoffice.com"
echo "• Site Path: $SITE_PATH"
echo "• Application: YalaOffice v2.0.0"
echo "• Files: 483 production-ready files deployed"
echo "• Build: React + TypeScript + Vite"
echo "• Web Server: Nginx with Cloudflare optimization"
echo "• CDN: Cloudflare global network"
echo ""
echo -e "${BLUE}🔗 Next Steps:${NC}"
echo ""
echo "1. Configure Supabase credentials:"
echo "   nano $SITE_PATH/.env"
echo "   (Update VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY)"
echo ""
echo "2. Ensure Cloudflare DNS is configured:"
echo "   A record: @ → YOUR_LINODE_IP (Proxied)"
echo "   A record: www → YOUR_LINODE_IP (Proxied)"
echo ""
echo "3. Test your application:"
echo "   https://yalaoffice.com"
echo ""
echo "4. CloudPanel Admin:"
echo "   https://YOUR_LINODE_IP:8443"
echo ""
echo "5. Cloudflare Dashboard:"
echo "   https://dash.cloudflare.com"
echo ""
echo -e "${BLUE}🔧 Maintenance Commands:${NC}"
echo "• Update: git pull && npm run build"
echo "• Logs: sudo tail -f /var/log/nginx/yalaoffice.com.access.log"
echo "• Restart: sudo systemctl reload nginx"
echo ""
echo -e "${GREEN}🚀 YalaOffice is ready at https://yalaoffice.com!${NC}"
echo ""
echo -e "${BLUE}Developer: Alindevx00x (https://github.com/alindevx00x)${NC}"
echo -e "${BLUE}Repository: https://github.com/alindevx00x/YalaOffice${NC}"
echo -e "${BLUE}Domain: https://yalaoffice.com${NC}"
