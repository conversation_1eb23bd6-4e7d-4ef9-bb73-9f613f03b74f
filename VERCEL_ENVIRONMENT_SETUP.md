# Vercel Environment Variables Setup for YalaOffice

## 🔍 **Root Cause of the 404 Error**

The `/reset-password` route is returning a 404 error because **your environment variables are not configured in Vercel**. The application fails to initialize properly without the required Supabase credentials, causing the entire React app to fail and resulting in a 404 error.

## 📋 **Required Environment Variables**

Based on your `.env.production` file, here are the **REQUIRED** environment variables for Vercel:

### **Critical Variables (Must Have)**
```
VITE_SUPABASE_URL=https://umzikqwughlzkiarldoa.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY
```

### **Recommended Variables (For Full Functionality)**
```
VITE_APP_NAME=YalaOffice
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Smart Supply Management System for Moroccan Businesses
VITE_API_BASE_URL=https://api.yalaoffice.com
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_DEBUG=false
VITE_DEVTOOLS=false
VITE_MOCK_API=false
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_REPORTS=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_MOBILE_APP=true
VITE_DEFAULT_LANGUAGE=en
VITE_DEFAULT_CURRENCY=MAD
VITE_DEFAULT_TIMEZONE=Africa/Casablanca
VITE_SESSION_TIMEOUT=86400
VITE_MAX_LOGIN_ATTEMPTS=5
VITE_LOCKOUT_DURATION=15
VITE_MONITORING_ENABLED=false
VITE_LOG_LEVEL=info
```

## 🚀 **Step-by-Step Vercel Configuration**

### **Method 1: Via Vercel Dashboard (Recommended)**

1. **Go to your Vercel Dashboard**
   - Visit: https://vercel.com/dashboard
   - Find your YalaOffice project

2. **Navigate to Environment Variables**
   - Click on your project
   - Go to "Settings" tab
   - Click "Environment Variables" in the sidebar

3. **Add Required Variables**
   - Click "Add New" for each variable
   - **Environment**: Select "Production" (and optionally "Preview" and "Development")
   - Add each variable from the list above

4. **Redeploy**
   - Go to "Deployments" tab
   - Click the three dots on the latest deployment
   - Click "Redeploy"

### **Method 2: Via Vercel CLI**

```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
vercel env add VITE_SUPABASE_URL production
# Enter: https://umzikqwughlzkiarldoa.supabase.co

vercel env add VITE_SUPABASE_ANON_KEY production
# Enter: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY

# Add other variables as needed...

# Redeploy
vercel --prod
```

## 🔧 **Quick Fix Script**

I'll create a script to help you set all variables at once:

```bash
#!/bin/bash
# Save this as setup-vercel-env.sh and run it

echo "Setting up Vercel environment variables for YalaOffice..."

# Critical variables
vercel env add VITE_SUPABASE_URL production
vercel env add VITE_SUPABASE_ANON_KEY production

# App configuration
vercel env add VITE_APP_NAME production
vercel env add VITE_APP_VERSION production
vercel env add VITE_APP_DESCRIPTION production

echo "Environment variables setup complete!"
echo "Now run: vercel --prod"
```

## ✅ **Verification Steps**

After setting up the environment variables:

1. **Check Build Logs**
   - Go to Vercel Dashboard → Your Project → Deployments
   - Click on the latest deployment
   - Check the "Build Logs" for any errors

2. **Test the Application**
   - Visit https://yalaoffice.com
   - Try the password reset flow again
   - The `/reset-password` route should now work

3. **Check Browser Console**
   - Open Developer Tools (F12)
   - Look for any JavaScript errors
   - Environment validation should pass

## 🐛 **Troubleshooting**

### **If you still get 404 errors:**

1. **Check Build Success**
   ```bash
   # Verify build works locally with production env
   npm run build
   npm run preview
   ```

2. **Verify vercel.json is deployed**
   - Check that `vercel.json` exists in your repository
   - Ensure it was committed and pushed

3. **Clear Vercel Cache**
   - In Vercel Dashboard → Settings → Functions
   - Clear all caches and redeploy

### **If environment variables aren't working:**

1. **Check Variable Names**
   - Ensure they start with `VITE_` for client-side access
   - No typos in variable names

2. **Check Environment Scope**
   - Variables must be set for "Production" environment
   - Consider setting for "Preview" and "Development" too

## 🔒 **Security Notes**

- ✅ The `VITE_SUPABASE_ANON_KEY` is safe to expose (it's designed for client-side use)
- ✅ Never expose `SUPABASE_SERVICE_KEY` in client-side environment variables
- ✅ All `VITE_` prefixed variables are bundled into the client-side code

## 📞 **Next Steps**

1. Set up the environment variables in Vercel (focus on the critical ones first)
2. Redeploy your application
3. Test the password reset functionality
4. If issues persist, check the build logs and let me know the specific errors

The password reset should work perfectly once the environment variables are properly configured!
