#!/bin/bash

# YalaOffice Vercel Environment Variables Setup Script
# This script helps you quickly set up all required environment variables in Vercel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${BLUE}"
echo "=================================================="
echo "  YalaOffice Vercel Environment Setup"
echo "  Setting up environment variables for production"
echo "=================================================="
echo -e "${NC}"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI is not installed!"
    print_info "Install it with: npm install -g vercel"
    exit 1
fi

print_status "Vercel CLI found"

# Check if user is logged in
if ! vercel whoami &> /dev/null; then
    print_warning "You need to login to Vercel first"
    print_info "Running: vercel login"
    vercel login
fi

print_status "Logged in to Vercel"

# Set critical environment variables
print_info "Setting up critical environment variables..."

echo ""
print_info "Setting VITE_SUPABASE_URL..."
echo "Enter your Supabase URL (https://umzikqwughlzkiarldoa.supabase.co):"
vercel env add VITE_SUPABASE_URL production

echo ""
print_info "Setting VITE_SUPABASE_ANON_KEY..."
echo "Enter your Supabase Anon Key:"
vercel env add VITE_SUPABASE_ANON_KEY production

# Set application configuration
print_info "Setting up application configuration..."

echo ""
print_info "Setting VITE_APP_NAME..."
echo "YalaOffice" | vercel env add VITE_APP_NAME production

echo ""
print_info "Setting VITE_APP_VERSION..."
echo "1.0.0" | vercel env add VITE_APP_VERSION production

echo ""
print_info "Setting VITE_APP_DESCRIPTION..."
echo "Smart Supply Management System for Moroccan Businesses" | vercel env add VITE_APP_DESCRIPTION production

# Set API configuration
print_info "Setting up API configuration..."

echo ""
print_info "Setting VITE_API_TIMEOUT..."
echo "30000" | vercel env add VITE_API_TIMEOUT production

echo ""
print_info "Setting VITE_API_RETRY_ATTEMPTS..."
echo "3" | vercel env add VITE_API_RETRY_ATTEMPTS production

# Set feature flags
print_info "Setting up feature flags..."

echo ""
print_info "Setting VITE_FEATURE_ANALYTICS..."
echo "true" | vercel env add VITE_FEATURE_ANALYTICS production

echo ""
print_info "Setting VITE_FEATURE_REPORTS..."
echo "true" | vercel env add VITE_FEATURE_REPORTS production

echo ""
print_info "Setting VITE_FEATURE_NOTIFICATIONS..."
echo "true" | vercel env add VITE_FEATURE_NOTIFICATIONS production

echo ""
print_info "Setting VITE_FEATURE_MOBILE_APP..."
echo "true" | vercel env add VITE_FEATURE_MOBILE_APP production

# Set localization
print_info "Setting up localization..."

echo ""
print_info "Setting VITE_DEFAULT_LANGUAGE..."
echo "en" | vercel env add VITE_DEFAULT_LANGUAGE production

echo ""
print_info "Setting VITE_DEFAULT_CURRENCY..."
echo "MAD" | vercel env add VITE_DEFAULT_CURRENCY production

echo ""
print_info "Setting VITE_DEFAULT_TIMEZONE..."
echo "Africa/Casablanca" | vercel env add VITE_DEFAULT_TIMEZONE production

# Set development flags (disabled for production)
print_info "Setting up development flags..."

echo ""
print_info "Setting VITE_DEBUG..."
echo "false" | vercel env add VITE_DEBUG production

echo ""
print_info "Setting VITE_DEVTOOLS..."
echo "false" | vercel env add VITE_DEVTOOLS production

echo ""
print_info "Setting VITE_MOCK_API..."
echo "false" | vercel env add VITE_MOCK_API production

echo ""
print_status "All environment variables have been set!"

echo ""
print_warning "Next steps:"
echo "1. Run 'vercel --prod' to redeploy with new environment variables"
echo "2. Test your password reset functionality at https://yalaoffice.com"
echo "3. Check the deployment logs if you encounter any issues"

echo ""
print_info "To redeploy now, run:"
echo "vercel --prod"
